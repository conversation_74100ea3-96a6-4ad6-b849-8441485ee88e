(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-workInjuryRecognition-add","pages_lifeCycle-pages-Appointment-Appointment~pages_lifeCycle-pages-recoveredServices-addrecoveredSe~3a68e2df","pages-workInjuryRecognition-detail~pages_user-pages-user-checkDetail"],{"003c":function(e,t,n){"use strict";n.r(t);var r=n("5b6d"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"01fc":function(e,t,n){"use strict";n.r(t);var r=n("640e"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"022e":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{ref:"u-row",staticClass:"u-row",style:[e.rowStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[e._t("default")],2)},a=[]},"0322":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uniNavBar:n("d23e").default,uTabs:n("c359").default,"u-Form":n("c3e3").default,uFormItem:n("49ce").default,"u-Input":n("fea2").default,uRadioGroup:n("dfd5").default,uRadio:n("06c9").default,uniDatetimePicker:n("d374").default,"u-Textarea":n("a8fd").default,uButton:n("7a42").default,uRow:n("6ef1").default,uCol:n("b40c").default,uniFilePicker:n("cb61").default,"u-Text":n("fe00").default},a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"add"},[r("uni-nav-bar",{attrs:{leftWidth:"180rpx",fixed:!0,"background-color":"#007AFF",border:"{false}"},on:{clickLeft:function(t){arguments[0]=t=e.$handleEvent(t),e.back.apply(void 0,arguments)}}},[r("template",{attrs:{slot:"left"},slot:"left"},[r("v-uni-view",{staticClass:"nav-left"},[r("v-uni-image",{attrs:{src:n("108a"),mode:""}}),e._v("工伤认定")],1)],1)],2),r("v-uni-view",{staticClass:"add_body"},[r("v-uni-view",{staticClass:"add_content"},[r("u-tabs",{attrs:{list:e.tabList},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleTabChange.apply(void 0,arguments)}}}),"基本信息"===e.curTab?r("v-uni-view",{staticClass:"basicFrom"},[r("u--form",{ref:"basicForm",attrs:{labelPosition:"left",labelWidth:"auto",model:e.formData,rules:e.basicRules}},[r("u-form-item",{attrs:{label:"姓名",prop:"employee_name",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.employee_name,callback:function(t){e.$set(e.formData,"employee_name",t)},expression:"formData.employee_name"}})],1),r("u-form-item",{attrs:{label:"性别",prop:"gender",borderBottom:!0}},[r("u-radio-group",{attrs:{placement:"row"},model:{value:e.formData.gender,callback:function(t){e.$set(e.formData,"gender",t)},expression:"formData.gender"}},[r("u-radio",{attrs:{label:"男",name:"男"}}),r("u-radio",{attrs:{label:"女",name:"女"}})],1)],1),r("u-form-item",{attrs:{label:"出生日期",prop:"birthday",borderBottom:!0}},[r("uni-datetime-picker",{attrs:{type:"date",border:!1},model:{value:e.formData.birthday,callback:function(t){e.$set(e.formData,"birthday",t)},expression:"formData.birthday"}})],1),r("u-form-item",{attrs:{label:"身份证号码",prop:"id_code",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.id_code,callback:function(t){e.$set(e.formData,"id_code",t)},expression:"formData.id_code"}})],1),r("u-form-item",{attrs:{label:"联系电话",prop:"employee_phone",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.employee_phone,callback:function(t){e.$set(e.formData,"employee_phone",t)},expression:"formData.employee_phone"}})],1),r("u-form-item",{attrs:{label:"家庭地址",prop:"employee_address",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.employee_address,callback:function(t){e.$set(e.formData,"employee_address",t)},expression:"formData.employee_address"}})],1),r("u-form-item",{attrs:{label:"邮政编码",prop:"employee_postcode",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.employee_postcode,callback:function(t){e.$set(e.formData,"employee_postcode",t)},expression:"formData.employee_postcode"}})],1),r("u-form-item",{attrs:{label:"所在单位名称",prop:"enterprise_name",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.enterprise_name,callback:function(t){e.$set(e.formData,"enterprise_name",t)},expression:"formData.enterprise_name"}})],1),r("u-form-item",{attrs:{label:"单位联系电话",prop:"enterprise_phone",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.enterprise_phone,callback:function(t){e.$set(e.formData,"enterprise_phone",t)},expression:"formData.enterprise_phone"}})],1),r("u-form-item",{attrs:{label:"单位地址",prop:"enterprise_address",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.enterprise_address,callback:function(t){e.$set(e.formData,"enterprise_address",t)},expression:"formData.enterprise_address"}})],1),r("u-form-item",{attrs:{label:"单位邮政编码",prop:"enterprise_postcode",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.enterprise_postcode,callback:function(t){e.$set(e.formData,"enterprise_postcode",t)},expression:"formData.enterprise_postcode"}})],1),r("u-form-item",{attrs:{label:"职业、工种或工作岗位",prop:"enterprise_postcode",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.employee_job,callback:function(t){e.$set(e.formData,"employee_job",t)},expression:"formData.employee_job"}})],1),r("u-form-item",{attrs:{label:"参加工作时间",prop:"employee_work_date",borderBottom:!0}},[r("uni-datetime-picker",{attrs:{type:"date",placeholder:"请选择",border:!1},model:{value:e.formData.employee_work_date,callback:function(t){e.$set(e.formData,"employee_work_date",t)},expression:"formData.employee_work_date"}})],1),r("u-form-item",{attrs:{label:"事故时间",prop:"accident_time",borderBottom:!0}},[r("uni-datetime-picker",{attrs:{type:"date",placeholder:"请选择",border:!1},model:{value:e.formData.accident_time,callback:function(t){e.$set(e.formData,"accident_time",t)},expression:"formData.accident_time"}})],1),r("u-form-item",{attrs:{label:"事故地点",prop:"accident_place",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.accident_place,callback:function(t){e.$set(e.formData,"accident_place",t)},expression:"formData.accident_place"}})],1),r("u-form-item",{attrs:{label:"事故主要原因",prop:"main_reason",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.main_reason,callback:function(t){e.$set(e.formData,"main_reason",t)},expression:"formData.main_reason"}})],1),r("u-form-item",{attrs:{label:"诊断时间",prop:"diagnosis_time",borderBottom:!0}},[r("uni-datetime-picker",{attrs:{type:"date",placeholder:"请选择",border:!1},model:{value:e.formData.diagnosis_time,callback:function(t){e.$set(e.formData,"diagnosis_time",t)},expression:"formData.diagnosis_time"}})],1),r("u-form-item",{attrs:{label:"受伤害部位",prop:"injury_part",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.injury_part,callback:function(t){e.$set(e.formData,"injury_part",t)},expression:"formData.injury_part"}})],1),r("u-form-item",{attrs:{label:"职业病名称",prop:"disease_name",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.disease_name,callback:function(t){e.$set(e.formData,"disease_name",t)},expression:"formData.disease_name"}})],1),r("u-form-item",{attrs:{label:"接触职业病危害岗位",prop:"exposure_post",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.exposure_post,callback:function(t){e.$set(e.formData,"exposure_post",t)},expression:"formData.exposure_post"}})],1),r("u-form-item",{attrs:{label:"接触职业病危害时间",prop:"exposure_time",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.exposure_time,callback:function(t){e.$set(e.formData,"exposure_time",t)},expression:"formData.exposure_time"}})],1),r("u-form-item",{attrs:{label:"受伤害经过简述",prop:"description",borderBottom:!0}},[r("u--textarea",{attrs:{placeholder:"请输入内容",border:"bottom"},model:{value:e.formData.description,callback:function(t){e.$set(e.formData,"description",t)},expression:"formData.description"}})],1),r("u-form-item",{attrs:{label:"申请事项",prop:"apply_matter",borderBottom:!0}},[r("u--input",{attrs:{placeholder:"请输入",border:"none"},model:{value:e.formData.apply_matter,callback:function(t){e.$set(e.formData,"apply_matter",t)},expression:"formData.apply_matter"}})],1)],1),r("v-uni-view",{staticClass:"operator"},[r("u-button",{attrs:{size:"small"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCancel.apply(void 0,arguments)}}},[e._v("取消")]),r("u-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitBasicForm.apply(void 0,arguments)}}},[e._v("暂存")]),r("u-button",{directives:[{name:"show",rawName:"v-show",value:e.isSubmit,expression:"isSubmit"}],attrs:{type:"success",size:"small"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitBasicForm("提交")}}},[e._v("保存并上报")])],1)],1):"工伤附件"===e.curTab?r("v-uni-view",{staticClass:"annexFile"},[r("u--form",{ref:"annexFileForm",attrs:{labelPosition:"top",labelWidth:"auto",model:e.formData,rules:e.annxeFileRules}},[r("u-row",{attrs:{customStyle:"margin-bottom: 10px"}},[r("u-col",{attrs:{span:"7"}},[r("u-form-item",{attrs:{label:"工伤申请认定表",prop:"annex_application",borderBottom:!0}},[r("uni-file-picker",{ref:"annex_application",attrs:{fileMediatype:"all","auto-upload":!1},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUAASelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),function(t){e.handleDeleteFile(t,"annex_application")}.apply(void 0,arguments)}},model:{value:e.annexFile.annex_application,callback:function(t){e.$set(e.annexFile,"annex_application",t)},expression:"annexFile.annex_application"}})],1)],1),r("u-col",{attrs:{span:"5"}},[r("u--text",{attrs:{type:"primary",text:"下载工伤认定申请模版"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleDownloadWorkInjuryFile(e.formData._id,"annex_application")}}})],1)],1),r("u-row",{attrs:{customStyle:"margin-bottom: 10px"}},[r("u-col",{attrs:{span:"7"}},[r("u-form-item",{attrs:{label:"劳动关系证明",prop:"annex_labor_contract",borderBottom:!0}},[r("uni-file-picker",{ref:"annex_labor_contract",attrs:{fileMediatype:"all","auto-upload":!1},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUALCSelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),function(t){e.handleDeleteFile(t,"annex_labor_contract")}.apply(void 0,arguments)}},model:{value:e.annexFile.annex_labor_contract,callback:function(t){e.$set(e.annexFile,"annex_labor_contract",t)},expression:"annexFile.annex_labor_contract"}})],1)],1),r("u-col",{attrs:{span:"5"}},[r("u--text",{attrs:{type:"primary",text:"下载劳动关系证明模版"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleDownloadWorkInjuryFile(e.formData._id,"annex_labor_contract")}}})],1)],1),r("u-row",{attrs:{customStyle:"margin-bottom: 10px"}},[r("u-col",{attrs:{span:"7"}},[r("u-form-item",{attrs:{label:"诊断证明书",prop:"annex_certificate",borderBottom:!0}},[r("uni-file-picker",{ref:"annex_certificate",attrs:{fileMediatype:"all","auto-upload":!1},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUACSelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),function(t){e.handleDeleteFile(t,"annex_certificate")}.apply(void 0,arguments)}},model:{value:e.annexFile.annex_certificate,callback:function(t){e.$set(e.annexFile,"annex_certificate",t)},expression:"annexFile.annex_certificate"}})],1)],1),r("u-col",{attrs:{span:"5"}})],1),r("u-row",{attrs:{customStyle:"margin-bottom: 10px"}},[r("u-col",{attrs:{span:"7"}},[r("u-form-item",{attrs:{label:"身份证复件",prop:"annex_id",borderBottom:!0}},[r("uni-file-picker",{ref:"annex_id",attrs:{fileMediatype:"all","auto-upload":!1},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUAISelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),function(t){e.handleDeleteFile(t,"annex_id")}.apply(void 0,arguments)}},model:{value:e.annexFile.annex_id,callback:function(t){e.$set(e.annexFile,"annex_id",t)},expression:"annexFile.annex_id"}})],1)],1),r("u-col",{attrs:{span:"5"}})],1),r("u-row",{attrs:{customStyle:"margin-bottom: 10px"}},[r("u-col",{attrs:{span:"7"}},[r("u-form-item",{attrs:{label:"一寸照片",prop:"annex_photo",borderBottom:!0}},[r("uni-file-picker",{ref:"annex_photo",attrs:{fileMediatype:"all","auto-upload":!1},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUAPSelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),function(t){e.handleDeleteFile(t,"annex_photo")}.apply(void 0,arguments)}},model:{value:e.annexFile.annex_photo,callback:function(t){e.$set(e.annexFile,"annex_photo",t)},expression:"annexFile.annex_photo"}})],1)],1),r("u-col",{attrs:{span:"5"}})],1),r("u-row",{attrs:{customStyle:"margin-bottom: 10px"}},[r("u-col",{attrs:{span:"7"}},[r("u-form-item",{attrs:{label:"调查报告",prop:"annex_investigation",borderBottom:!0}},[r("uni-file-picker",{ref:"annex_investigation",attrs:{fileMediatype:"all","auto-upload":!1},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUAInSelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),function(t){e.handleDeleteFile(t,"annex_investigation")}.apply(void 0,arguments)}},model:{value:e.annexFile.annex_investigation,callback:function(t){e.$set(e.annexFile,"annex_investigation",t)},expression:"annexFile.annex_investigation"}})],1)],1),r("u-col",{attrs:{span:"5"}},[r("u--text",{attrs:{type:"primary",text:"下载调查报告模版"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleDownloadWorkInjuryFile(e.formData._id,"annex_investigation")}}})],1)],1)],1),r("v-uni-view",{staticClass:"operator"},[r("u-button",{attrs:{size:"small"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCancel.apply(void 0,arguments)}}},[e._v("取消")]),r("u-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitAnnexForm.apply(void 0,arguments)}}},[e._v("暂存")]),r("u-button",{attrs:{type:"success",size:"small"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitAnnexForm("提交")}}},[e._v("保存并上报")])],1)],1):e._e()],1)],1)],1)},i=[]},"0463":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{gutter:{type:[String,Number],default:uni.$u.props.row.gutter},justify:{type:String,default:uni.$u.props.row.justify},align:{type:String,default:uni.$u.props.row.align}}};t.default=r},"048c":function(e,t,n){"use strict";var r=n("c451"),a=n.n(r);a.a},"06c9":function(e,t,n){"use strict";n.r(t);var r=n("a3f2"),a=n("0de7");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("b5d0");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"4dbd7d4a",null,!1,r["a"],void 0);t["default"]=s.exports},"0a17":function(e,t,n){"use strict";var r=n("22ed"),a=n.n(r);a.a},"0bdc":function(e,t,n){"use strict";var r=n("6efe"),a=n.n(r);a.a},"0bfe":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.addOrEditWorkInjuryRecognition=function(e){return(0,a.default)({url:"manage/adminorg/workInjuryRecognition",data:e,method:"post"})},t.uploadFile=t.getWorkInjuryRecognitionList=t.getWorkInjuryRecognitionDetail=t.downloadTemplateFile=t.deleteWorkInjuryRecognition=t.deleteFile=void 0;var a=r(n("e40e"));t.getWorkInjuryRecognitionList=function(e){return(0,a.default)({url:"manage/adminorg/workInjuryRecognition",data:e,method:"get"})};t.getWorkInjuryRecognitionDetail=function(e){return(0,a.default)({url:"manage/adminorg/workInjuryRecognitionDetail?_id="+e._id,method:"get"})};t.deleteWorkInjuryRecognition=function(e){return(0,a.default)({url:"manage/adminorg/workInjuryRecognition",method:"delete",data:e})};t.downloadTemplateFile=function(e){return(0,a.default)({url:"manage/adminorg/recognitionTemp",data:e,method:"get"})};t.uploadFile=function(e){return(0,a.default)({url:"app/file",data:e,method:"post",header:{"Content-Type":"multipart/form-data"}})};t.deleteFile=function(e){return(0,a.default)({url:"app/file?filePath="+e.filePath,method:"delete"})}},"0de7":function(e,t,n){"use strict";n.r(t);var r=n("22e3"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"0ef8":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("82ee")),i=(r(n("4918")),r(n("0464")),r(n("bb76"))),o={name:"u--text",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default,i.default],computed:{valueStyle:function(){var e={textDecoration:this.decoration,fontWeight:this.bold?"bold":"normal",wordWrap:this.wordWrap,fontSize:uni.$u.addUnit(this.size)};return!this.type&&(e.color=this.color),this.isNvue&&this.lines&&(e.lines=this.lines),this.lineHeight&&(e.lineHeight=uni.$u.addUnit(this.lineHeight)),!this.isNvue&&this.block&&(e.display="block"),uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))},isNvue:function(){return!1},isMp:function(){return!1}},data:function(){return{}},methods:{clickHandler:function(){this.call&&"phone"===this.mode&&uni.makePhoneCall({phoneNumber:this.text}),this.$emit("click")}}};t.default=o},"0fa6":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("b2ee")),i=r(n("fa4d")),o={name:"u--input",mixins:[uni.$u.mpMixin,i.default,uni.$u.mixin],components:{uvInput:a.default}};t.default=o},1008:function(e,t,n){"use strict";n.r(t);var r=n("c891"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},1052:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"uni-file-picker"},[e.title?n("v-uni-view",{staticClass:"uni-file-picker__header"},[n("v-uni-text",{staticClass:"file-title"},[e._v(e._s(e.title))]),n("v-uni-text",{staticClass:"file-count"},[e._v(e._s(e.filesList.length)+"/"+e._s(e.limitLength))])],1):e._e(),"image"===e.fileMediatype&&"grid"===e.showType?n("upload-image",{attrs:{readonly:e.readonly,"image-styles":e.imageStyles,"files-list":e.filesList,limit:e.limitLength,disablePreview:e.disablePreview,delIcon:e.delIcon},on:{uploadFiles:function(t){arguments[0]=t=e.$handleEvent(t),e.uploadFiles.apply(void 0,arguments)},choose:function(t){arguments[0]=t=e.$handleEvent(t),e.choose.apply(void 0,arguments)},delFile:function(t){arguments[0]=t=e.$handleEvent(t),e.delFile.apply(void 0,arguments)}}},[e._t("default",[n("v-uni-view",{staticClass:"icon-add"}),n("v-uni-view",{staticClass:"icon-add rotate"})])],2):e._e(),"image"!==e.fileMediatype||"grid"!==e.showType?n("upload-file",{attrs:{readonly:e.readonly,"list-styles":e.listStyles,"files-list":e.filesList,showType:e.showType,delIcon:e.delIcon},on:{uploadFiles:function(t){arguments[0]=t=e.$handleEvent(t),e.uploadFiles.apply(void 0,arguments)},choose:function(t){arguments[0]=t=e.$handleEvent(t),e.choose.apply(void 0,arguments)},delFile:function(t){arguments[0]=t=e.$handleEvent(t),e.delFile.apply(void 0,arguments)}}},[e._t("default",[n("v-uni-button",{attrs:{type:"primary",size:"mini"}},[e._v("选择文件")])])],2):e._e()],1)},a=[]},"108a":function(e,t,n){e.exports=n.p+"static/img/leftArrow.e84103a9.svg"},1479:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"uni-file-picker__container"},[e._l(e.filesList,(function(t,r){return n("v-uni-view",{key:r,staticClass:"file-picker__box",style:e.boxStyle},[n("v-uni-view",{staticClass:"file-picker__box-content",style:e.borderStyle},[n("v-uni-image",{staticClass:"file-image",attrs:{src:t.url,mode:"aspectFill"},on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.prviewImage(t,r)}}}),e.delIcon&&!e.readonly?n("v-uni-view",{staticClass:"icon-del-box",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.delFile(r)}}},[n("v-uni-view",{staticClass:"icon-del"}),n("v-uni-view",{staticClass:"icon-del rotate"})],1):e._e(),t.progress&&100!==t.progress||0===t.progress?n("v-uni-view",{staticClass:"file-picker__progress"},[n("v-uni-progress",{staticClass:"file-picker__progress-item",attrs:{percent:-1===t.progress?0:t.progress,"stroke-width":"4",backgroundColor:t.errMsg?"#ff5a5f":"#EBEBEB"}})],1):e._e(),t.errMsg?n("v-uni-view",{staticClass:"file-picker__mask",on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.uploadFiles(t,r)}}},[e._v("点击重试")]):e._e()],1)],1)})),e.filesList.length<e.limit&&!e.readonly?n("v-uni-view",{staticClass:"file-picker__box",style:e.boxStyle},[n("v-uni-view",{staticClass:"file-picker__box-content is-add",style:e.borderStyle,on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.choose.apply(void 0,arguments)}}},[e._t("default")],2)],1):e._e()],2)},a=[]},"1b01":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"uni-status-bar",style:{height:this.statusBarHeight}},[this._t("default")],2)},a=[]},"1b1e":function(e,t,n){"use strict";n.r(t);var r=n("73dcb"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"20f3":function(e,t,n){"use strict";var r=n("8bdb"),a=n("5145");r({target:"Array",proto:!0,forced:a!==[].lastIndexOf},{lastIndexOf:a})},2230:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uIcon:n("165f").default,uLink:n("8732").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.show?n("v-uni-view",{staticClass:"u-text",class:[],style:{margin:e.margin,justifyContent:"left"===e.align?"flex-start":"center"===e.align?"center":"flex-end"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},["price"===e.mode?n("v-uni-text",{class:["u-text__price",e.type&&"u-text__value--"+e.type],style:[e.valueStyle]},[e._v("￥")]):e._e(),e.prefixIcon?n("v-uni-view",{staticClass:"u-text__prefix-icon"},[n("u-icon",{attrs:{name:e.prefixIcon,customStyle:e.$u.addStyle(e.iconStyle)}})],1):e._e(),"link"===e.mode?n("u-link",{attrs:{text:e.value,href:e.href,underLine:!0}}):e.openType&&e.isMp?[n("v-uni-button",{staticClass:"u-reset-button u-text__value",style:[e.valueStyle],attrs:{"data-index":e.index,openType:e.openType,lang:e.lang,"session-from":e.sessionFrom,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"app-parameter":e.appParameter},on:{getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.onGetUserInfo.apply(void 0,arguments)},contact:function(t){arguments[0]=t=e.$handleEvent(t),e.onContact.apply(void 0,arguments)},getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.onGetPhoneNumber.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.onError.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.onLaunchApp.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.onOpenSetting.apply(void 0,arguments)}}},[e._v(e._s(e.value))])]:n("v-uni-text",{staticClass:"u-text__value",class:[e.type&&"u-text__value--"+e.type,e.lines&&"u-line-"+e.lines],style:[e.valueStyle]},[e._v(e._s(e.value))]),e.suffixIcon?n("v-uni-view",{staticClass:"u-text__suffix-icon"},[n("u-icon",{attrs:{name:e.suffixIcon,customStyle:e.$u.addStyle(e.iconStyle)}})],1):e._e()],2):e._e()},i=[]},"22e3":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("aa9c");var a=r(n("b98a")),i={name:"u-radio",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{checked:!1,parentData:{iconSize:12,labelDisabled:null,disabled:null,shape:null,activeColor:null,inactiveColor:null,size:18,value:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}},computed:{elDisabled:function(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize:function(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize:function(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor:function(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor:function(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor:function(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape:function(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize:function(){return uni.$u.addUnit(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor:function(){var e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.checked?this.elInactiveColor:"transparent":this.checked?e:"transparent"},iconClasses:function(){var e=[];return e.push("u-radio__icon-wrap--"+this.elShape),this.elDisabled&&e.push("u-radio__icon-wrap--disabled"),this.checked&&this.elDisabled&&e.push("u-radio__icon-wrap--disabled--checked"),e},iconWrapStyle:function(){var e={};return e.backgroundColor=this.checked&&!this.elDisabled?this.elActiveColor:"#ffffff",e.borderColor=this.checked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,e.width=uni.$u.addUnit(this.elSize),e.height=uni.$u.addUnit(this.elSize),"right"===this.parentData.iconPlacement&&(e.marginRight=0),e},radioStyle:function(){var e={};return this.parentData.borderBottom&&"row"===this.parentData.placement&&uni.$u.error("检测到您将borderBottom设置为true，需要同时将u-radio-group的placement设置为column才有效"),this.parentData.borderBottom&&"column"===this.parentData.placement&&(e.paddingBottom="ios"===uni.$u.os()?"12px":"8px"),uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))}},mounted:function(){this.init()},methods:{init:function(){this.updateParentData(),this.parent||uni.$u.error("u-radio必须搭配u-radio-group组件使用"),this.checked=this.name===this.parentData.value},updateParentData:function(){this.getParentData("u-radio-group")},iconClickHandler:function(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},wrapperClickHandler:function(e){"right"===this.parentData.iconPlacement&&this.iconClickHandler(e)},labelClickHandler:function(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent:function(){var e=this;this.checked||(this.$emit("change",this.name),this.$nextTick((function(){uni.$u.formValidate(e,"change")})))},setRadioCheckedStatus:function(){this.emitEvent(),this.checked=!0,"function"===typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}}};t.default=i},"22ed":function(e,t,n){var r=n("6ae7");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("d496d280",r,!0,{sourceMap:!1,shadowMode:!1})},"23c8":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("fd3c");var a=r(n("5cb8")),i={name:"u-radio-group",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],computed:{parentData:function(){return[this.value,this.disabled,this.inactiveColor,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.borderBottom,this.placement]},bemClass:function(){return this.bem("radio-group",["placement"])}},watch:{parentData:function(){this.children.length&&this.children.map((function(e){"function"===typeof e.init&&e.init()}))}},data:function(){return{}},created:function(){this.children=[]},methods:{unCheckedOther:function(e){this.children.map((function(t){e!==t&&(t.checked=!1)}));var t=e.name;this.$emit("input",t),this.$emit("change",t)}}};t.default=i},"24e5":function(e,t,n){"use strict";n.r(t);var r=n("6ad17"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"27e9":function(e,t,n){"use strict";n.r(t);var r=n("bf29"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"28d0":function(e,t,n){t.nextTick=function(e){var t=Array.prototype.slice.call(arguments);t.shift(),setTimeout((function(){e.apply(null,t)}),0)},t.platform=t.arch=t.execPath=t.title="browser",t.pid=1,t.browser=!0,t.env={},t.argv=[],t.binding=function(e){throw new Error("No such module. (Possibly not yet loaded)")},function(){var e,r="/";t.cwd=function(){return r},t.chdir=function(t){e||(e=n("a3fc")),r=e.resolve(t,r)}}(),t.exit=t.kill=t.umask=t.dlopen=t.uptime=t.memoryUsage=t.uvCounters=function(){},t.features={}},"2a68":function(e,t,n){"use strict";var r=n("b5f3"),a=n.n(r);a.a},"2bde":function(e,t,n){var r=n("9f32");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("6ccff0e0",r,!0,{sourceMap:!1,shadowMode:!1})},"2ce9":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{isDot:{type:Boolean,default:uni.$u.props.badge.isDot},value:{type:[Number,String],default:uni.$u.props.badge.value},show:{type:Boolean,default:uni.$u.props.badge.show},max:{type:[Number,String],default:uni.$u.props.badge.max},type:{type:String,default:uni.$u.props.badge.type},showZero:{type:Boolean,default:uni.$u.props.badge.showZero},bgColor:{type:[String,null],default:uni.$u.props.badge.bgColor},color:{type:[String,null],default:uni.$u.props.badge.color},shape:{type:String,default:uni.$u.props.badge.shape},numberType:{type:String,default:uni.$u.props.badge.numberType},offset:{type:Array,default:uni.$u.props.badge.offset},inverted:{type:Boolean,default:uni.$u.props.badge.inverted},absolute:{type:Boolean,default:uni.$u.props.badge.absolute}}};t.default=r},"2f8c":function(e,t,n){"use strict";n.r(t);var r=n("c821"),a=n("1008");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("ed86");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"1fecfc2e",null,!1,r["a"],void 0);t["default"]=s.exports},"2fc5":function(e,t,n){"use strict";(function(e){n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("bf0f"),n("2797"),n("dc8a"),n("d4b5"),n("fd3c"),n("4626"),n("5c47"),n("a1c1"),n("aa9c"),n("8f71"),n("bd06"),n("dc69");var a=r(n("9b1b")),i=r(n("2634")),o=r(n("2fdc")),s=r(n("20f8")),u=n("0bfe"),c=n("33f5"),l={name:"WIRAdd",data:function(){return{tabList:[{name:"基本信息"},{name:"工伤附件"}],curTab:"基本信息",formData:{employee_name:"",gender:"",birthday:"",id_code:"",employee_phone:"",employee_address:"",employee_postcode:"",enterprise_name:"",enterprise_phone:"",enterprise_address:"",enterprise_postcode:"",employee_job:"",employee_work_date:"",accident_time:"",accident_place:"",main_reason:"",diagnosis_time:"",injury_part:"",disease_name:"",exposure_post:"",exposure_time:"",description:"",apply_matter:"",annex_application:[],annex_labor_contract:[],annex_certificate:[],annex_id:[],annex_photo:[],annex_investigation:[]},basicRules:{apply_name:[{required:!0,message:"请输入申请人姓名",trigger:"blur"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}],gender:[{required:!0,message:"请选择性别",trigger:"blur"}],birthday:[{required:!0,message:"请选填写出生日期",trigger:"change"}],id_code:[{required:!0,message:"请填写身份证号",trigger:"blur"}],employee_phone:[{required:!0,message:"请填写联系电话",trigger:"blur"}],employee_address:[{required:!0,message:"请填写家庭地址",trigger:"blur"}],enterprise_name:[{required:!0,message:"请填写工作单位",trigger:"blur"}],enterprise_phone:[{required:!0,message:"请填写工作单位联系电话",trigger:"blur"}],enterprise_address:[{required:!0,message:"请填写工作单位地址",trigger:"blur"}],employee_job:[{required:!0,message:"请填写职业、工种或工作岗位",trigger:"blur"}],employee_work_date:[{required:!0,message:"请填写参加工作时间",trigger:"change"}],main_reason:[{required:!0,message:"请填写事故主要原因",trigger:"blur"}],diagnosis_time:[{required:!0,message:"请填写诊断时间",trigger:"change"}],injury_part:[{required:!0,message:"请填写受伤部位",trigger:"blur"}],disease_name:[{required:!0,message:"请填写职业病名称",trigger:"blur"}],exposure_post:[{required:!0,message:"请填写接触职业病危害岗位",trigger:"blur"}],exposure_time:[{required:!0,message:"请填写接触职业病危害时间",trigger:"change"}],description:[{required:!0,message:"请填写受伤害经过简述",trigger:"blur"}],apply_matter:[{required:!0,message:"请填写申请事项",trigger:"blur"}]},annxeFileRules:{annex_application:[{type:"array",required:!0,message:"请上传工伤认定申请书",trigger:"change"}],annex_labor_contract:[{type:"array",required:!0,message:"请上传劳动关系证明",trigger:"change"}],annex_certificate:[{type:"array",required:!0,message:"请上传诊断证明书",trigger:"change"}],annex_id:[{type:"array",required:!0,message:"请上传身份证复件",trigger:"change"}],annex_photo:[{type:"array",required:!0,message:"请上传一寸照片",trigger:"change"}]},annexFile:{annex_application:[],annex_labor_contract:[],annex_certificate:[],annex_id:[],annex_photo:[],annex_investigation:[]}}},computed:{isSubmit:function(){return this.formData.annex_application&&this.formData.annex_application.length>0}},onLoad:function(e){var t=this;return(0,o.default)((0,i.default)().mark((function n(){var r,a;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r=e._id,!r){n.next=7;break}return n.next=4,(0,u.getWorkInjuryRecognitionDetail)({_id:r});case 4:a=n.sent,t.formData=a.data,Object.keys(t.annexFile).forEach((function(e){var n=JSON.parse(JSON.stringify(t.formData[e]));(n||0!==n.length)&&(t.annexFile[e]=n.map((function(e){var t=null===e||void 0===e?void 0:e.split("/").pop();return{name:t,url:e}})))}));case 7:case"end":return n.stop()}}),n)})))()},methods:{back:function(){uni.navigateBack()},handleTabChange:function(e){var t=e.name;this.curTab=t},handleCancel:function(){uni.navigateTo({url:"/pages/workInjuryRecognition/index"})},submitBasicForm:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"暂存";switch(n){case"暂存":this.formData.status="0";break;case"提交":this.formData.status="1",e.log(2);break}this.$refs.basicForm.validate().then(function(){var e=(0,o.default)((0,i.default)().mark((function e(n){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.addOrEditWorkInjuryRecognition)((0,a.default)((0,a.default)({},t.formData),{},{apply_name:t.formData.employee_name,apply_relation:"本人"}));case 3:uni.$u.toast("申请成功"),uni.navigateTo({url:"/pages/workInjuryRecognition/index"}),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),uni.$u.toast(e.t0.message);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){uni.$u.toast("请将表单填写完整")}))},submitAnnexForm:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"暂存";switch(n){case"暂存":this.formData.status="0";break;case"提交":this.formData.status="1",e.log(2);break}this.$refs.annexFileForm.validate().then(function(){var e=(0,o.default)((0,i.default)().mark((function e(n){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.addOrEditWorkInjuryRecognition)((0,a.default)((0,a.default)({},t.formData),{},{apply_name:t.formData.employee_name,apply_relation:"本人"}));case 3:uni.$u.toast("申请成功"),uni.navigateTo({url:"/pages/workInjuryRecognition/index"}),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),uni.$u.toast(e.t0.message);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){uni.$u.toast("请将表单填写完整")}))},isFileValid:function(e){if(e.size>5242880)return!1;var t=e.name.split(".").pop().toLowerCase(),n=e.type.toLowerCase(),r=["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx"].includes(t),a=["image/jpeg","image/png","image/gif","image/bmp","image/webp","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(n);return r||a},handleDownloadWorkInjuryFile:function(t,n){return(0,o.default)((0,i.default)().mark((function r(){var a,o,l;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a={annex_application:"工伤申请认定表",annex_labor_contract:"劳动关系证明",annex_investigation:"调查报告"},r.prev=1,r.next=4,(0,u.downloadTemplateFile)({_id:t,file_category:n});case 4:o=r.sent,l=o.data,e.log(a[n]),(0,c.downloadFile)(a[n],s.default.apiServer+l.url.replace(/^\//,"")),r.next=13;break;case 10:r.prev=10,r.t0=r["catch"](1),uni.showToast({icon:"error",title:"".concat(a[n],"下载失败"),duration:3e3});case 13:case"end":return r.stop()}}),r,null,[[1,10]])})))()},handleUploadFile:function(e,t){var n=this;return(0,o.default)((0,i.default)().mark((function r(){return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:uni.uploadFile({url:s.default.apiServer+"app/file",filePath:e.url,name:"file",success:function(r){var a=JSON.parse(r.data);n.formData[t].push(a.data.url),e.url=a.data.url},fail:function(r){var a;uni.$u.toast("".concat(e.name," 上传失败")),n.annexFile[t]=n.annexFile[t].filter((function(t){return t.uuid!==e.uuid}));var i=null===(a=n.annexFile[t])||void 0===a?void 0:a.findIndex((function(t){return t.uuid===e.uuid}));-1!==i&&n.$refs[t].clearFiles(i)}});case 1:case"end":return r.stop()}}),r)})))()},handleDeleteFile:function(e,t){var n=this;return(0,o.default)((0,i.default)().mark((function r(){var a;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.tempFile,r.prev=1,r.next=4,(0,u.deleteFile)({filePath:a.url.replace(/\\/g,"/")});case 4:n.formData[t]=n.formData[t].filter((function(e){return e!==a.url})),r.next=10;break;case 7:r.prev=7,r.t0=r["catch"](1),uni.$u.toast("".concat(a.name," 删除失败"));case 10:case"end":return r.stop()}}),r,null,[[1,7]])})))()},handleUAASelect:function(t){var n=this,r=t.tempFiles,a=[];r.forEach((function(t,r){var i=t.name,o=t.url,s=t.uuid,u=t.file,c=n.isFileValid(u);e.log(c),c?n.annexFile.annex_application.push({name:i,url:o,uuid:s,file:u}):(uni.$u.toast("".concat(u.name," 文件格式不支持")),a.push(r))})),e.log("annex_application",this.annexFile.annex_application),a.reverse().forEach((function(e){n.$refs.annex_application.clearFiles(e)})),this.annexFile.annex_application.forEach((function(e){n.handleUploadFile(e,"annex_application")}))},handleUALCSelect:function(t){var n=this,r=t.tempFiles,a=[];r.forEach((function(t,r){var i=t.name,o=t.url,s=t.uuid,u=t.file,c=n.isFileValid(u);e.log(c),c?n.annexFile.annex_labor_contract.push({name:i,url:o,uuid:s,file:u}):(uni.$u.toast("".concat(u.name," 文件格式不支持")),a.push(r))})),a.reverse().forEach((function(e){n.$refs.annex_labor_contract.clearFiles(e)})),this.annexFile.annex_labor_contract.forEach((function(e){n.handleUploadFile(e,"annex_labor_contract")}))},handleUACSelect:function(t){var n=this,r=t.tempFiles,a=[];r.forEach((function(t,r){var i=t.name,o=t.url,s=t.uuid,u=t.file,c=n.isFileValid(u);e.log(c),c?n.annexFile.annex_certificate.push({name:i,url:o,uuid:s,file:u}):(uni.$u.toast("".concat(u.name," 文件格式不支持")),a.push(r))})),a.reverse().forEach((function(e){n.$refs.annex_certificate.clearFiles(e)})),this.annexFile.annex_certificate.forEach((function(e){n.handleUploadFile(e,"annex_certificate")}))},handleUAISelect:function(t){var n=this,r=t.tempFiles,a=[];r.forEach((function(t,r){var i=t.name,o=t.url,s=t.uuid,u=t.file,c=n.isFileValid(u);e.log(c),c?n.annexFile.annex_id.push({name:i,url:o,uuid:s,file:u}):(uni.$u.toast("".concat(u.name," 文件格式不支持")),a.push(r))})),a.reverse().forEach((function(e){n.$refs.annex_id.clearFiles(e)})),this.annexFile.annex_id.forEach((function(e){n.handleUploadFile(e,"annex_id")}))},handleUAPSelect:function(t){var n=this,r=t.tempFiles,a=[];r.forEach((function(t,r){var i=t.name,o=t.url,s=t.uuid,u=t.file,c=n.isFileValid(u);e.log(c),c?n.annexFile.annex_photo.push({name:i,url:o,uuid:s,file:u}):(uni.$u.toast("".concat(u.name," 文件格式不支持")),a.push(r))})),a.reverse().forEach((function(e){n.$refs.annex_photo.clearFiles(e)})),this.annexFile.annex_photo.forEach((function(e){n.handleUploadFile(e,"annex_photo")}))},handleUAInSelect:function(t){var n=this,r=t.tempFiles,a=[];r.forEach((function(t,r){var i=t.name,o=t.url,s=t.uuid,u=t.file,c=n.isFileValid(u);e.log(c),c?n.annexFile.annex_investigation.push({name:i,url:o,uuid:s,file:u}):(uni.$u.toast("".concat(u.name," 文件格式不支持")),a.push(r))})),a.reverse().forEach((function(e){n.$refs.annex_investigation.clearFiles(e)})),this.annexFile.annex_investigation.forEach((function(e){n.handleUploadFile(e,"annex_investigation")}))}}};t.default=l}).call(this,n("ba7c")["default"])},"30ee":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("d29e")),i=r(n("bb76")),o={name:"u--text",mixins:[uni.$u.mpMixin,i.default,uni.$u.mixin],components:{uvText:a.default}};t.default=o},"314f":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".uni-file-picker[data-v-509aa77e]{\nbox-sizing:border-box;overflow:hidden;width:100%;\nflex:1}.uni-file-picker__header[data-v-509aa77e]{padding-top:5px;padding-bottom:10px;\ndisplay:flex;\njustify-content:space-between}.file-title[data-v-509aa77e]{font-size:14px;color:#333}.file-count[data-v-509aa77e]{font-size:14px;color:#999}.icon-add[data-v-509aa77e]{width:50px;height:5px;background-color:#f1f1f1;border-radius:2px}.rotate[data-v-509aa77e]{position:absolute;-webkit-transform:rotate(90deg);transform:rotate(90deg)}",""]),e.exports=t},"33f5":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.downloadFile=void 0;t.downloadFile=function(e,t){var n=document.createElement("a");n.href=t,n.download=e||"file.ext",n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n)}},"371b":function(e,t,n){"use strict";n.r(t);var r=n("fa22"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"3bde":function(e,t,n){"use strict";n.r(t);var r=n("e497"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"3fe8":function(e,t,n){"use strict";n.r(t);var r=n("4d1b"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},4085:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1");r({global:!0,forced:a.globalThis!==a},{globalThis:a})},"45b4":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-1ba40ab6], uni-scroll-view[data-v-1ba40ab6], uni-swiper-item[data-v-1ba40ab6]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-textarea[data-v-1ba40ab6]{border-radius:4px;background-color:#fff;position:relative;\ndisplay:flex;\nflex-direction:row;flex:1;padding:9px}.u-textarea--radius[data-v-1ba40ab6]{border-radius:4px}.u-textarea--no-radius[data-v-1ba40ab6]{border-radius:0}.u-textarea--disabled[data-v-1ba40ab6]{background-color:#f5f7fa}.u-textarea__field[data-v-1ba40ab6]{flex:1;font-size:15px;color:#606266;width:100%}.u-textarea__count[data-v-1ba40ab6]{position:absolute;right:5px;bottom:2px;font-size:12px;color:#909193;background-color:#fff;padding:1px 4px}",""]),e.exports=t},"47c4":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uBadge:n("9df0").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-tabs"},[n("v-uni-view",{staticClass:"u-tabs__wrapper"},[e._t("left"),n("v-uni-view",{staticClass:"u-tabs__wrapper__scroll-view-wrapper"},[n("v-uni-scroll-view",{ref:"u-tabs__wrapper__scroll-view",staticClass:"u-tabs__wrapper__scroll-view",attrs:{"scroll-x":e.scrollable,"scroll-left":e.scrollLeft,"scroll-with-animation":!0,"show-scrollbar":!1}},[n("v-uni-view",{ref:"u-tabs__wrapper__nav",staticClass:"u-tabs__wrapper__nav"},[e._l(e.list,(function(t,r){return n("v-uni-view",{key:r,ref:"u-tabs__wrapper__nav__item-"+r,refInFor:!0,staticClass:"u-tabs__wrapper__nav__item",class:["u-tabs__wrapper__nav__item-"+r,t.disabled&&"u-tabs__wrapper__nav__item--disabled"],style:[e.$u.addStyle(e.itemStyle),{flex:e.scrollable?"":1}],on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.clickHandler(t,r)}}},[n("v-uni-text",{staticClass:"u-tabs__wrapper__nav__item__text",class:[t.disabled&&"u-tabs__wrapper__nav__item__text--disabled"],style:[e.textStyle(r)]},[e._v(e._s(t[e.keyName]))]),n("u-badge",{attrs:{show:!(!t.badge||!(t.badge.show||t.badge.isDot||t.badge.value)),isDot:t.badge&&t.badge.isDot||e.propsBadge.isDot,value:t.badge&&t.badge.value||e.propsBadge.value,max:t.badge&&t.badge.max||e.propsBadge.max,type:t.badge&&t.badge.type||e.propsBadge.type,showZero:t.badge&&t.badge.showZero||e.propsBadge.showZero,bgColor:t.badge&&t.badge.bgColor||e.propsBadge.bgColor,color:t.badge&&t.badge.color||e.propsBadge.color,shape:t.badge&&t.badge.shape||e.propsBadge.shape,numberType:t.badge&&t.badge.numberType||e.propsBadge.numberType,inverted:t.badge&&t.badge.inverted||e.propsBadge.inverted,customStyle:"margin-left: 4px;"}})],1)})),n("v-uni-view",{ref:"u-tabs__wrapper__nav__line",staticClass:"u-tabs__wrapper__nav__line",style:[{width:e.$u.addUnit(e.lineWidth),transform:"translate("+e.lineOffsetLeft+"px)",transitionDuration:(e.firstTime?0:e.duration)+"ms",height:e.$u.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}]})],2)],1)],1),e._t("right")],2)],1)},i=[]},4846:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uvText",{attrs:{type:e.type,show:e.show,text:e.text,prefixIcon:e.prefixIcon,suffixIcon:e.suffixIcon,mode:e.mode,href:e.href,format:e.format,call:e.call,openType:e.openType,bold:e.bold,block:e.block,lines:e.lines,color:e.color,decoration:e.decoration,size:e.size,iconStyle:e.iconStyle,margin:e.margin,lineHeight:e.lineHeight,align:e.align,wordWrap:e.wordWrap,customStyle:e.customStyle},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("click")}}})},a=[]},"49c9":function(e,t,n){"use strict";n.r(t);var r=n("af24"),a=n("24e5");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("d248");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"1ba40ab6",null,!1,r["a"],void 0);t["default"]=s.exports},"49ce":function(e,t,n){"use strict";n.r(t);var r=n("922d"),a=n("1b1e");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("b109");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"03e1ba13",null,!1,r["a"],void 0);t["default"]=s.exports},"4d1b":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("92e9")),i={name:"u-line",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],computed:{lineStyle:function(){var e={};return e.margin=this.margin,"row"===this.direction?(e.borderBottomWidth="1px",e.borderBottomStyle=this.dashed?"dashed":"solid",e.width=uni.$u.addUnit(this.length),this.hairline&&(e.transform="scaleY(0.5)")):(e.borderLeftWidth="1px",e.borderLeftStyle=this.dashed?"dashed":"solid",e.height=uni.$u.addUnit(this.length),this.hairline&&(e.transform="scaleX(0.5)")),e.borderColor=this.color,uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))}}};t.default=i},"51f2":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-3fa5722e], uni-scroll-view[data-v-3fa5722e], uni-swiper-item[data-v-3fa5722e]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-row[data-v-3fa5722e]{\ndisplay:flex;\nflex-direction:row}",""]),e.exports=t},"544f":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("2634")),i=r(n("2fdc"));n("64aa"),n("bf0f");var o=r(n("0463")),s={name:"u-row",mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],data:function(){return{}},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align},rowStyle:function(){var e={alignItems:this.uAlignItem,justifyContent:this.uJustify};return this.gutter&&(e.marginLeft=uni.$u.addUnit(-Number(this.gutter)/2),e.marginRight=uni.$u.addUnit(-Number(this.gutter)/2)),uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))}},methods:{clickHandler:function(e){this.$emit("click")},getComponentWidth:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,uni.$u.sleep();case 2:return t.abrupt("return",new Promise((function(t){e.$uGetRect(".u-row").then((function(e){t(e.width)}))})));case 3:case"end":return t.stop()}}),t)})))()}}};t.default=s},"56ac":function(e,t,n){var r=n("b907");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("6a33678f",r,!0,{sourceMap:!1,shadowMode:!1})},"56f9":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{model:{type:Object,default:uni.$u.props.form.model},rules:{type:[Object,Function,Array],default:uni.$u.props.form.rules},errorType:{type:String,default:uni.$u.props.form.errorType},borderBottom:{type:Boolean,default:uni.$u.props.form.borderBottom},labelPosition:{type:String,default:uni.$u.props.form.labelPosition},labelWidth:{type:[String,Number],default:uni.$u.props.form.labelWidth},labelAlign:{type:String,default:uni.$u.props.form.labelAlign},labelStyle:{type:Object,default:uni.$u.props.form.labelStyle}}};t.default=r},"5b6d":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var a=r(n("2ce9")),i={name:"u-badge",mixins:[uni.$u.mpMixin,a.default,uni.$u.mixin],computed:{boxStyle:function(){return{}},badgeStyle:function(){var e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){var t=this.offset[0],n=this.offset[1]||t;e.top=uni.$u.addUnit(t),e.right=uni.$u.addUnit(n)}return e},showValue:function(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}}};t.default=i},"5c0e":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{duration:{type:Number,default:uni.$u.props.tabs.duration},list:{type:Array,default:uni.$u.props.tabs.list},lineColor:{type:String,default:uni.$u.props.tabs.lineColor},activeStyle:{type:[String,Object],default:uni.$u.props.tabs.activeStyle},inactiveStyle:{type:[String,Object],default:uni.$u.props.tabs.inactiveStyle},lineWidth:{type:[String,Number],default:uni.$u.props.tabs.lineWidth},lineHeight:{type:[String,Number],default:uni.$u.props.tabs.lineHeight},lineBgSize:{type:String,default:uni.$u.props.tabs.lineBgSize},itemStyle:{type:[String,Object],default:uni.$u.props.tabs.itemStyle},scrollable:{type:Boolean,default:uni.$u.props.tabs.scrollable},current:{type:[Number,String],default:uni.$u.props.tabs.current},keyName:{type:String,default:uni.$u.props.tabs.keyName}}};t.default=r},"5cb8":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{value:{type:[String,Number,Boolean],default:uni.$u.props.radioGroup.value},disabled:{type:Boolean,default:uni.$u.props.radioGroup.disabled},shape:{type:String,default:uni.$u.props.radioGroup.shape},activeColor:{type:String,default:uni.$u.props.radioGroup.activeColor},inactiveColor:{type:String,default:uni.$u.props.radioGroup.inactiveColor},name:{type:String,default:uni.$u.props.radioGroup.name},size:{type:[String,Number],default:uni.$u.props.radioGroup.size},placement:{type:String,default:uni.$u.props.radioGroup.placement},label:{type:[String],default:uni.$u.props.radioGroup.label},labelColor:{type:[String],default:uni.$u.props.radioGroup.labelColor},labelSize:{type:[String,Number],default:uni.$u.props.radioGroup.labelSize},labelDisabled:{type:Boolean,default:uni.$u.props.radioGroup.labelDisabled},iconColor:{type:String,default:uni.$u.props.radioGroup.iconColor},iconSize:{type:[String,Number],default:uni.$u.props.radioGroup.iconSize},borderBottom:{type:Boolean,default:uni.$u.props.radioGroup.borderBottom},iconPlacement:{type:String,default:uni.$u.props.radio.iconPlacement}}};t.default=r},"5d01":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".uni-file-picker__files[data-v-1fecfc2e]{display:flex;flex-direction:column;justify-content:flex-start}.uni-file-picker__lists[data-v-1fecfc2e]{position:relative;margin-top:5px;overflow:hidden}.file-picker__mask[data-v-1fecfc2e]{display:flex;justify-content:center;align-items:center;position:absolute;right:0;top:0;bottom:0;left:0;color:#fff;font-size:14px;background-color:rgba(0,0,0,.4)}.uni-file-picker__lists-box[data-v-1fecfc2e]{position:relative}.uni-file-picker__item[data-v-1fecfc2e]{display:flex;align-items:center;padding:8px 10px;padding-right:5px;padding-left:10px}.files-border[data-v-1fecfc2e]{border-top:1px #eee solid}.files__name[data-v-1fecfc2e]{flex:1;font-size:14px;color:#666;margin-right:25px;word-break:break-all;word-wrap:break-word}.icon-files[data-v-1fecfc2e]{position:static;background-color:initial}.is-list-card[data-v-1fecfc2e]{border:1px #eee solid;margin-bottom:5px;border-radius:5px;box-shadow:0 0 2px 0 rgba(0,0,0,.1);padding:5px}.files__image[data-v-1fecfc2e]{width:40px;height:40px;margin-right:10px}.header-image[data-v-1fecfc2e]{width:100%;height:100%}.is-text-box[data-v-1fecfc2e]{border:1px #eee solid;border-radius:5px}.is-text-image[data-v-1fecfc2e]{width:25px;height:25px;margin-left:5px}.rotate[data-v-1fecfc2e]{position:absolute;-webkit-transform:rotate(90deg);transform:rotate(90deg)}.icon-del-box[data-v-1fecfc2e]{display:flex;margin:auto 0;align-items:center;justify-content:center;position:absolute;top:0;bottom:0;right:5px;height:26px;width:26px;z-index:2;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.icon-del[data-v-1fecfc2e]{width:15px;height:1px;background-color:#333}@media (min-width:768px){.uni-file-picker__files[data-v-1fecfc2e]{max-width:375px}}",""]),e.exports=t},"5d54":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-4236db40], uni-scroll-view[data-v-4236db40], uni-swiper-item[data-v-4236db40]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-radio-group[data-v-4236db40]{flex:1}.u-radio-group--row[data-v-4236db40]{\ndisplay:flex;\nflex-direction:row}.u-radio-group--column[data-v-4236db40]{\ndisplay:flex;\nflex-direction:column}",""]),e.exports=t},"5ef4":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"u-form"},[this._t("default")],2)},a=[]},"5ff1":function(e,t,n){var r=n("80f4");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("6b41fab2",r,!0,{sourceMap:!1,shadowMode:!1})},"60ad":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uvTextarea",{attrs:{value:e.value,placeholder:e.placeholder,height:e.height,confirmType:e.confirmType,disabled:e.disabled,count:e.count,focus:e.focus,autoHeight:e.autoHeight,fixed:e.fixed,cursorSpacing:e.cursorSpacing,cursor:e.cursor,showConfirmBar:e.showConfirmBar,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,disableDefaultPadding:e.disableDefaultPadding,holdKeyboard:e.holdKeyboard,maxlength:e.maxlength,border:e.border,customStyle:e.customStyle,formatter:e.formatter,ignoreCompositionEvent:e.ignoreCompositionEvent},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("focus")}.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("blur")}.apply(void 0,arguments)},linechange:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("linechange",t)}.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("confirm")}.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("input",t)}.apply(void 0,arguments)},keyboardheightchange:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("keyboardheightchange")}.apply(void 0,arguments)}}})},a=[]},"62b0":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t&&("object"===(0,r.default)(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,a.default)(e)},n("7a76"),n("c9b5");var r=i(n("fcf3")),a=i(n("f478"));function i(e){return e&&e.__esModule?e:{default:e}}},"640e":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={name:"UniStatusBar",data:function(){return{statusBarHeight:uni.getSystemInfoSync().statusBarHeight+"px"}}};t.default=r},6684:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"u-radio-group",class:this.bemClass},[this._t("default")],2)},a=[]},6730:function(e,t,n){"use strict";var r=n("8bdb"),a=n("71e9");r({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return a(URL.prototype.toString,this)}})},"68ef":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=u,n("c1a3"),n("bf0f"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=s(n("f1f8")),a=s(n("e668")),i=s(n("d441")),o=s(n("d2c4"));function s(e){return e&&e.__esModule?e:{default:e}}function u(e){var n="function"===typeof Map?new Map:void 0;return t.default=u=function(e){if(null===e||!(0,i.default)(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return(0,o.default)(e,arguments,(0,r.default)(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),(0,a.default)(t,e)},u(e)}},"698d":function(e,t,n){var r=n("45b4");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("f35a8fe0",r,!0,{sourceMap:!1,shadowMode:!1})},"6a88":function(e,t,n){"use strict";var r=n("8bdb"),a=n("6aa6"),i=n("9f9e"),o=n("8598"),s=n("5ee2"),u=n("e7e3"),c=n("1c06"),l=n("e37c"),d=n("af9e"),f=a("Reflect","construct"),p=Object.prototype,h=[].push,v=d((function(){function e(){}return!(f((function(){}),[],e)instanceof e)})),g=!d((function(){f((function(){}))})),m=v||g;r({target:"Reflect",stat:!0,forced:m,sham:m},{construct:function(e,t){s(e),u(t);var n=arguments.length<3?e:s(arguments[2]);if(g&&!v)return f(e,t,n);if(e===n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return i(h,r,t),new(i(o,e,r))}var a=n.prototype,d=l(c(a)?a:p),m=i(e,d,t);return c(m)?m:d}})},"6ad17":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("c223"),n("aa9c");var a=r(n("8237")),i={name:"u-textarea",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:function(e){return e}}},watch:{value:{immediate:!0,handler:function(e,t){this.innerValue=e,!1===this.firstChange&&!1===this.changeFromInner&&this.valueChange(),this.firstChange=!1,this.changeFromInner=!1}}},computed:{textareaClass:function(){var e=[],t=this.border,n=this.disabled;this.shape;return"surround"===t&&(e=e.concat(["u-border","u-textarea--radius"])),"bottom"===t&&(e=e.concat(["u-border-bottom","u-textarea--no-radius"])),n&&e.push("u-textarea--disabled"),e.join(" ")},textareaStyle:function(){return uni.$u.deepMerge({},uni.$u.addStyle(this.customStyle))}},methods:{setFormatter:function(e){this.innerFormatter=e},onFocus:function(e){this.$emit("focus",e)},onBlur:function(e){this.$emit("blur",e),uni.$u.formValidate(this,"blur")},onLinechange:function(e){this.$emit("linechange",e)},onInput:function(e){var t=this,n=e.detail||{},r=n.value,a=void 0===r?"":r,i=this.formatter||this.innerFormatter,o=i(a);this.innerValue=a,this.$nextTick((function(){t.innerValue=o,t.valueChange()}))},valueChange:function(){var e=this,t=this.innerValue;this.$nextTick((function(){e.$emit("input",t),e.changeFromInner=!0,e.$emit("change",t),uni.$u.formValidate(e,"change")}))},onConfirm:function(e){this.$emit("confirm",e)},onKeyboardheightchange:function(e){this.$emit("keyboardheightchange",e)}}};t.default=i},"6ae7":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".uni-file-picker__container[data-v-3e80374f]{display:flex;box-sizing:border-box;flex-wrap:wrap;margin:-5px}.file-picker__box[data-v-3e80374f]{position:relative;width:33.3%;height:0;padding-top:33.33%;box-sizing:border-box}.file-picker__box-content[data-v-3e80374f]{position:absolute;top:0;right:0;bottom:0;left:0;margin:5px;border:1px #eee solid;border-radius:5px;overflow:hidden}.file-picker__progress[data-v-3e80374f]{position:absolute;bottom:0;left:0;right:0;\n  /* border: 1px red solid; */z-index:2}.file-picker__progress-item[data-v-3e80374f]{width:100%}.file-picker__mask[data-v-3e80374f]{display:flex;justify-content:center;align-items:center;position:absolute;right:0;top:0;bottom:0;left:0;color:#fff;font-size:12px;background-color:rgba(0,0,0,.4)}.file-image[data-v-3e80374f]{width:100%;height:100%}.is-add[data-v-3e80374f]{display:flex;align-items:center;justify-content:center}.rotate[data-v-3e80374f]{position:absolute;-webkit-transform:rotate(90deg);transform:rotate(90deg)}.icon-del-box[data-v-3e80374f]{display:flex;align-items:center;justify-content:center;position:absolute;top:3px;right:3px;height:26px;width:26px;border-radius:50%;background-color:rgba(0,0,0,.5);z-index:2;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.icon-del[data-v-3e80374f]{width:15px;height:2px;background-color:#fff;border-radius:2px}",""]),e.exports=t},"6bf8":function(e,t,n){"use strict";n.r(t);var r=n("1479"),a=n("27e9");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("0a17");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"3e80374f",null,!1,r["a"],void 0);t["default"]=s.exports},"6c31":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},n("bf0f"),n("7996"),n("6a88")},"6ddf":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("2634")),i=r(n("2fdc")),o=r(n("8f65")),s={name:"u-col",mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],data:function(){return{width:0,parentData:{gutter:0},gridNum:12}},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align},colStyle:function(){var e={paddingLeft:uni.$u.addUnit(uni.$u.getPx(this.parentData.gutter)/2),paddingRight:uni.$u.addUnit(uni.$u.getPx(this.parentData.gutter)/2),alignItems:this.uAlignItem,justifyContent:this.uJustify,textAlign:this.textAlign,flex:"0 0 ".concat(100/this.gridNum*this.span,"%"),marginLeft:100/12*this.offset+"%"};return uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))}},mounted:function(){this.init()},methods:{init:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.updateParentData(),t.next=3,e.parent.getComponentWidth();case 3:e.width=t.sent;case 4:case"end":return t.stop()}}),t)})))()},updateParentData:function(){this.getParentData("u-row")},clickHandler:function(e){this.$emit("click")}}};t.default=s},"6eda":function(e,t,n){"use strict";(function(e,r){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("9b1b")),o=a(n("fcf3"));n("bf0f"),n("2797"),n("aa9c"),n("f7a5"),n("5c47"),n("a1c1"),n("64aa"),n("d4b5"),n("dc8a"),n("5ef2"),n("0506"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("2c10"),n("7a76"),n("c9b5"),n("c223"),n("de6c"),n("fd3c"),n("dd2b");var s=/%[sdj%]/g,u=function(){};function c(e){if(!e||!e.length)return null;var t={};return e.forEach((function(e){var n=e.field;t[n]=t[n]||[],t[n].push(e)})),t}function l(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=1,a=t[0],i=t.length;if("function"===typeof a)return a.apply(null,t.slice(1));if("string"===typeof a){for(var o=String(a).replace(s,(function(e){if("%%"===e)return"%";if(r>=i)return e;switch(e){case"%s":return String(t[r++]);case"%d":return Number(t[r++]);case"%j":try{return JSON.stringify(t[r++])}catch(n){return"[Circular]"}break;default:return e}})),u=t[r];r<i;u=t[++r])o+=" ".concat(u);return o}return a}function d(e,t){return void 0===e||null===e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}(t)||"string"!==typeof e||e))}function f(e,t,n){var r=0,a=e.length;(function i(o){if(o&&o.length)n(o);else{var s=r;r+=1,s<a?t(e[s],i):n([])}})([])}function p(e,t,n,r){if(t.first){var a=new Promise((function(t,a){var i=function(e){var t=[];return Object.keys(e).forEach((function(n){t.push.apply(t,e[n])})),t}(e);f(i,n,(function(e){return r(e),e.length?a({errors:e,fields:c(e)}):t()}))}));return a.catch((function(e){return e})),a}var i=t.firstFields||[];!0===i&&(i=Object.keys(e));var o=Object.keys(e),s=o.length,u=0,l=[],d=new Promise((function(t,a){var d=function(e){if(l.push.apply(l,e),u++,u===s)return r(l),l.length?a({errors:l,fields:c(l)}):t()};o.length||(r(l),t()),o.forEach((function(t){var r=e[t];-1!==i.indexOf(t)?f(r,n,d):function(e,t,n){var r=[],a=0,i=e.length;function o(e){r.push.apply(r,e),a++,a===i&&n(r)}e.forEach((function(e){t(e,o)}))}(r,n,d)}))}));return d.catch((function(e){return e})),d}function h(e){return function(t){return t&&t.message?(t.field=t.field||e.fullField,t):{message:"function"===typeof t?t():t,field:t.field||e.fullField}}}function v(e,t){if(t)for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];"object"===(0,o.default)(r)&&"object"===(0,o.default)(e[n])?e[n]=(0,i.default)((0,i.default)({},e[n]),r):e[n]=r}return e}function g(e,t,n,r,a,i){!e.required||n.hasOwnProperty(e.field)&&!d(t,i||e.type)||r.push(l(a.messages.required,e.fullField))}"undefined"!==typeof e&&Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_INDEX_CSS_HASH:"2da1efab",VUE_APP_INDEX_DARK_CSS_HASH:"aeec55f8",VUE_APP_NAME:"职业健康达人",VUE_APP_PLATFORM:"h5",BASE_URL:"/"});var m={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},y={integer:function(e){return/^(-)?\d+$/.test(e)},float:function(e){return/^(-)?\d+(\.\d+)?$/.test(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"===typeof e.getTime&&"function"===typeof e.getMonth&&"function"===typeof e.getYear},number:function(e){return!isNaN(e)&&"number"===typeof+e},object:function(e){return"object"===(0,o.default)(e)&&!y.array(e)},method:function(e){return"function"===typeof e},email:function(e){return"string"===typeof e&&!!e.match(m.email)&&e.length<255},url:function(e){return"string"===typeof e&&!!e.match(m.url)},hex:function(e){return"string"===typeof e&&!!e.match(m.hex)}};var b={required:g,whitespace:function(e,t,n,r,a){(/^\s+$/.test(t)||""===t)&&r.push(l(a.messages.whitespace,e.fullField))},type:function(e,t,n,r,a){if(e.required&&void 0===t)g(e,t,n,r,a);else{var i=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(i)>-1?y[i](t)||r.push(l(a.messages.types[i],e.fullField,e.type)):i&&(0,o.default)(t)!==e.type&&r.push(l(a.messages.types[i],e.fullField,e.type))}},range:function(e,t,n,r,a){var i="number"===typeof e.len,o="number"===typeof e.min,s="number"===typeof e.max,u=t,c=null,d="number"===typeof t,f="string"===typeof t,p=Array.isArray(t);if(d?c="number":f?c="string":p&&(c="array"),!c)return!1;p&&(u=t.length),f&&(u=t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),i?u!==e.len&&r.push(l(a.messages[c].len,e.fullField,e.len)):o&&!s&&u<e.min?r.push(l(a.messages[c].min,e.fullField,e.min)):s&&!o&&u>e.max?r.push(l(a.messages[c].max,e.fullField,e.max)):o&&s&&(u<e.min||u>e.max)&&r.push(l(a.messages[c].range,e.fullField,e.min,e.max))},enum:function(e,t,n,r,a){e["enum"]=Array.isArray(e["enum"])?e["enum"]:[],-1===e["enum"].indexOf(t)&&r.push(l(a.messages["enum"],e.fullField,e["enum"].join(", ")))},pattern:function(e,t,n,r,a){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||r.push(l(a.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"===typeof e.pattern){var i=new RegExp(e.pattern);i.test(t)||r.push(l(a.messages.pattern.mismatch,e.fullField,t,e.pattern))}}};function _(e,t,n,r,a){var i=e.type,o=[],s=e.required||!e.required&&r.hasOwnProperty(e.field);if(s){if(d(t,i)&&!e.required)return n();b.required(e,t,r,o,a,i),d(t,i)||b.type(e,t,r,o,a)}n(o)}var x={string:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t,"string")&&!e.required)return n();b.required(e,t,r,i,a,"string"),d(t,"string")||(b.type(e,t,r,i,a),b.range(e,t,r,i,a),b.pattern(e,t,r,i,a),!0===e.whitespace&&b.whitespace(e,t,r,i,a))}n(i)},method:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&b.type(e,t,r,i,a)}n(i)},number:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(""===t&&(t=void 0),d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&(b.type(e,t,r,i,a),b.range(e,t,r,i,a))}n(i)},boolean:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&b.type(e,t,r,i,a)}n(i)},regexp:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),d(t)||b.type(e,t,r,i,a)}n(i)},integer:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&(b.type(e,t,r,i,a),b.range(e,t,r,i,a))}n(i)},float:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&(b.type(e,t,r,i,a),b.range(e,t,r,i,a))}n(i)},array:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t,"array")&&!e.required)return n();b.required(e,t,r,i,a,"array"),d(t,"array")||(b.type(e,t,r,i,a),b.range(e,t,r,i,a))}n(i)},object:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&b.type(e,t,r,i,a)}n(i)},enum:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a),void 0!==t&&b["enum"](e,t,r,i,a)}n(i)},pattern:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t,"string")&&!e.required)return n();b.required(e,t,r,i,a),d(t,"string")||b.pattern(e,t,r,i,a)}n(i)},date:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();var s;if(b.required(e,t,r,i,a),!d(t))s="number"===typeof t?new Date(t):t,b.type(e,s,r,i,a),s&&b.range(e,s.getTime(),r,i,a)}n(i)},url:_,hex:_,email:_,required:function(e,t,n,r,a){var i=[],s=Array.isArray(t)?"array":(0,o.default)(t);b.required(e,t,r,i,a,s),n(i)},any:function(e,t,n,r,a){var i=[],o=e.required||!e.required&&r.hasOwnProperty(e.field);if(o){if(d(t)&&!e.required)return n();b.required(e,t,r,i,a)}n(i)}};function w(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var k=w();function S(e){this.rules=null,this._messages=k,this.define(e)}S.prototype={messages:function(e){return e&&(this._messages=v(w(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!==(0,o.default)(e)||Array.isArray(e))throw new Error("Rules must be an object");var t,n;for(t in this.rules={},e)e.hasOwnProperty(t)&&(n=e[t],this.rules[t]=Array.isArray(n)?n:[n])},validate:function(e,t,n){var r=this;void 0===t&&(t={}),void 0===n&&(n=function(){});var a,s,u=e,d=t,f=n;if("function"===typeof d&&(f=d,d={}),!this.rules||0===Object.keys(this.rules).length)return f&&f(),Promise.resolve();if(d.messages){var g=this.messages();g===k&&(g=w()),v(g,d.messages),d.messages=g}else d.messages=this.messages();var m={},y=d.keys||Object.keys(this.rules);y.forEach((function(t){a=r.rules[t],s=u[t],a.forEach((function(n){var a=n;"function"===typeof a.transform&&(u===e&&(u=(0,i.default)({},u)),s=u[t]=a.transform(s)),a="function"===typeof a?{validator:a}:(0,i.default)({},a),a.validator=r.getValidationMethod(a),a.field=t,a.fullField=a.fullField||t,a.type=r.getType(a),a.validator&&(m[t]=m[t]||[],m[t].push({rule:a,value:s,source:u,field:t}))}))}));var b={};return p(m,d,(function(e,t){var n,r=e.rule,a=("object"===r.type||"array"===r.type)&&("object"===(0,o.default)(r.fields)||"object"===(0,o.default)(r.defaultField));function s(e,t){return(0,i.default)((0,i.default)({},t),{},{fullField:"".concat(r.fullField,".").concat(e)})}function u(n){void 0===n&&(n=[]);var o=n;if(Array.isArray(o)||(o=[o]),!d.suppressWarning&&o.length&&S.warning("async-validator:",o),o.length&&r.message&&(o=[].concat(r.message)),o=o.map(h(r)),d.first&&o.length)return b[r.field]=1,t(o);if(a){if(r.required&&!e.value)return o=r.message?[].concat(r.message).map(h(r)):d.error?[d.error(r,l(d.messages.required,r.field))]:[],t(o);var u={};if(r.defaultField)for(var c in e.value)e.value.hasOwnProperty(c)&&(u[c]=r.defaultField);for(var f in u=(0,i.default)((0,i.default)({},u),e.rule.fields),u)if(u.hasOwnProperty(f)){var p=Array.isArray(u[f])?u[f]:[u[f]];u[f]=p.map(s.bind(null,f))}var v=new S(u);v.messages(d.messages),e.rule.options&&(e.rule.options.messages=d.messages,e.rule.options.error=d.error),v.validate(e.value,e.rule.options||d,(function(e){var n=[];o&&o.length&&n.push.apply(n,o),e&&e.length&&n.push.apply(n,e),t(n.length?n:null)}))}else t(o)}a=a&&(r.required||!r.required&&e.value),r.field=e.field,r.asyncValidator?n=r.asyncValidator(r,e.value,u,e.source,d):r.validator&&(n=r.validator(r,e.value,u,e.source,d),!0===n?u():!1===n?u(r.message||"".concat(r.field," fails")):n instanceof Array?u(n):n instanceof Error&&u(n.message)),n&&n.then&&n.then((function(){return u()}),(function(e){return u(e)}))}),(function(e){(function(e){var t,n=[],r={};function a(e){var t;Array.isArray(e)?n=(t=n).concat.apply(t,e):n.push(e)}for(t=0;t<e.length;t++)a(e[t]);n.length?r=c(n):(n=null,r=null),f(n,r)})(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!==typeof e.validator&&e.type&&!x.hasOwnProperty(e.type))throw new Error(l("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"===typeof e.validator)return e.validator;var t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?x.required:x[this.getType(e)]||!1}},S.register=function(e,t){if("function"!==typeof t)throw new Error("Cannot register a validator by type, validator is not a function");x[e]=t},S.warning=u,S.messages=k;var P=S;t.default=P}).call(this,n("28d0"),n("ba7c")["default"])},"6ef1":function(e,t,n){"use strict";n.r(t);var r=n("022e"),a=n("825e");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("048c");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"3fa5722e",null,!1,r["a"],void 0);t["default"]=s.exports},"6efe":function(e,t,n){var r=n("8e16");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("5bcdab21",r,!0,{sourceMap:!1,shadowMode:!1})},"6f0c":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var a=r(n("dbe1")),i=function(e){return"number"===typeof e?e+"px":e},o={name:"UniNavBar",components:{statusBar:a.default},emits:["clickLeft","clickRight","clickTitle"],props:{dark:{type:Boolean,default:!1},title:{type:String,default:""},leftText:{type:String,default:""},rightText:{type:String,default:""},leftIcon:{type:String,default:""},rightIcon:{type:String,default:""},fixed:{type:[Boolean,String],default:!1},color:{type:String,default:""},backgroundColor:{type:String,default:""},statusBar:{type:[Boolean,String],default:!1},shadow:{type:[Boolean,String],default:!1},border:{type:[Boolean,String],default:!0},height:{type:[Number,String],default:44},leftWidth:{type:[Number,String],default:60},rightWidth:{type:[Number,String],default:60},stat:{type:[Boolean,String],default:""}},computed:{themeBgColor:function(){return this.dark?this.backgroundColor?this.backgroundColor:this.dark?"#333":"#FFF":this.backgroundColor||"#FFF"},themeColor:function(){return this.dark?this.color?this.color:this.dark?"#fff":"#333":this.color||"#333"},navbarHeight:function(){return i(this.height)},leftIconWidth:function(){return i(this.leftWidth)},rightIconWidth:function(){return i(this.rightWidth)}},mounted:function(){uni.report&&this.stat&&""!==this.title&&uni.report("title",this.title)},methods:{onClickLeft:function(){this.$emit("clickLeft")},onClickRight:function(){this.$emit("clickRight")},onClickTitle:function(){this.$emit("clickTitle")}}};t.default=o},"70db":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uvInput",{attrs:{value:e.value,type:e.type,fixed:e.fixed,disabled:e.disabled,disabledColor:e.disabledColor,clearable:e.clearable,password:e.password,maxlength:e.maxlength,placeholder:e.placeholder,placeholderClass:e.placeholderClass,placeholderStyle:e.placeholderStyle,showWordLimit:e.showWordLimit,confirmType:e.confirmType,confirmHold:e.confirmHold,holdKeyboard:e.holdKeyboard,focus:e.focus,autoBlur:e.autoBlur,disableDefaultPadding:e.disableDefaultPadding,cursor:e.cursor,cursorSpacing:e.cursorSpacing,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,inputAlign:e.inputAlign,fontSize:e.fontSize,color:e.color,prefixIcon:e.prefixIcon,suffixIcon:e.suffixIcon,suffixIconStyle:e.suffixIconStyle,prefixIconStyle:e.prefixIconStyle,border:e.border,readonly:e.readonly,shape:e.shape,customStyle:e.customStyle,formatter:e.formatter,ignoreCompositionEvent:e.ignoreCompositionEvent},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("focus")},blur:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("blur",t)}.apply(void 0,arguments)},keyboardheightchange:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("keyboardheightchange")},change:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("change",t)}.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("input",t)}.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("confirm",t)}.apply(void 0,arguments)},clear:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("clear")},click:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("click")}}},[e._t("prefix",null,{slot:"prefix"}),e._t("suffix",null,{slot:"suffix"})],2)},a=[]},"71bf":function(e,t,n){"use strict";var r=n("aa3b"),a=n.n(r);a.a},"71e5":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("2634")),i=r(n("b7c7")),o=r(n("39d8")),s=r(n("2fdc"));n("fd3c"),n("dc8a"),n("c223"),n("4626"),n("5ac7"),n("5c47"),n("0506"),n("aa9c"),n("bf0f");var u=r(n("56f9")),c=r(n("6eda"));c.default.warning=function(){};var l={name:"u-form",mixins:[uni.$u.mpMixin,uni.$u.mixin,u.default],provide:function(){return{uForm:this}},data:function(){return{formRules:{},validator:{},originalModel:null}},watch:{rules:{immediate:!0,handler:function(e){this.setRules(e)}},propsChange:function(e){var t;null!==(t=this.children)&&void 0!==t&&t.length&&this.children.map((function(e){"function"==typeof e.updateParentData&&e.updateParentData()}))},model:{immediate:!0,handler:function(e){this.originalModel||(this.originalModel=uni.$u.deepClone(e))}}},computed:{propsChange:function(){return[this.errorType,this.borderBottom,this.labelPosition,this.labelWidth,this.labelAlign,this.labelStyle]}},created:function(){this.children=[]},methods:{setRules:function(e){0!==Object.keys(e).length&&(this.formRules=e,this.validator=new c.default(e))},resetFields:function(){this.resetModel()},resetModel:function(e){var t=this;this.children.map((function(e){var n=null===e||void 0===e?void 0:e.prop,r=uni.$u.getProperty(t.originalModel,n);uni.$u.setProperty(t.model,n,r)}))},clearValidate:function(e){e=[].concat(e),this.children.map((function(t){(void 0===e[0]||e.includes(t.prop))&&(t.message=null)}))},validateField:function(e,t){var n=arguments,r=this;return(0,s.default)((0,a.default)().mark((function s(){var u;return(0,a.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:u=n.length>2&&void 0!==n[2]?n[2]:null,r.$nextTick((function(){var n=[];e=[].concat(e),r.children.map((function(t){var a=[];if(e.includes(t.prop)){var s=uni.$u.getProperty(r.model,t.prop),l=t.prop.split("."),d=l[l.length-1],f=r.formRules[t.prop];if(!f)return;for(var p=[].concat(f),h=0;h<p.length;h++){var v=p[h],g=[].concat(null===v||void 0===v?void 0:v.trigger);if(!u||g.includes(u)){var m=new c.default((0,o.default)({},d,v));m.validate((0,o.default)({},d,s),(function(e,r){var o,s;uni.$u.test.array(e)&&(n.push.apply(n,(0,i.default)(e)),a.push.apply(a,(0,i.default)(e))),t.message=null!==(o=null===(s=a[0])||void 0===s?void 0:s.message)&&void 0!==o?o:null}))}}}})),"function"===typeof t&&t(n)}));case 2:case"end":return a.stop()}}),s)})))()},validate:function(e){var t=this;return new Promise((function(e,n){t.$nextTick((function(){var r=t.children.map((function(e){return e.prop}));t.validateField(r,(function(r){r.length?("toast"===t.errorType&&uni.$u.toast(r[0].message),n(r)):e(!0)}))}))}))}}};t.default=l},7391:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-7a78d4df], uni-scroll-view[data-v-7a78d4df], uni-swiper-item[data-v-7a78d4df]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-tabs__wrapper[data-v-7a78d4df]{\ndisplay:flex;\nflex-direction:row;align-items:center}.u-tabs__wrapper__scroll-view-wrapper[data-v-7a78d4df]{flex:1;overflow:auto hidden}.u-tabs__wrapper__scroll-view[data-v-7a78d4df]{\ndisplay:flex;\nflex-direction:row;flex:1}.u-tabs__wrapper__nav[data-v-7a78d4df]{\ndisplay:flex;\nflex-direction:row;position:relative}.u-tabs__wrapper__nav__item[data-v-7a78d4df]{padding:0 11px;\ndisplay:flex;\nflex-direction:row;align-items:center;justify-content:center}.u-tabs__wrapper__nav__item--disabled[data-v-7a78d4df]{cursor:not-allowed}.u-tabs__wrapper__nav__item__text[data-v-7a78d4df]{font-size:15px;color:#606266}.u-tabs__wrapper__nav__item__text--disabled[data-v-7a78d4df]{color:#c8c9cc!important}.u-tabs__wrapper__nav__line[data-v-7a78d4df]{height:3px;background:#3c9cff;width:30px;position:absolute;bottom:2px;border-radius:100px;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;transition-duration:.3s}",""]),e.exports=t},"73dcb":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("ab56")),i={name:"u-form-item",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{message:"",parentData:{labelPosition:"left",labelAlign:"left",labelStyle:{},labelWidth:45,errorType:"message"}}},computed:{propsLine:function(){return uni.$u.props.line}},mounted:function(){this.init()},methods:{init:function(){this.updateParentData(),this.parent||uni.$u.error("u-form-item需要结合u-form组件使用")},updateParentData:function(){this.getParentData("u-form")},clearValidate:function(){this.message=null},resetField:function(){var e=uni.$u.getProperty(this.parent.originalModel,this.prop);uni.$u.setProperty(this.parent.model,this.prop,e),this.message=null},clickHandler:function(){this.$emit("click")}}};t.default=i},7404:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.show&&(0!==Number(e.value)||e.showZero||e.isDot)?n("v-uni-text",{staticClass:"u-badge",class:[e.isDot?"u-badge--dot":"u-badge--not-dot",e.inverted&&"u-badge--inverted","horn"===e.shape&&"u-badge--horn","u-badge--"+e.type+(e.inverted?"--inverted":"")],style:[e.$u.addStyle(e.customStyle),e.badgeStyle]},[e._v(e._s(e.isDot?"":e.showValue))]):e._e()},a=[]},7710:function(e,t,n){"use strict";var r=n("a53c"),a=n.n(r);a.a},7834:function(e,t,n){"use strict";var r=n("56ac"),a=n.n(r);a.a},"795f":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uvForm",{ref:"uForm",attrs:{model:e.model,rules:e.rules,errorType:e.errorType,borderBottom:e.borderBottom,labelPosition:e.labelPosition,labelWidth:e.labelWidth,labelAlign:e.labelAlign,labelStyle:e.labelStyle,customStyle:e.customStyle}},[e._t("default")],2)},a=[]},7996:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1"),i=n("181d");r({global:!0},{Reflect:{}}),i(a.Reflect,"Reflect",!0)},"7eb0":function(e,t,n){var r=n("5d54");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("60c90f59",r,!0,{sourceMap:!1,shadowMode:!1})},8049:function(e,t,n){"use strict";var r=n("89e8"),a=n.n(r);a.a},"80f4":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-2f0e5305], uni-scroll-view[data-v-2f0e5305], uni-swiper-item[data-v-2f0e5305]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-line[data-v-2f0e5305]{vertical-align:middle}",""]),e.exports=t},8237:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{value:{type:[String,Number],default:uni.$u.props.textarea.value},placeholder:{type:[String,Number],default:uni.$u.props.textarea.placeholder},placeholderClass:{type:String,default:uni.$u.props.input.placeholderClass},placeholderStyle:{type:[String,Object],default:uni.$u.props.input.placeholderStyle},height:{type:[String,Number],default:uni.$u.props.textarea.height},confirmType:{type:String,default:uni.$u.props.textarea.confirmType},disabled:{type:Boolean,default:uni.$u.props.textarea.disabled},count:{type:Boolean,default:uni.$u.props.textarea.count},focus:{type:Boolean,default:uni.$u.props.textarea.focus},autoHeight:{type:Boolean,default:uni.$u.props.textarea.autoHeight},fixed:{type:Boolean,default:uni.$u.props.textarea.fixed},cursorSpacing:{type:Number,default:uni.$u.props.textarea.cursorSpacing},cursor:{type:[String,Number],default:uni.$u.props.textarea.cursor},showConfirmBar:{type:Boolean,default:uni.$u.props.textarea.showConfirmBar},selectionStart:{type:Number,default:uni.$u.props.textarea.selectionStart},selectionEnd:{type:Number,default:uni.$u.props.textarea.selectionEnd},adjustPosition:{type:Boolean,default:uni.$u.props.textarea.adjustPosition},disableDefaultPadding:{type:Boolean,default:uni.$u.props.textarea.disableDefaultPadding},holdKeyboard:{type:Boolean,default:uni.$u.props.textarea.holdKeyboard},maxlength:{type:[String,Number],default:uni.$u.props.textarea.maxlength},border:{type:String,default:uni.$u.props.textarea.border},formatter:{type:[Function,null],default:uni.$u.props.textarea.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}};t.default=r},"825e":function(e,t,n){"use strict";n.r(t);var r=n("544f"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"82ee":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("5c47"),n("0506"),n("c223");var r={computed:{value:function(){var e=this.text,t=this.mode,n=this.format,r=this.href;return"price"===t?(/^\d+(\.\d+)?$/.test(e)||uni.$u.error("金额模式下，text参数需要为金额格式"),uni.$u.test.func(n)?n(e):uni.$u.priceFormat(e,2)):"date"===t?(!uni.$u.test.date(e)&&uni.$u.error("日期模式下，text参数需要为日期或时间戳格式"),uni.$u.test.func(n)?n(e):n?uni.$u.timeFormat(e,n):uni.$u.timeFormat(e,"yyyy-mm-dd")):"phone"===t?uni.$u.test.func(n)?n(e):"encrypt"===n?"".concat(e.substr(0,3),"****").concat(e.substr(7)):e:"name"===t?("string"!==typeof e&&uni.$u.error("姓名模式下，text参数需要为字符串格式"),uni.$u.test.func(n)?n(e):"encrypt"===n?this.formatName(e):e):"link"===t?(!uni.$u.test.url(r)&&uni.$u.error("超链接模式下，href参数需要为URL格式"),e):e}},methods:{formatName:function(e){var t="";if(2===e.length)t=e.substr(0,1)+"*";else if(e.length>2){for(var n="",r=0,a=e.length-2;r<a;r++)n+="*";t=e.substr(0,1)+n+e.substr(-1,1)}else t=e;return t}}};t.default=r},8332:function(e,t,n){"use strict";n.r(t);var r=n("8337"),a=n("3fe8");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("d843");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"2f0e5305",null,!1,r["a"],void 0);t["default"]=s.exports},8337:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"u-line",style:[this.lineStyle]})},a=[]},"833b":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__5F78BD0"}},8565:function(e,t,n){"use strict";n.r(t);var r=n("0fa6"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},8598:function(e,t,n){"use strict";var r=n("bb80"),a=n("7992"),i=n("1c06"),o=n("338c"),s=n("37ad"),u=n("8f26"),c=Function,l=r([].concat),d=r([].join),f={},p=function(e,t,n){if(!o(f,t)){for(var r=[],a=0;a<t;a++)r[a]="a["+a+"]";f[t]=c("C,a","return new C("+d(r,",")+")")}return f[t](e,n)};e.exports=u?c.bind:function(e){var t=a(this),n=t.prototype,r=s(arguments,1),o=function(){var n=l(r,s(arguments));return this instanceof o?p(t,n.length,n):t.apply(e,n)};return i(n)&&(o.prototype=n),o}},"861b":function(e,t,n){"use strict";(function(e,r){var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var i=a(n("f478")),o=a(n("5de6")),s=a(n("fcf3")),u=a(n("b7c7")),c=a(n("3471")),l=a(n("2634")),d=a(n("2fdc")),f=a(n("9b1b")),p=a(n("acb1")),h=a(n("cad9")),v=a(n("68ef")),g=a(n("80b1")),m=a(n("efe5"));n("4085"),n("7a76"),n("c9b5"),n("bf0f"),n("ab80"),n("f7a5"),n("aa9c"),n("e966"),n("c223"),n("dd2b"),n("5ef2"),n("2797"),n("dc8a"),n("473f"),n("4626"),n("5ac7"),n("4100"),n("5c47"),n("d4b5"),n("0c26"),n("0506"),n("fd3c"),n("6a54"),n("a1c1"),n("de6c"),n("c1a3"),n("18f7"),n("af8f"),n("64aa"),n("8f71"),n("23f4"),n("7d2f"),n("9c4e"),n("4db2"),n("c976"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("01a2"),n("e39c"),n("e062"),n("aa77"),n("2c10"),n("f555"),n("dc69"),n("9370"),n("6730"),n("08eb"),n("15d1"),n("d5c6"),n("5a56"),n("f074"),n("20f3");var y=a(n("f3ee"));function b(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var _=b((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},a=r.lib={},i=a.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=a.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,a=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<a;i++){var o=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=o<<24-(r+i)%4*8}else for(i=0;i<a;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=a,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],a=function(t){var n=987654321,r=4294967295;return function(){var a=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return a/=4294967296,(a+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var s=a(4294967296*(n||e.random()));n=987654071*s(),r.push(4294967296*s()|0)}return new o.init(r,t)}}),s=r.enc={},u=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new o.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new o.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=a.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,a=n.sigBytes,i=this.blockSize,s=a/(4*i),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,c=e.min(4*u,a);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(r,l);var d=r.splice(0,u);n.sigBytes-=c}return new o.init(d,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=d.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}});var f=r.algo={};return r}(Math),n)})),x=_,w=(b((function(e,t){var n;e.exports=(n=x,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,o=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=o.MD5=i.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,a=e[r];e[r]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}var i=this._hash.words,o=e[t+0],u=e[t+1],p=e[t+2],h=e[t+3],v=e[t+4],g=e[t+5],m=e[t+6],y=e[t+7],b=e[t+8],_=e[t+9],x=e[t+10],w=e[t+11],k=e[t+12],S=e[t+13],P=e[t+14],I=e[t+15],C=i[0],O=i[1],T=i[2],E=i[3];C=c(C,O,T,E,o,7,s[0]),E=c(E,C,O,T,u,12,s[1]),T=c(T,E,C,O,p,17,s[2]),O=c(O,T,E,C,h,22,s[3]),C=c(C,O,T,E,v,7,s[4]),E=c(E,C,O,T,g,12,s[5]),T=c(T,E,C,O,m,17,s[6]),O=c(O,T,E,C,y,22,s[7]),C=c(C,O,T,E,b,7,s[8]),E=c(E,C,O,T,_,12,s[9]),T=c(T,E,C,O,x,17,s[10]),O=c(O,T,E,C,w,22,s[11]),C=c(C,O,T,E,k,7,s[12]),E=c(E,C,O,T,S,12,s[13]),T=c(T,E,C,O,P,17,s[14]),C=l(C,O=c(O,T,E,C,I,22,s[15]),T,E,u,5,s[16]),E=l(E,C,O,T,m,9,s[17]),T=l(T,E,C,O,w,14,s[18]),O=l(O,T,E,C,o,20,s[19]),C=l(C,O,T,E,g,5,s[20]),E=l(E,C,O,T,x,9,s[21]),T=l(T,E,C,O,I,14,s[22]),O=l(O,T,E,C,v,20,s[23]),C=l(C,O,T,E,_,5,s[24]),E=l(E,C,O,T,P,9,s[25]),T=l(T,E,C,O,h,14,s[26]),O=l(O,T,E,C,b,20,s[27]),C=l(C,O,T,E,S,5,s[28]),E=l(E,C,O,T,p,9,s[29]),T=l(T,E,C,O,y,14,s[30]),C=d(C,O=l(O,T,E,C,k,20,s[31]),T,E,g,4,s[32]),E=d(E,C,O,T,b,11,s[33]),T=d(T,E,C,O,w,16,s[34]),O=d(O,T,E,C,P,23,s[35]),C=d(C,O,T,E,u,4,s[36]),E=d(E,C,O,T,v,11,s[37]),T=d(T,E,C,O,y,16,s[38]),O=d(O,T,E,C,x,23,s[39]),C=d(C,O,T,E,S,4,s[40]),E=d(E,C,O,T,o,11,s[41]),T=d(T,E,C,O,h,16,s[42]),O=d(O,T,E,C,m,23,s[43]),C=d(C,O,T,E,_,4,s[44]),E=d(E,C,O,T,k,11,s[45]),T=d(T,E,C,O,I,16,s[46]),C=f(C,O=d(O,T,E,C,p,23,s[47]),T,E,o,6,s[48]),E=f(E,C,O,T,y,10,s[49]),T=f(T,E,C,O,P,15,s[50]),O=f(O,T,E,C,g,21,s[51]),C=f(C,O,T,E,k,6,s[52]),E=f(E,C,O,T,h,10,s[53]),T=f(T,E,C,O,x,15,s[54]),O=f(O,T,E,C,u,21,s[55]),C=f(C,O,T,E,b,6,s[56]),E=f(E,C,O,T,I,10,s[57]),T=f(T,E,C,O,m,15,s[58]),O=f(O,T,E,C,S,21,s[59]),C=f(C,O,T,E,v,6,s[60]),E=f(E,C,O,T,w,10,s[61]),T=f(T,E,C,O,p,15,s[62]),O=f(O,T,E,C,_,21,s[63]),i[0]=i[0]+C|0,i[1]=i[1]+O|0,i[2]=i[2]+T|0,i[3]=i[3]+E|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;n[a>>>5]|=128<<24-a%32;var i=e.floor(r/4294967296),o=r;n[15+(a+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(a+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,a,i,o){var s=e+(t&n|~t&r)+a+o;return(s<<i|s>>>32-i)+t}function l(e,t,n,r,a,i,o){var s=e+(t&r|n&~r)+a+o;return(s<<i|s>>>32-i)+t}function d(e,t,n,r,a,i,o){var s=e+(t^n^r)+a+o;return(s<<i|s>>>32-i)+t}function f(e,t,n,r,a,i,o){var s=e+(n^(t|~r))+a+o;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(u),t.HmacMD5=i._createHmacHelper(u)}(Math),n.MD5)})),b((function(e,t){var n;e.exports=(n=x,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,a=4*n;t.sigBytes>a&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,u=o.words,c=0;c<n;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=o.sigBytes=a,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),b((function(e,t){e.exports=x.HmacMD5}))),k=b((function(e,t){e.exports=x.enc.Utf8})),S=b((function(e,t){var n;e.exports=(n=x,function(){var e=n,t=e.lib.WordArray;function r(e,n,r){for(var a=[],i=0,o=0;o<n;o++)if(o%4){var s=r[e.charCodeAt(o-1)]<<o%4*2,u=r[e.charCodeAt(o)]>>>6-o%4*2;a[i>>>2]|=(s|u)<<24-i%4*8,i++}return t.create(a,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var a=[],i=0;i<n;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)a.push(r.charAt(o>>>6*(3-s)&63));var u=r.charAt(64);if(u)for(;a.length%4;)a.push(u);return a.join("")},parse:function(e){var t=e.length,n=this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<n.length;i++)a[n.charCodeAt(i)]=i}var o=n.charAt(64);if(o){var s=e.indexOf(o);-1!==s&&(t=s)}return r(e,t,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),P="uni_id_token",I="uni_id_token_expired",C={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},O="pending",T="fulfilled",E="rejected";function $(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function A(e){return"object"===$(e)}function D(e){return"function"==typeof e}function F(e){return function(){try{return e.apply(e,arguments)}catch(e){r.error(e)}}}var j="REJECTED",M="NOT_PENDING",R=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,r=t.retryRule,a=void 0===r?j:r;(0,g.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=a}return(0,m.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case j:return this.status===E;case M:return this.status!==O}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=O,this.promise=this.createPromise().then((function(t){return e.status=T,Promise.resolve(t)}),(function(t){return e.status=E,Promise.reject(t)})),this.promise):this.promise}}]),e}(),L=function(){function e(){(0,g.default)(this,e),this._callback={}}return(0,m.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var r=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];if(t)for(var i=0;i<t.length;i++)t[i].apply(t,r)}}]),e}();function N(e){return e&&"string"==typeof e?JSON.parse(e):e}var U=N([]),q="web",B=(N(void 0),N([])||[]);try{(n("833b").default||n("833b")).appid}catch(br){}var z,H={};function W(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=H,n=e,Object.prototype.hasOwnProperty.call(t,n)||(H[e]=r),H[e]}"app"===q&&(H=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={});var K=["invoke","success","fail","complete"],V=W("_globalUniCloudInterceptor");function J(e,t){V[e]||(V[e]={}),A(t)&&Object.keys(t).forEach((function(n){K.indexOf(n)>-1&&function(e,t,n){var r=V[e][t];r||(r=V[e][t]=[]),-1===r.indexOf(n)&&D(n)&&r.push(n)}(e,n,t[n])}))}function G(e,t){V[e]||(V[e]={}),A(t)?Object.keys(t).forEach((function(n){K.indexOf(n)>-1&&function(e,t,n){var r=V[e][t];if(r){var a=r.indexOf(n);a>-1&&r.splice(a,1)}}(e,n,t[n])})):delete V[e]}function Y(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function Q(e,t){return V[e]&&V[e][t]||[]}function X(e){J("callObject",e)}var Z=W("_globalUniCloudListener"),ee={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},te={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function ne(e){return Z[e]||(Z[e]=[]),Z[e]}function re(e,t){var n=ne(e);n.includes(t)||n.push(t)}function ae(e,t){var n=ne(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function ie(e,t){for(var n=ne(e),r=0;r<n.length;r++)(0,n[r])(t)}var oe,se=!1;function ue(){return oe||(oe=new Promise((function(e){se&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(se=!0,e())}se||setTimeout((function(){t()}),30)}()})),oe)}function ce(e){var t={};for(var n in e){var r=e[n];D(r)&&(t[n]=F(r))}return t}var le=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(e){var r;return(0,g.default)(this,n),r=t.call(this,e.message),r.errMsg=e.message||e.errMsg||"unknown system error",r.code=r.errCode=e.code||e.errCode||"SYSTEM_ERROR",r.errSubject=r.subject=e.subject||e.errSubject,r.cause=e.cause,r.requestId=e.requestId,r}return(0,m.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,v.default)(Error));t.UniCloudError=le;var de,fe,pe={request:function(e){return uni.request(e)},uploadFile:function(e){return uni.uploadFile(e)},setStorageSync:function(e,t){return uni.setStorageSync(e,t)},getStorageSync:function(e){return uni.getStorageSync(e)},removeStorageSync:function(e){return uni.removeStorageSync(e)},clearStorageSync:function(){return uni.clearStorageSync()},connectSocket:function(e){return uni.connectSocket(e)}};function he(){return{token:pe.getStorageSync(P)||pe.getStorageSync("uniIdToken"),tokenExpired:pe.getStorageSync(I)}}function ve(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&pe.setStorageSync(P,t),n&&pe.setStorageSync(I,n)}function ge(){return de||(de=uni.getSystemInfoSync()),de}var me={};function ye(){var e=uni.getLocale&&uni.getLocale()||"en";if(fe)return(0,f.default)((0,f.default)((0,f.default)({},me),fe),{},{locale:e,LOCALE:e});var t=ge(),n=t.deviceId,r=t.osName,a=t.uniPlatform,i=t.appId,o=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var s in t)Object.hasOwnProperty.call(t,s)&&-1===o.indexOf(s)&&delete t[s];return fe=(0,f.default)((0,f.default)({PLATFORM:a,OS:r,APPID:i,DEVICEID:n},function(){var e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=uni.getLaunchOptionsSync(),r=n.scene,a=n.channel;e=a,t=r}}catch(e){}return{channel:e,scene:t}}()),t),(0,f.default)((0,f.default)((0,f.default)({},me),fe),{},{locale:e,LOCALE:e})}var be,_e={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),w(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,r){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var a=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",i=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new le({code:a,message:i,requestId:t}))}var o=e.data;if(o.error)return r(new le({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},toBase64:function(e){return S.stringify(k.parse(e))}},xe=function(){function e(t){var n=this;(0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=pe,this._getAccessTokenPromiseHub=new R({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new le({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:M})}return(0,m.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return _e.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=_e.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=_e.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request((0,f.default)((0,f.default)({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,r=e.formData,a=e.name,i=e.filePath,o=e.fileType,s=e.onUploadProgress;return new Promise((function(e,u){var c=t.adapter.uploadFile({url:n,formData:r,name:a,filePath:i,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):u(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){u(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c,d,f,p,h,v,g,m,y,b,_,x,w,k,S;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=t.fileType,i=void 0===a?"image":a,o=t.cloudPathAsRealPath,s=void 0!==o&&o,u=t.onUploadProgress,c=t.config,"string"===$(r)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(d=c&&c.envType||this.config.envType,!(s&&("/"!==r[0]&&(r="/"+r),r.indexOf("\\")>-1))){e.next=10;break}throw new le({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:d,filename:s?r.split("/").pop():r,fileId:s?r:void 0});case 12:return f=e.sent.result,p="https://"+f.cdnDomain+"/"+f.ossPath,h=f.securityToken,v=f.accessKeyId,g=f.signature,m=f.host,y=f.ossPath,b=f.id,_=f.policy,x=f.ossCallbackUrl,w={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:v,Signature:g,host:m,id:b,key:y,policy:_,success_action_status:200},h&&(w["x-oss-security-token"]=h),x&&(k=JSON.stringify({callbackUrl:x,callbackBody:JSON.stringify({fileId:b,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),w.callback=_e.toBase64(k)),S={url:"https://"+f.host,formData:w,fileName:"file",name:"file",filePath:n,fileType:i},e.next=27,this.uploadFileToOSS(Object.assign({},S,{onUploadProgress:u}));case 27:if(!x){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:p});case 29:return e.next=31,this.reportOSSUpload({id:b});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:p});case 33:throw new le({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList;return new Promise((function(e,n){Array.isArray(t)&&0!==t.length||n(new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e({fileList:t.map((function(e){return{fileID:e,tempFileURL:e}}))})}))}},{key:"getFileInfo",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return r={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(r));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),we={init:function(e){var t=new xe(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},ke="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(be||(be={}));var Se,Pe=function(){},Ie=b((function(e,t){var n;e.exports=(n=x,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,o=t.algo,s=[],u=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,a=0;a<64;)t(r)&&(a<8&&(s[a]=n(e.pow(r,.5))),u[a]=n(e.pow(r,1/3)),a++),r++}();var c=[],l=o.SHA256=i.extend({_doReset:function(){this._hash=new a.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],a=n[1],i=n[2],o=n[3],s=n[4],l=n[5],d=n[6],f=n[7],p=0;p<64;p++){if(p<16)c[p]=0|e[t+p];else{var h=c[p-15],v=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,g=c[p-2],m=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[p]=v+c[p-7]+m+c[p-16]}var y=r&a^r&i^a&i,b=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),_=f+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&d)+u[p]+c[p];f=d,d=l,l=s,s=o+_|0,o=i,i=a,a=r,r=_+(b+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+a|0,n[2]=n[2]+i|0,n[3]=n[3]+o|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+d|0,n[7]=n[7]+f|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;return n[a>>>5]|=128<<24-a%32,n[14+(a+64>>>9<<4)]=e.floor(r/4294967296),n[15+(a+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Ce=Ie,Oe=b((function(e,t){e.exports=x.HmacSHA256})),Te=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new le({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,r){return e?n(e):t(r)}}));return e.promise=n,e};function Ee(e){return void 0===e}function $e(e){return"[object Null]"===Object.prototype.toString.call(e)}function Ae(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function De(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",r=0;r<e;r++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Se||(Se={}));var Fe={adapter:null,runtime:void 0},je=["anonymousUuidKey"],Me=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){var e;return(0,g.default)(this,n),e=t.call(this),Fe.adapter.root.tcbObject||(Fe.adapter.root.tcbObject={}),e}return(0,m.default)(n,[{key:"setItem",value:function(e,t){Fe.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Fe.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Fe.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Fe.adapter.root.tcbObject}}]),n}(Pe);function Re(e,t){switch(e){case"local":return t.localStorage||new Me;case"none":return new Me;default:return t.sessionStorage||new Me}}var Le=function(){function e(t){if((0,g.default)(this,e),!this._storage){this._persistence=Fe.adapter.primaryStorage||t.persistence,this._storage=Re(this._persistence,Fe.adapter);var n="access_token_".concat(t.env),r="access_token_expire_".concat(t.env),a="refresh_token_".concat(t.env),i="anonymous_uuid_".concat(t.env),o="login_type_".concat(t.env),s="token_type_".concat(t.env),u="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:r,refreshTokenKey:a,anonymousUuidKey:i,loginTypeKey:o,userInfoKey:u,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,m.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=Re(e,Fe.adapter);for(var r in this.keys){var a=this.keys[r];if(!t||!je.includes(r)){var i=this._storage.getItem(a);Ee(i)||$e(i)||(n.setItem(a,i),this._storage.removeItem(a))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var r={version:n||"localCachev1",content:t},a=JSON.stringify(r);try{this._storage.setItem(e,a)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),Ne={},Ue={};function qe(e){return Ne[e]}var Be=(0,m.default)((function e(t,n){(0,g.default)(this,e),this.data=n||null,this.name=t})),ze=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(e,r){var a;return(0,g.default)(this,n),a=t.call(this,"error",{error:e,data:r}),a.error=e,a}return(0,m.default)(n)}(Be),He=new(function(){function e(){(0,g.default)(this,e),this._listeners={}}return(0,m.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof ze)return r.error(e.error),this;var n="string"==typeof e?new Be(e,t||{}):e,a=n.name;if(this._listens(a)){n.target=this;var i,o=this._listeners[a]?(0,u.default)(this._listeners[a]):[],s=(0,c.default)(o);try{for(s.s();!(i=s.n()).done;){var l=i.value;l.call(this,n)}}catch(d){s.e(d)}finally{s.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function We(e,t){He.on(e,t)}function Ke(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};He.fire(e,t)}function Ve(e,t){He.off(e,t)}var Je,Ge="loginStateChanged",Ye="loginStateExpire",Qe="loginTypeChanged",Xe="anonymousConverted",Ze="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(Je||(Je={}));var et=function(){function e(){(0,g.default)(this,e),this._fnPromiseMap=new Map}return(0,m.default)(e,[{key:"run",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var r,a=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=this._fnPromiseMap.get(t),e.abrupt("return",(r||(r=new Promise(function(){var e=(0,d.default)((0,l.default)().mark((function e(r,i){var o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a._runIdlePromise();case 3:return o=n(),e.t0=r,e.next=7,o;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),i(e.t2);case 14:return e.prev=14,a._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,r)),r));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),tt=function(){function e(t){(0,g.default)(this,e),this._singlePromise=new et,this._cache=qe(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Fe.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,m.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=De(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var r,a,i,o,s,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=u.length>2&&void 0!==u[2]?u[2]:{},a={"x-request-id":De(),"x-device-id":this._getDeviceId()},!r.withAccessToken){e.next=9;break}return i=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:o=e.sent,s=this._cache.getStore(i),a.authorization="".concat(s," ").concat(o);case 9:return e.abrupt("return",this._reqClass["get"===r.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:a}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s,u,c,f,p=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=t.tokenTypeKey,o=this._cache.getStore(n),!o||o===Je.ANONYMOUS){e.next=3;break}throw new le({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return s=e.sent,u=s.access_token,c=s.expires_in,f=s.token_type,e.abrupt("return",(this._cache.setStore(i,f),this._cache.setStore(r,u),this._cache.setStore(a,Date.now()+1e3*c),u));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=this._cache.getStore(n),i=this._cache.getStore(r),e.abrupt("return",this.isAccessTokenExpired(a,i)?this._fetchAccessToken():a);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,Je.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),nt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],rt={"X-SDK-Version":"1.3.5"};function at(e,t,n){var r=e[t];e[t]=function(t){var a={},i={};n.forEach((function(n){var r=n.call(e,t),o=r.data,s=r.headers;Object.assign(a,o),Object.assign(i,s)}));var o=t.data;return o&&function(){var e;if(e=o,"[object FormData]"!==Object.prototype.toString.call(e))t.data=(0,f.default)((0,f.default)({},o),a);else for(var n in a)o.append(n,a[n])}(),t.headers=(0,f.default)((0,f.default)({},t.headers||{}),i),r.call(e,t)}}function it(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:(0,f.default)((0,f.default)({},rt),{},{"x-seqid":e})}}var ot=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,g.default)(this,e),this.config=n,this._reqClass=new Fe.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=qe(this.config.env),this._localCache=(t=this.config.env,Ue[t]),this.oauth=new tt(this.config),at(this._reqClass,"post",[it]),at(this._reqClass,"upload",[it]),at(this._reqClass,"download",[it])}return(0,m.default)(e,[{key:"post",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s,u,c,d,f,p,h;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,i=t.loginTypeKey,o=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(r),s=this._cache.getStore(a),s){e.next=5;break}throw new le({message:"未登录CloudBase"});case 5:return u={refresh_token:s},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",u);case 8:if(c=e.sent,!c.data.code){e.next=21;break}if(d=c.data.code,"SIGN_PARAM_INVALID"!==d&&"REFRESH_TOKEN_EXPIRED"!==d&&"INVALID_REFRESH_TOKEN"!==d){e.next=20;break}if(this._cache.getStore(i)!==Je.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==d){e.next=19;break}return f=this._cache.getStore(o),p=this._cache.getStore(a),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:f,refresh_token:p});case 17:return h=e.sent,e.abrupt("return",(this.setRefreshToken(h.refresh_token),this._refreshAccessToken()));case 19:Ke(Ye),this._cache.removeStore(a);case 20:throw new le({code:c.data.code,message:"刷新access token失败：".concat(c.data.code)});case 21:if(!c.data.access_token){e.next=23;break}return e.abrupt("return",(Ke(Ze),this._cache.setStore(n,c.data.access_token),this._cache.setStore(r,c.data.access_token_expire+Date.now()),{accessToken:c.data.access_token,accessTokenExpire:c.data.access_token_expire}));case 23:c.data.refresh_token&&(this._cache.removeStore(a),this._cache.setStore(a,c.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,this._cache.getStore(a)){e.next=3;break}throw new le({message:"refresh token不存在，登录状态异常"});case 3:if(i=this._cache.getStore(n),o=this._cache.getStore(r),s=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(i,o);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}s=!1;case 12:return e.abrupt("return",(!i||!o||o<Date.now())&&s?this.refreshAccessToken():{accessToken:i,accessTokenExpire:o});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n,r){var a,i,o,s,u,c,d,p,h,v,g,m,y,b,_;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a="x-tcb-trace_".concat(this.config.env),i="application/x-www-form-urlencoded",o=(0,f.default)({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===nt.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:o.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(u in s=new FormData,s)s.hasOwnProperty(u)&&void 0!==s[u]&&s.append(u,o[u]);i="multipart/form-data",e.next=17;break;case 15:for(c in i="application/json",s={},o)void 0!==o[c]&&(s[c]=o[c]);case 17:return d={headers:{"content-type":i}},r&&r.timeout&&(d.timeout=r.timeout),r&&r.onUploadProgress&&(d.onUploadProgress=r.onUploadProgress),p=this._localCache.getStore(a),p&&(d.headers["X-TCB-Trace"]=p),h=n.parse,v=n.inQuery,g=n.search,m={env:this.config.env},h&&(m.parse=!0),v&&(m=(0,f.default)((0,f.default)({},v),m)),y=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+="".concat(i,"=").concat(encodeURIComponent(n[i]));return/^http(s)?\:\/\//.test(t+=a)?t:"".concat(e).concat(t)}(ke,"//tcb-api.tencentcloudapi.com/web",m),g&&(y+=g),e.next=28,this.post((0,f.default)({url:y,data:s},d));case 28:if(b=e.sent,_=b.header&&b.header["x-tcb-trace"],_&&this._localCache.setStore(a,_),(200===Number(b.status)||200===Number(b.statusCode))&&b.data){e.next=32;break}throw new le({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",b);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r,a,i,o=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.next=4,this.request(t,n,(0,f.default)((0,f.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 4:if(a=e.sent,"ACCESS_TOKEN_DISABLED"!==a.data.code&&"ACCESS_TOKEN_EXPIRED"!==a.data.code||-1!==nt.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,(0,f.default)((0,f.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 10:if(i=e.sent,!i.data.code){e.next=13;break}throw new le({code:i.data.code,message:Ae(i.data.message)});case 13:return e.abrupt("return",i.data);case 14:if(!a.data.code){e.next=16;break}throw new le({code:a.data.code,message:Ae(a.data.message)});case 16:return e.abrupt("return",a.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}}]),e}(),st={};function ut(e){return st[e]}var ct=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=qe(t.env),this._request=ut(t.env)}return(0,m.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,r=n.accessTokenKey,a=n.accessTokenExpireKey;this._cache.setStore(r,e),this._cache.setStore(a,t)}},{key:"refreshUserInfo",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),lt=function(){function e(t){if((0,g.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=qe(this._envId),this._request=ut(this._envId),this.setUserInfo()}return(0,m.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,r=!1,a=n.users,e.abrupt("return",(a.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(r=!0)})),{users:a,hasPrimaryUid:r}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,r=t.gender,a=t.avatarUrl,i=t.province,o=t.country,s=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:r,avatarUrl:a,province:i,country:o,city:s});case 8:u=e.sent,c=u.data,this.setLocalUserInfo(c);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),dt=function(){function e(t){if((0,g.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=qe(t);var n=this._cache.keys,r=n.refreshTokenKey,a=n.accessTokenKey,i=n.accessTokenExpireKey,o=this._cache.getStore(r),s=this._cache.getStore(a),u=this._cache.getStore(i);this.credential={refreshToken:o,accessToken:s,accessTokenExpire:u},this.user=new lt(t)}return(0,m.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===Je.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===Je.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===Je.WECHAT||this.loginType===Je.WECHAT_OPEN||this.loginType===Je.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),ft=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return Ke(Ge),Ke(Qe,{env:this.config.env,loginType:Je.ANONYMOUS,persistence:"local"}),t=new dt(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,r=n.anonymousUuidKey,a=n.refreshTokenKey,i=this._cache.getStore(r),o=this._cache.getStore(a),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:i,refresh_token:o,ticket:t});case 7:if(s=e.sent,!s.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(s.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return Ke(Xe,{env:this.config.env}),Ke(Qe,{loginType:Je.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:s.refresh_token}});case 16:throw new le({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,r=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(r,Je.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(ct),pt=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(r=e.sent,!r.refresh_token){e.next=15;break}return this.setRefreshToken(r.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return Ke(Ge),Ke(Qe,{env:this.config.env,loginType:Je.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new dt(this.config.env));case 15:throw new le({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(ct),ht=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var r,a,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"email must be a string"});case 2:return r=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(r)||""});case 5:if(a=e.sent,i=a.refresh_token,o=a.access_token,s=a.access_token_expire,!i){e.next=22;break}if(this.setRefreshToken(i),!o||!s){e.next=15;break}this.setAccessToken(o,s),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return Ke(Ge),Ke(Qe,{env:this.config.env,loginType:Je.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new dt(this.config.env));case 22:throw a.code?new le({code:a.code,message:"邮箱登录失败: ".concat(a.message)}):new le({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),vt=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var a,i,o,s,u;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",r.warn("password is empty")),a=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:Je.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(a)||""});case 6:if(i=e.sent,o=i.refresh_token,s=i.access_token_expire,u=i.access_token,!o){e.next=23;break}if(this.setRefreshToken(o),!u||!s){e.next=16;break}this.setAccessToken(u,s),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return Ke(Ge),Ke(Qe,{env:this.config.env,loginType:Je.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new dt(this.config.env));case 23:throw i.code?new le({code:i.code,message:"用户名密码登录失败: ".concat(i.message)}):new le({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),gt=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=qe(t.env),this._request=ut(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),We(Qe,this._onLoginTypeChanged)}return(0,m.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new ft(this.config)}},{key:"customAuthProvider",value:function(){return new pt(this.config)}},{key:"emailAuthProvider",value:function(){return new ht(this.config)}},{key:"usernameAuthProvider",value:function(){return new vt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ft(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ht(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new vt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new ft(this.config)),We(Xe,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==Je.ANONYMOUS){e.next=2;break}throw new le({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=this._cache.getStore(n),i){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:i});case 7:return o=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.removeStore(a),Ke(Ge),Ke(Qe,{env:this.config.env,loginType:Je.NULL,persistence:this.config.persistence}),o));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;We(Ge,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){We(Ye,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){We(Ze,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){We(Xe,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;We(Qe,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,r=this._cache.getStore(t),a=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(r,a)?null:new dt(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,r=n.data,e.abrupt("return",r&&r.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new pt(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:(0,f.default)((0,f.default)({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,r=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+r}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,r=t.persistence,a=t.env;a===this.config.env&&(this._cache.updatePersistence(r),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),mt=function(e,t){t=t||Te();var n=ut(this.config.env),r=e.cloudPath,a=e.filePath,i=e.onUploadProgress,o=e.fileType,s=void 0===o?"image":o;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){var o=e.data,u=o.url,c=o.authorization,l=o.token,d=o.fileId,f=o.cosFileId,p=e.requestId,h={key:r,signature:c,"x-cos-meta-fileid":f,success_action_status:"201","x-cos-security-token":l};n.upload({url:u,data:h,file:a,name:r,fileType:s,onUploadProgress:i}).then((function(e){201===e.statusCode?t(null,{fileID:d,requestId:p}):t(new le({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},yt=function(e,t){t=t||Te();var n=ut(this.config.env),r=e.cloudPath;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},bt=function(e,t){var n=e.fileList;if(t=t||Te(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var r,a=(0,c.default)(n);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(!i||"string"!=typeof i)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){a.e(s)}finally{a.f()}var o={fileid_list:n};return ut(this.config.env).send("storage.batchDeleteFile",o).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},_t=function(e,t){var n=e.fileList;t=t||Te(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var r,a=[],i=(0,c.default)(n);try{for(i.s();!(r=i.n()).done;){var o=r.value;"object"==(0,s.default)(o)?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),a.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?a.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(l){i.e(l)}finally{i.f()}var u={file_list:a};return ut(this.config.env).send("storage.batchGetDownloadUrl",u).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},xt=function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var r,a,i,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.fileID,e.next=3,_t.call(this,{fileList:[{fileID:r,maxAge:600}]});case 3:if(a=e.sent.fileList[0],"SUCCESS"===a.code){e.next=6;break}return e.abrupt("return",n?n(a):new Promise((function(e){e(a)})));case 6:if(i=ut(this.config.env),o=a.download_url,o=encodeURI(o),n){e.next=10;break}return e.abrupt("return",i.download({url:o}));case 10:return e.t0=n,e.next=13,i.download({url:o});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),wt=function(e,t){var n,r=e.name,a=e.data,i=e.query,o=e.parse,s=e.search,u=e.timeout,c=t||Te();try{n=a?JSON.stringify(a):""}catch(r){return Promise.reject(r)}if(!r)return Promise.reject(new le({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:i,parse:o,search:s,function_name:r,request_data:n};return ut(this.config.env).send("functions.invokeFunction",l,{timeout:u}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(o)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new le({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},kt={timeout:15e3,persistence:"session"},St={},Pt=function(){function e(t){(0,g.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,m.default)(e,[{key:"init",value:function(t){switch(Fe.adapter||(this.requestClient=new Fe.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=(0,f.default)((0,f.default)({},kt),t),!0){case this.config.timeout>6e5:r.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:r.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,r=t||Fe.adapter.primaryStorage||kt.persistence;return r!==this.config.persistence&&(this.config.persistence=r),function(e){var t=e.env;Ne[t]=new Le(e),Ue[t]=new Le((0,f.default)((0,f.default)({},e),{},{persistence:"local"}))}(this.config),n=this.config,st[n.env]=new ot(n),this.authObj=new gt(this.config),this.authObj}},{key:"on",value:function(e,t){return We.apply(this,[e,t])}},{key:"off",value:function(e,t){return Ve.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return wt.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return bt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return _t.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return xt.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return mt.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return yt.apply(this,[e,t])}},{key:"registerExtension",value:function(e){St[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=St[t],r){e.next=3;break}throw new le({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,r.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,r=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),a=(0,c.default)(r);try{for(a.s();!(n=a.n()).done;){var i=n.value,o=i.isMatch,s=i.genAdapter,u=i.runtime;if(o())return{adapter:s(),runtime:u}}}catch(l){a.e(l)}finally{a.f()}}(e)||{},n=t.adapter,r=t.runtime;n&&(Fe.adapter=n),r&&(Fe.runtime=r)}}]),e}(),It=new Pt;function Ct(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=a)?t:""+e+t}var Ot=function(){function e(){(0,g.default)(this,e)}return(0,m.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){pe.request({url:Ct("https:",t),data:n,method:"GET",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){pe.request({url:Ct("https:",t),data:n,method:"POST",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var r=e.url,a=e.file,i=e.data,o=e.headers,s=e.fileType,u=pe.uploadFile({url:Ct("https:",r),name:"file",formData:Object.assign({},i),filePath:a,fileType:s,header:o,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Tt={setItem:function(e,t){pe.setStorageSync(e,t)},getItem:function(e){return pe.getStorageSync(e)},removeItem:function(e){pe.removeStorageSync(e)},clear:function(){pe.clearStorageSync()}},Et={genAdapter:function(){return{root:{},reqClass:Ot,localStorage:Tt,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};It.useAdapters(Et);var $t=It,At=$t.init;$t.init=function(e){e.env=e.spaceId;var t=At.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=ce(e),r=t.success,a=t.fail,i=t.complete;if(!(r||a||i))return n.call(this,e);n.call(this,e).then((function(e){r&&r(e),i&&i(e)}),(function(e){a&&a(e),i&&i(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Dt=$t;function Ft(e,t){return jt.apply(this,arguments)}function jt(){return jt=(0,d.default)((0,l.default)().mark((function e(t,n){var r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,i={url:r,timeout:500},new Promise((function(e,t){pe.request((0,f.default)((0,f.default)({},i),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return a=e.sent,e.abrupt("return",!(!a.data||0!==a.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),jt.apply(this,arguments)}function Mt(e,t){return Rt.apply(this,arguments)}function Rt(){return Rt=(0,d.default)((0,l.default)().mark((function e(t,n){var r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=0;case 1:if(!(a<t.length)){e.next=11;break}return i=t[a],e.next=5,Ft(i,n);case 5:if(!e.sent){e.next=8;break}return r=i,e.abrupt("break",11);case 8:a++,e.next=1;break;case 11:return e.abrupt("return",{address:r,port:n});case 12:case"end":return e.stop()}}),e)}))),Rt.apply(this,arguments)}var Lt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},Nt=function(){function e(t){if((0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=pe}return(0,m.default)(e,[{key:"request",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r=this,a=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(a.length>1&&void 0!==a[1])||a[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?r.requestLocal(t):_e.wrappedRequest(t,r.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,r){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",a=e.data&&e.data.message||"request:fail";return r(new le({code:t,message:a}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=_e.sign(t,this.config.clientSecret);var r=ye();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));var a=he(),i=a.token;return n["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c,d;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=ye(),r=he(),a=r.token,i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:a}),o=this.__dev__&&this.__dev__.debugInfo||{},s=o.address,u=o.servePort,e.next=9,Mt(s,u);case 9:return c=e.sent,d=c.address,e.abrupt("return",{url:"http://".concat(d,":").concat(u,"/").concat(Lt[t.method]),method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,r=e.filePath,a=e.cloudPath,i=e.fileType,o=void 0===i?"image":i,s=e.onUploadProgress;if(!a)throw new le({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:a}).then((function(e){var a=e.result,i=a.url,u=a.formData,c=a.name;return t=e.result.fileUrl,new Promise((function(e,t){var a=n.adapter.uploadFile({url:i,formData:u,name:c,filePath:r,fileType:o,success:function(n){n&&n.statusCode<400?e(n):t(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:a})})).then((function(e){return new Promise((function(n,a){e.success?n({success:!0,filePath:r,fileID:t}):a(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new le({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var r={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(r).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new le({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),Ut={init:function(e){var t=new Nt(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},qt=b((function(e,t){e.exports=x.enc.Hex}));function Bt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,r=t.functionName,a=t.method,i=t.headers,s=t.signHeaderKeys,u=void 0===s?[]:s,c=t.config,l=String(Date.now()),d=Bt(),f=Object.assign({},i,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":l,"x-from-function-name":r,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":d,"x-alipay-callid":d,"x-trace-id":d}),p=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(u),h=e.split("?")||[],v=(0,o.default)(h,2),g=v[0],m=void 0===g?"":g,y=v[1],b=void 0===y?"":y,_=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),r=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),a=Ce(e.body).toString(qt),i="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(r,"\n").concat(n,"\n").concat(a,"\n"),o=Ce(i).toString(qt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(o,"\n"),u=Oe(s,e.secretKey).toString(qt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(u)}({path:m,query:b,method:a,headers:f,timestamp:l,body:JSON.stringify(n),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:p.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},f,{Authorization:_})}}function Ht(e){var t=e.url,n=e.data,r=e.method,a=void 0===r?"POST":r,i=e.headers,o=void 0===i?{}:i,u=e.timeout;return new Promise((function(e,r){pe.request({url:t,method:a,data:"object"==(0,s.default)(n)?JSON.stringify(n):n,header:o,dataType:"json",timeout:u,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=o["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var a=t.data||{},i=a.message,s=a.errMsg,u=a.trace_id;return r(new le({code:"SYS_ERR",message:i||s||"request:fail",requestId:u||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Wt(e,t){var n=e.path,r=e.data,a=e.method,i=void 0===a?"GET":a,o=zt(n,{functionName:"",data:r,method:i,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=o.url,u=o.headers;return Ht({url:s,data:r,method:i,headers:u}).then((function(e){var t=e.data||{};if(!t.success)throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Kt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new le({code:"INVALID_PARAM",message:"fileID不合法"});var a=t.substring(0,n),i=t.substring(n+1);return a!==this.config.spaceId&&r.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),i}function Vt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var Jt=function(){function e(t){(0,g.default)(this,e),this.config=t}return(0,m.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),a=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Bt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return a[e]?"".concat(e,"=").concat(a[e]):null})).filter(Boolean).join("&"),"host:".concat(r)].join("\n"),o=["HMAC-SHA256",Ce(i).toString(qt)].join("\n"),s=Oe(o,this.config.secretKey).toString(qt),u=Object.keys(a).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(a[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(u,"&signature=").concat(s)}}]),e}(),Gt=function(){function e(t){if((0,g.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new Jt(this.config)}return(0,m.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,r=e.data,a=e.async,i=void 0!==a&&a,o=e.timeout,s="POST",u={"x-to-function-name":n};i&&(u["x-function-invoke-type"]="async");var c=zt("/functions/invokeFunction",{functionName:n,data:r,method:s,headers:u,signHeaderKeys:["x-to-function-name"],config:t}),l=c.url,d=c.headers;return Ht({url:l,data:r,method:s,headers:d,timeout:o}).then((function(e){var t=0;if(i){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new le({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,r=e.fileType,a=e.formData,i=e.onUploadProgress;return new Promise((function(e,o){var s=pe.uploadFile({url:t,filePath:n,fileType:r,formData:a,name:"file",success:function(t){t&&t.statusCode<400?e(t):o(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){o(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c,d,f,p;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=void 0===r?"":r,i=t.fileType,o=void 0===i?"image":i,s=t.onUploadProgress,"string"===$(a)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(a=a.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(a)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Wt({path:"/".concat(a.replace(/^\//,""),"?post_url")},this.config);case 9:return u=e.sent,c=u.file_id,d=u.upload_url,f=u.form_data,p=f&&f.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:d,filePath:n,fileType:o,formData:p,onUploadProgress:s}).then((function(){return{fileID:c}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var i,o=[],s=(0,c.default)(n);try{for(s.s();!(i=s.n()).done;){var u=i.value,l=void 0;"string"!==$(u)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{l=Kt.call(a,u)}catch(e){r.warn(e.errCode,e.errMsg),l=u}o.push({file_id:l,expire:600})}}catch(d){s.e(d)}finally{s.f()}Wt({path:"/?download_url",data:{file_list:o},method:"POST"},a.config).then((function(t){var n=t.file_list,r=void 0===n?[]:n;e({fileList:r.map((function(e){return{fileID:Vt.call(a,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,r=t.query,e.abrupt("return",pe.connectSocket({url:this._websocket.signedURL(n,r),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Yt={init:function(e){e.provider="alipay";var t=new Gt(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Qt(e){var t,n=e.data;t=ye();var r=JSON.parse(JSON.stringify(n||{}));if(Object.assign(r,{clientInfo:t}),!r.uniIdToken){var a=he(),i=a.token;i&&(r.uniIdToken=i)}return r}var Xt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],Zt=/[\\^$.*+?()[\]{}|]/g,en=RegExp(Zt.source);function tn(e,t,n){return e.replace(new RegExp((r=t)&&en.test(r)?r.replace(Zt,"\\$&"):r,"g"),n);var r}var nn={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},rn="_globalUniCloudStatus",an="_globalUniCloudSecureNetworkCache__{spaceId}";var on;on="0123456789abcdef";var sn="uni-secure-network",un={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function cn(e){var t=e||{},n=t.errSubject,r=t.subject,a=t.errCode,i=t.errMsg,o=t.code,s=t.message,u=t.cause;return new le({subject:n||r||sn,code:a||o||un.SYSTEM_ERROR.code,message:i||s,cause:u})}var ln;function dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===nn.REQUEST||t===nn.RESPONSE||t===nn.BOTH}function fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,r=void 0===n?{}:n;return"app"===q&&"DCloud-clientDB"===t&&"encryption"===r.redirectTo&&"getAppClientKey"===r.action}function pn(e){e.functionName,e.result,e.logPvd}function hn(e){var t=e.callFunction,n=function(n){var r=this,a=n.name;n.data=Qt.call(e,{data:n.data});var i={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],o=dn(n),s=fn(n),u=o||s;return t.call(this,n).then((function(e){return e.errCode=0,!u&&pn.call(r,{functionName:a,result:e,logPvd:i}),Promise.resolve(e)}),(function(e){return!u&&pn.call(r,{functionName:a,result:e,logPvd:i}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,r=e.extraInfo,a=void 0===r?{}:r,i=e.formatter,o=void 0===i?[]:i,s=0;s<o.length;s++){var u=o[s],c=u.rule,l=u.content,d=u.mode,f=n.match(c);if(f){for(var p=l,h=1;h<f.length;h++)p=tn(p,"{$".concat(h,"}"),f[h]);for(var v in a)p=tn(p,"{".concat(v,"}"),a[v]);return"replace"===d?p:n+p}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:Xt,extraInfo:{functionName:a}})),Promise.reject(e)}))};e.callFunction=function(t){var a,i,o=e.config,s=o.provider,u=o.spaceId,c=t.name;return t.data=t.data||{},a=n,a=a.bind(e),i=fn(t)?n.call(e,t):function(e){var t=e.name,n=e.data,r=void 0===n?{}:n;return"mp-weixin"===q&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===r.method}(t)?a.call(e,t):dn(t)?new ln({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,a=e.functionName,i=ge(),o=i.appId,s=i.uniPlatform,u=i.osName,c=s;"app"===s&&(c=u);var l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=U;if(!r)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var a=r.find((function(e){return e.provider===t&&e.spaceId===n}));return a&&a.config}({provider:t,spaceId:n});if(!l||!l.accessControl||!l.accessControl.enable)return!1;var d=l.accessControl.function||{},f=Object.keys(d);if(0===f.length)return!0;var p=function(e,t){for(var n,r,a,i=0;i<e.length;i++){var o=e[i];o!==t?"*"!==o?o.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(r=o):a=o:n=o}return n||r||a}(f,a);if(!p)return!1;if((d[p]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===o&&(e.platform||"").toLowerCase()===c.toLowerCase()})))return!0;throw r.error("此应用[appId: ".concat(o,", platform: ").concat(c,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),cn(un.APP_INFO_INVALID)}({provider:s,spaceId:u,functionName:c})?new ln({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):a(t),Object.defineProperty(i,"result",{get:function(){return r.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),i.then((function(e){return"undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e}))}}ln="mp-weixin"!==q&&"app"!==q?function(){return(0,m.default)((function e(){throw(0,g.default)(this,e),cn({message:"Platform ".concat(q," is not supported by secure network")})}))}():function(){return(0,m.default)((function e(){throw(0,g.default)(this,e),cn({message:"Platform ".concat(q," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var vn=Symbol("CLIENT_DB_INTERNAL");function gn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=vn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,r){if("_uniClient"===n)return null;if("symbol"==(0,s.default)(n))return e[n];if(n in e||"string"!=typeof n){var a=e[n];return"function"==typeof a?a.bind(e):a}return t.get(e,n,r)}})}function mn(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}var yn=["db.Geo","db.command","command.aggregate"];function bn(e,t){return yn.indexOf("".concat(e,".").concat(t))>-1}function _n(e){switch($(e)){case"array":return e.map((function(e){return _n(e)}));case"object":return e._internalType===vn||Object.keys(e).forEach((function(t){e[t]=_n(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function xn(e){return e&&e.content&&e.content.$method}var wn=function(){function e(t,n,r){(0,g.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=r}return(0,m.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:_n(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=xn(e),n=xn(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===xn(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=xn(e),n=xn(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return kn({$method:e,$param:_n(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:_n(t)}),this._database._callCloudFunction({action:n,command:r})}}]),e}();function kn(e,t,n){return gn(new wn(e,t,n),{get:function(e,t){var r="db";return e&&e.content&&(r=e.content.$method),bn(r,t)?kn({$method:t},e,n):function(){return kn({$method:t,$param:_n(Array.from(arguments))},e,n)}}})}function Sn(e){var t=e.path,n=e.method;return function(){function e(){(0,g.default)(this,e),this.param=Array.from(arguments)}return(0,m.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,u.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Pn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,r=void 0===n?{}:n,a=t.isJQL,i=void 0!==a&&a;(0,g.default)(this,e),this._uniClient=r,this._authCallBacks={},this._dbCallBacks={},r._isDefault&&(this._dbCallBacks=W("_globalUniCloudDatabaseCallback")),i||(this.auth=mn(this._authCallBacks)),this._isJQL=i,Object.assign(this,mn(this._dbCallBacks)),this.env=gn({},{get:function(e,t){return{$env:t}}}),this.Geo=gn({},{get:function(e,t){return Sn({path:["Geo"],method:t})}}),this.serverDate=Sn({path:[],method:"serverDate"}),this.RegExp=Sn({path:[],method:"RegExp"})}return(0,m.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function In(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return gn(new e(t),{get:function(e,t){return bn("db",t)?kn({$method:t},null,e):function(){return kn({$method:t,$param:_n(Array.from(arguments))},null,e)}}})}var Cn=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,a=e.command,i=e.multiCommand,o=e.queryList;function s(e,t){if(i&&o)for(var n=0;n<o.length;n++){var r=o[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}var u=this,c=this._isJQL?"databaseForJQL":"database";function l(e){return u._callback("error",[e]),Y(Q(c,"fail"),e).then((function(){return Y(Q(c,"complete"),e)})).then((function(){return s(null,e),ie(ee.RESPONSE,{type:te.CLIENT_DB,content:e}),Promise.reject(e)}))}var d=Y(Q(c,"invoke")),f=this._uniClient;return d.then((function(){return f.callFunction({name:"DCloud-clientDB",type:C.CLIENT_DB,data:{action:n,command:a,multiCommand:i}})})).then((function(e){var n=e.result,a=n.code,i=n.message,o=n.token,d=n.tokenExpired,f=n.systemInfo,p=void 0===f?[]:f;if(p)for(var h=0;h<p.length;h++){var v=p[h],g=v.level,m=v.message,y=v.detail,b="[System Info]"+m;y&&(b="".concat(b,"\n详细信息：").concat(y)),(r["app"===q&&"warn"===g?"error":g]||r.log)(b)}if(a)return l(new le({code:a,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,o&&d&&(ve({token:o,tokenExpired:d}),t._callbackAuth("refreshToken",[{token:o,tokenExpired:d}]),t._callback("refreshToken",[{token:o,tokenExpired:d}]),ie(ee.REFRESH_TOKEN,{token:o,tokenExpired:d}));for(var _=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],x=function(t){var n=_[t],a=n.prop,i=n.tips;if(a in e.result){var o=e.result[a];Object.defineProperty(e.result,a,{get:function(){return r.warn(i),o}})}},w=0;w<_.length;w++)x(w);return function(e){return Y(Q(c,"success"),e).then((function(){return Y(Q(c,"complete"),e)})).then((function(){s(e,null);var t=u._parseResult(e);return ie(ee.RESPONSE,{type:te.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&r.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),l(new le({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Pn),On="token无效，跳转登录页面",Tn="token过期，跳转登录页面",En={TOKEN_INVALID_TOKEN_EXPIRED:Tn,TOKEN_INVALID_INVALID_CLIENTID:On,TOKEN_INVALID:On,TOKEN_INVALID_WRONG_TOKEN:On,TOKEN_INVALID_ANONYMOUS_USER:On},$n={"uni-id-token-expired":Tn,"uni-id-check-token-failed":On,"uni-id-token-not-exist":On,"uni-id-check-device-feature-failed":On};function An(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function Dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],r=[];return e.forEach((function(e){!0===e.needLogin?n.push(An(t,e.path)):!1===e.needLogin&&r.push(An(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function Fn(e){return e.split("?")[0].replace(/^\//,"")}function jn(){return function(e){var t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Mn(){return Fn(jn())}function Rn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,r=Fn(e);return n.some((function(e){return e.pagePath===r}))}var Ln,Nn=!!y.default.uniIdRouter,Un=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y.default,t=e.pages,n=void 0===t?[]:t,r=e.subPackages,a=void 0===r?[]:r,i=e.uniIdRouter,o=void 0===i?{}:i,s=e.tabBar,c=void 0===s?{}:s,l=o.loginPage,d=o.needLogin,f=void 0===d?[]:d,p=o.resToLogin,h=void 0===p||p,v=Dn(n),g=v.needLoginPage,m=v.notNeedLoginPage,b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var r=e.root,a=e.pages,i=void 0===a?[]:a,o=Dn(i,r),s=o.needLoginPage,c=o.notNeedLoginPage;t.push.apply(t,(0,u.default)(s)),n.push.apply(n,(0,u.default)(c))})),{needLoginPage:t,notNeedLoginPage:n}}(a),_=b.needLoginPage,x=b.notNeedLoginPage;return{loginPage:l,routerNeedLogin:f,resToLogin:h,needLoginPage:[].concat((0,u.default)(g),(0,u.default)(_)),notNeedLoginPage:[].concat((0,u.default)(m),(0,u.default)(x)),loginPageInTabBar:Rn(l,c)}}(),qn=Un.loginPage,Bn=Un.routerNeedLogin,zn=Un.resToLogin,Hn=Un.needLoginPage,Wn=Un.notNeedLoginPage,Kn=Un.loginPageInTabBar;if(Hn.indexOf(qn)>-1)throw new Error("Login page [".concat(qn,'] should not be "needLogin", please check your pages.json'));function Vn(e){var t=Mn();if("/"===e.charAt(0))return e;var n=e.split("?"),r=(0,o.default)(n,2),a=r[0],i=r[1],s=a.replace(/^\//,"").split("/"),u=t.split("/");u.pop();for(var c=0;c<s.length;c++){var l=s[c];".."===l?u.pop():"."!==l&&u.push(l)}return""===u[0]&&u.shift(),"/"+u.join("/")+(i?"?"+i:"")}function Jn(e){var t=Fn(Vn(e));return!(Wn.indexOf(t)>-1)&&(Hn.indexOf(t)>-1||Bn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function Gn(e){var t=e.redirect,n=Fn(t),r=Fn(qn);return Mn()!==r&&n!==r}function Yn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&Gn({redirect:n})){var r=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(qn,n);Kn?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var a={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((function(){a[t]({url:r})}),0)}}function Qn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},r=function(){var e,t=he(),n=t.token,r=t.tokenExpired;if(n){if(r<Date.now()){var a="uni-id-token-expired";e={errCode:a,errMsg:$n[a]}}}else{var i="uni-id-check-token-failed";e={errCode:i,errMsg:$n[i]}}return e}();if(Jn(t)&&r){if(r.uniIdRedirectUrl=t,ne(ee.NEED_LOGIN).length>0)return setTimeout((function(){ie(ee.NEED_LOGIN,r)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function Xn(){!function(){var e=jn(),t=Qn({url:e}),n=t.abortLoginPageJump,r=t.autoToLoginPage;n||r&&Yn({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];uni.addInterceptor(n,{invoke:function(e){var t=Qn({url:e.url}),r=t.abortLoginPageJump,a=t.autoToLoginPage;return r?e:a?(Yn({api:n,redirect:Vn(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function Zn(){this.onResponse((function(e){var t=e.type,n=e.content,r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=(0,s.default)(e))return!1;var t=e||{},n=t.errCode;return n in $n}(n);break;case"clientdb":r=function(e){if("object"!=(0,s.default)(e))return!1;var t=e||{},n=t.errCode;return n in En}(n)}r&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ne(ee.NEED_LOGIN);ue().then((function(){var n=jn();if(n&&Gn({redirect:n}))return t.length>0?ie(ee.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(qn&&Yn({api:"navigateTo",redirect:n}))}))}(n)}))}function er(e){!function(e){e.onResponse=function(e){re(ee.RESPONSE,e)},e.offResponse=function(e){ae(ee.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){re(ee.NEED_LOGIN,e)},e.offNeedLogin=function(e){ae(ee.NEED_LOGIN,e)},Nn&&(W(rn).needLoginInit||(W(rn).needLoginInit=!0,ue().then((function(){Xn.call(e)})),zn&&Zn.call(e)))}(e),function(e){e.onRefreshToken=function(e){re(ee.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){ae(ee.REFRESH_TOKEN,e)}}(e)}var tr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",nr=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function rr(){var e,t,n=he().token||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=r[1],decodeURIComponent(Ln(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}Ln="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!nr.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,a="",i=0;i<e.length;)t=tr.indexOf(e.charAt(i++))<<18|tr.indexOf(e.charAt(i++))<<12|(n=tr.indexOf(e.charAt(i++)))<<6|(r=tr.indexOf(e.charAt(i++))),a+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return a}:atob;var ar=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",r="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function i(e,t,r){var a=r.onChooseFile,i=r.onUploadProgress;return t.then((function(e){if(a){var t=a(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var i=t.tempFiles,o=i.length,s=0;return new Promise((function(n){for(;s<r;)u();function u(){var r=s++;if(r>=o)!i.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var c=i[r];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=r,e.tempFile=c,e.tempFilePath=c.path,a&&a(e)}}).then((function(e){c.url=e.fileID,r<o&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,r<o&&u()}))}}}))}(e,t,5,i)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?i(e,function(e){var t=e.count,n=e.sizeType,i=e.sourceType,o=void 0===i?["album","camera"]:i,s=e.extension;return new Promise((function(e,i){uni.chooseImage({count:t,sizeType:n,sourceType:o,extension:s,success:function(t){e(a(t,"image"))},fail:function(e){i({errMsg:e.errMsg.replace("chooseImage:fail",r)})}})}))}(t),t):"video"===t.type?i(e,function(e){var t=e.camera,n=e.compressed,i=e.maxDuration,o=e.sourceType,s=void 0===o?["album","camera"]:o,u=e.extension;return new Promise((function(e,o){uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:s,extension:u,success:function(t){var n=t.tempFilePath,r=t.duration,i=t.size,o=t.height,s=t.width;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:s,height:o,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){o({errMsg:e.errMsg.replace("chooseVideo:fail",r)})}})}))}(t),t):i(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,i){var o=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(o=wx.chooseMessageFile),"function"!=typeof o)return i({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});o({type:"all",count:t,extension:n,success:function(t){e(a(t))},fail:function(e){i({errMsg:e.errMsg.replace("chooseFile:fail",r)})}})}))}(t),t)}}})),ir=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ar),or={auto:"auto",onready:"onready",manual:"manual"};function sr(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==or.manual){for(var r=!1,a=[],i=2;i<t.length;i++)t[i]!==n[i]&&(a.push(t[i]),r=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(r,a)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,r=void 0!==n&&n,a=t.success,i=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,i=n.data,o=n.count;e.getcount&&(e.mixinDatacomPage.count=o),e.mixinDatacomHasMore=i.length<e.pageSize;var s=r?i.length?i[0]:void 0:i;e.mixinDatacomResData=s,a&&a(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet:function(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r=r||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var a=r.action||this.action;a&&(n=n.action(a));var i=r.collection||this.collection;n=Array.isArray(i)?(t=n).collection.apply(t,(0,u.default)(i)):n.collection(i);var o=r.where||this.where;o&&Object.keys(o).length&&(n=n.where(o));var s=r.field||this.field;s&&(n=n.field(s));var c=r.foreignKey||this.foreignKey;c&&(n=n.foreignKey(c));var l=r.groupby||this.groupby;l&&(n=n.groupBy(l));var d=r.groupField||this.groupField;d&&(n=n.groupField(d)),!0===(void 0!==r.distinct?r.distinct:this.distinct)&&(n=n.distinct());var f=r.orderby||this.orderby;f&&(n=n.orderBy(f));var p=void 0!==r.pageCurrent?r.pageCurrent:this.mixinDatacomPage.current,h=void 0!==r.pageSize?r.pageSize:this.mixinDatacomPage.size,v=void 0!==r.getcount?r.getcount:this.getcount,g=void 0!==r.gettree?r.gettree:this.gettree,m=void 0!==r.gettreepath?r.gettreepath:this.gettreepath,y={getCount:v},b={limitLevel:void 0!==r.limitlevel?r.limitlevel:this.limitlevel,startWith:void 0!==r.startwith?r.startwith:this.startwith};return g&&(y.getTree=b),m&&(y.getTreePath=b),n=n.skip(h*(p-1)).limit(h).get(y),n}}}}function ur(e){return W(an.replace("{spaceId}",e.config.spaceId))}function cr(){return lr.apply(this,arguments)}function lr(){return lr=(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=u.length>0&&void 0!==u[0]?u[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r,i=ur(this),"mp-weixin"===q){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(q,"`"));case 4:if(!n||!a){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(i.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){uni.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return o=e.sent,s=this.importObject("uni-id-co",{customUI:!0}),e.next=14,s.secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:a});case 14:return i.mpWeixinCode=o,e.abrupt("return",{code:o});case 16:case"end":return e.stop()}}),e,this)}))),lr.apply(this,arguments)}function dr(e){return fr.apply(this,arguments)}function fr(){return fr=(0,d.default)((0,l.default)().mark((function e(t){var n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=ur(this),e.abrupt("return",(n.initPromise||(n.initPromise=cr.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),fr.apply(this,arguments)}function pr(e){!function(e){me=e}(e)}function hr(e){var t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise((function(r,a){t[e]((0,f.default)((0,f.default)({},n),{},{success:function(e){r(e)},fail:function(e){a(e)}}))}))}}var vr=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){var e;return(0,g.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,i.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,m.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([hr("getSystemInfo")(),hr("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,o.default)(t,2),r=n[0];r=void 0===r?{}:r;var a=r.appId,i=n[1];i=void 0===i?{}:i;var s=i.cid;if(!a)throw new Error("Invalid appId, please check the manifest.json file");if(!s)throw new Error("Invalid push client id");e._appId=a,e._pushClientId=s,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,r=t.messageId,a=t.message;this._payloadQueue.push({action:n,messageId:r,message:a}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,r=e.message;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){uni.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){uni.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(L);var gr={tcb:Dt,tencent:Dt,aliyun:we,private:Ut,dcloud:Ut,alipay:Yt},mr=new(function(){function e(){(0,g.default)(this,e)}return(0,m.default)(e,[{key:"init",value:function(e){var t={},n=gr[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new R({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),hn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=In(Cn,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=In(Cn,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=rr,e.chooseAndUploadFile=ir.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return sr(e)}}),e.SSEChannel=vr,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r;return dr.call(e,{openid:n,callLoginByWeixin:a})}}(e),e.setCustomClientInfo=pr,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,s.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var r=n,a=r.customUI,i=r.loadingOptions,o=r.errorOptions,u=r.parseSystemError,c=!a;return new Proxy({},{get:function(r,a){switch(a){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,r=e.getCallbackArgs;return(0,d.default)((0,l.default)().mark((function e(){var a,i,o,s,u,c,d=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=d.length,i=new Array(a),o=0;o<a;o++)i[o]=d[o];return s=r?r({params:i}):{},e.prev=2,e.next=5,Y(Q(n,"invoke"),(0,f.default)({},s));case 5:return e.next=7,t.apply(void 0,i);case 7:return u=e.sent,e.next=10,Y(Q(n,"success"),(0,f.default)((0,f.default)({},s),{},{result:u}));case 10:return e.abrupt("return",u);case 13:return e.prev=13,e.t0=e["catch"](2),c=e.t0,e.next=18,Y(Q(n,"fail"),(0,f.default)((0,f.default)({},s),{},{error:c}));case 18:throw c;case 19:return e.prev=19,e.next=22,Y(Q(n,"complete"),c?(0,f.default)((0,f.default)({},s),{},{error:c}):(0,f.default)((0,f.default)({},s),{},{result:u}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var r=(0,d.default)((0,l.default)().mark((function r(){var h,v,g,m,y,b,_,x,w,k,S,P,I,O,T,E=arguments;return(0,l.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:for(c&&uni.showLoading({title:i.title,mask:i.mask}),v=E.length,g=new Array(v),m=0;m<v;m++)g[m]=E[m];return y={name:t,type:C.OBJECT,data:{method:a,params:g}},"object"==(0,s.default)(n.secretMethods)&&function(e,t){var n=t.data.method,r=e.secretMethods||{},a=r[n]||r["*"];a&&(t.secretType=a)}(n,y),b=!1,r.prev=5,r.next=8,e.callFunction(y);case 8:h=r.sent,r.next=14;break;case 11:r.prev=11,r.t0=r["catch"](5),b=!0,h={result:new le(r.t0)};case 14:if(_=h.result||{},x=_.errSubject,w=_.errCode,k=_.errMsg,S=_.newToken,c&&uni.hideLoading(),S&&S.token&&S.tokenExpired&&(ve(S),ie(ee.REFRESH_TOKEN,(0,f.default)({},S))),!w){r.next=39;break}if(P=k,!b||!u){r.next=24;break}return r.next=20,u({objectName:t,methodName:a,params:g,errSubject:x,errCode:w,errMsg:k});case 20:if(r.t1=r.sent.errMsg,r.t1){r.next=23;break}r.t1=k;case 23:P=r.t1;case 24:if(!c){r.next=37;break}if("toast"!==o.type){r.next=29;break}uni.showToast({title:P,icon:"none"}),r.next=37;break;case 29:if("modal"===o.type){r.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(o.type));case 31:return r.next=33,(0,d.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:{},n=t.title,r=t.content,a=t.showCancel,i=t.cancelText,o=t.confirmText,e.abrupt("return",new Promise((function(e,t){uni.showModal({title:n,content:r,showCancel:a,cancelText:i,confirmText:o,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:P,showCancel:o.retry,cancelText:"取消",confirmText:o.retry?"重试":"确定"});case 33:if(I=r.sent,O=I.confirm,!o.retry||!O){r.next=37;break}return r.abrupt("return",p.apply(void 0,g));case 37:throw T=new le({subject:x,code:w,message:k,requestId:h.requestId}),T.detail=h.result,ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:T}),T;case 39:return r.abrupt("return",(ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:h.result}),h.result));case 40:case"end":return r.stop()}}),r,null,[[5,11]])})));function p(){return r.apply(this,arguments)}return p}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:a,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var r=this,a=!1;if("callFunction"===t){var i=n&&n.type||C.DEFAULT;a=i!==C.DEFAULT}var o="callFunction"===t&&!a,s=this._initPromiseHub.exec();n=n||{};var u=ce(n),c=u.success,l=u.fail,d=u.complete,f=s.then((function(){return a?Promise.resolve():Y(Q(t,"invoke"),n)})).then((function(){return e.call(r,n)})).then((function(e){return a?Promise.resolve(e):Y(Q(t,"success"),e).then((function(){return Y(Q(t,"complete"),e)})).then((function(){return o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return a?Promise.reject(e):Y(Q(t,"fail"),e).then((function(){return Y(Q(t,"complete"),e)})).then((function(){return ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||l||d))return f;f.then((function(e){c&&c(e),d&&d(e),o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),d&&d(e),o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=mr,function(){var e=B,n={};if(e&&1===e.length)n=e[0],t.uniCloud=mr=mr.init(n),mr._isDefault=!0;else{var a;a=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"].forEach((function(e){mr[e]=function(){return r.error(a),Promise.reject(new le({code:"SYS_ERR",message:a}))}}))}if(Object.assign(mr,{get mixinDatacom(){return sr(mr)}}),er(mr),mr.addInterceptor=J,mr.removeInterceptor=G,mr.interceptObject=X,"app"===q&&(uni.__uniCloud=mr),"app"===q||"web"===q){var i=function(){return z||(z=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),z)}();i.uniCloud=mr,i.UniCloudError=le}}();var yr=mr;t.default=yr}).call(this,n("0ee4"),n("ba7c")["default"])},"86b2":function(e,t,n){"use strict";(function(e){n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.chooseAndUploadFile=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};if("image"===e.type)return u(a(e),e);if("video"===e.type)return u(i(e),e);return u(o(e),e)},t.uploadCloudFiles=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=arguments.length>2?arguments[2]:void 0;t=JSON.parse(JSON.stringify(t));var a=t.length,i=0,o=this;return new Promise((function(s){while(i<n)u();function u(){var n=i++;if(n>=a)!t.find((function(e){return!e.url&&!e.errMsg}))&&s(t);else{var c=t[n],l=o.files.findIndex((function(e){return e.uuid===c.uuid}));c.url="",delete c.errMsg,e.uploadFile({filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,onUploadProgress:function(e){e.index=l,r&&r(e)}}).then((function(e){c.url=e.fileID,c.index=l,n<a&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,c.index=l,n<a&&u()}))}}}))},n("bf0f"),n("5c47"),n("a1c1"),n("2797"),n("20f3"),n("fd3c"),n("d4b5"),n("aa77"),n("bd06");var r="chooseAndUploadFile:fail";function a(e){var t=e.count,n=e.sizeType,a=void 0===n?["original","compressed"]:n,i=e.sourceType,o=e.extension;return new Promise((function(e,n){uni.chooseImage({count:t,sizeType:a,sourceType:i,extension:o,success:function(t){e(s(t,"image"))},fail:function(e){n({errMsg:e.errMsg.replace("chooseImage:fail",r)})}})}))}function i(e){e.count;var t=e.camera,n=e.compressed,a=e.maxDuration,i=e.sourceType,o=e.extension;return new Promise((function(e,u){uni.chooseVideo({camera:t,compressed:n,maxDuration:a,sourceType:i,extension:o,success:function(t){var n=t.tempFilePath,r=t.duration,a=t.size,i=t.height,o=t.width;e(s({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:a,type:t.tempFile&&t.tempFile.type||"",width:o,height:i,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){u({errMsg:e.errMsg.replace("chooseVideo:fail",r)})}})}))}function o(e){var t=e.count,n=e.extension;return new Promise((function(e,a){var i=uni.chooseFile;if("undefined"!==typeof wx&&"function"===typeof wx.chooseMessageFile&&(i=wx.chooseMessageFile),"function"!==typeof i)return a({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});i({type:"all",count:t,extension:n,success:function(t){e(s(t))},fail:function(e){a({errMsg:e.errMsg.replace("chooseFile:fail",r)})}})}))}function s(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function u(e,t){var n=t.onChooseFile;t.onUploadProgress;return e.then((function(e){if(n){var t=n(e);if("undefined"!==typeof t)return Promise.resolve(t).then((function(t){return"undefined"===typeof t?e:t}))}return e})).then((function(e){return!1===e?{errMsg:"chooseAndUploadFile:ok",tempFilePaths:[],tempFiles:[]}:e}))}}).call(this,n("861b")["uniCloud"])},8732:function(e,t,n){"use strict";n.r(t);var r=n("f37b"),a=n("371b");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("7710");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"2b5fb029",null,!1,r["a"],void 0);t["default"]=s.exports},"89e8":function(e,t,n){var r=n("314f");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("01588de7",r,!0,{sourceMap:!1,shadowMode:!1})},"89ff":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("49c9")),i=r(n("8237")),o={name:"u--textarea",mixins:[uni.$u.mpMixin,i.default,uni.$u.mixin],components:{uvTextarea:a.default}};t.default=o},"8d0d":function(e,t,n){"use strict";n.r(t);var r=n("71e5"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"8e16":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-91d5fe04], uni-scroll-view[data-v-91d5fe04], uni-swiper-item[data-v-91d5fe04]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-col[data-v-91d5fe04]{padding:0;box-sizing:border-box}.u-col-0[data-v-91d5fe04]{width:0}.u-col-1[data-v-91d5fe04]{width:8.3333333333%}.u-col-2[data-v-91d5fe04]{width:16.6666666667%}.u-col-3[data-v-91d5fe04]{width:25%}.u-col-4[data-v-91d5fe04]{width:33.3333333333%}.u-col-5[data-v-91d5fe04]{width:41.6666666667%}.u-col-6[data-v-91d5fe04]{width:50%}.u-col-7[data-v-91d5fe04]{width:58.3333333333%}.u-col-8[data-v-91d5fe04]{width:66.6666666667%}.u-col-9[data-v-91d5fe04]{width:75%}.u-col-10[data-v-91d5fe04]{width:83.3333333333%}.u-col-11[data-v-91d5fe04]{width:91.6666666667%}.u-col-12[data-v-91d5fe04]{width:100%}",""]),e.exports=t},"8ed3":function(e,t,n){"use strict";var r=n("ea01"),a=n.n(r);a.a},"8f65":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{span:{type:[String,Number],default:uni.$u.props.col.span},offset:{type:[String,Number],default:uni.$u.props.col.offset},justify:{type:String,default:uni.$u.props.col.justify},align:{type:String,default:uni.$u.props.col.align},textAlign:{type:String,default:uni.$u.props.col.textAlign}}};t.default=r},"922d":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uIcon:n("165f").default,uLine:n("8332").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-form-item"},[n("v-uni-view",{staticClass:"u-form-item__body",style:[e.$u.addStyle(e.customStyle),{flexDirection:"left"===(e.labelPosition||e.parentData.labelPosition)?"row":"column"}],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[e._t("label",[e.required||e.leftIcon||e.label?n("v-uni-view",{staticClass:"u-form-item__body__left",style:{width:e.$u.addUnit(e.labelWidth||e.parentData.labelWidth),marginBottom:"left"===e.parentData.labelPosition?0:"5px"}},[n("v-uni-view",{staticClass:"u-form-item__body__left__content"},[e.required?n("v-uni-text",{staticClass:"u-form-item__body__left__content__required"},[e._v("*")]):e._e(),e.leftIcon?n("v-uni-view",{staticClass:"u-form-item__body__left__content__icon"},[n("u-icon",{attrs:{name:e.leftIcon,"custom-style":e.leftIconStyle}})],1):e._e(),n("v-uni-text",{staticClass:"u-form-item__body__left__content__label",style:[e.parentData.labelStyle,{justifyContent:"left"===e.parentData.labelAlign?"flex-start":"center"===e.parentData.labelAlign?"center":"flex-end"}]},[e._v(e._s(e.label))])],1)],1):e._e()]),n("v-uni-view",{staticClass:"u-form-item__body__right"},[n("v-uni-view",{staticClass:"u-form-item__body__right__content"},[n("v-uni-view",{staticClass:"u-form-item__body__right__content__slot"},[e._t("default")],2),e.$slots.right?n("v-uni-view",{staticClass:"item__body__right__content__icon"},[e._t("right")],2):e._e()],1)],1)],2),e._t("error",[e.message&&"message"===e.parentData.errorType?n("v-uni-text",{staticClass:"u-form-item__body__right__message",style:{marginLeft:e.$u.addUnit("top"===e.parentData.labelPosition?0:e.labelWidth||e.parentData.labelWidth)}},[e._v(e._s(e.message))]):e._e()]),e.borderBottom?n("u-line",{attrs:{color:e.message&&"border-bottom"===e.parentData.errorType?e.$u.color.error:e.propsLine.color,customStyle:"margin-top: "+(e.message&&"message"===e.parentData.errorType?"5px":0)}}):e._e()],2)},i=[]},"92e9":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{color:{type:String,default:uni.$u.props.line.color},length:{type:[String,Number],default:uni.$u.props.line.length},direction:{type:String,default:uni.$u.props.line.direction},hairline:{type:Boolean,default:uni.$u.props.line.hairline},margin:{type:[String,Number],default:uni.$u.props.line.margin},dashed:{type:Boolean,default:uni.$u.props.line.dashed}}};t.default=r},9370:function(e,t,n){"use strict";var r=n("8bdb"),a=n("af9e"),i=n("1099"),o=n("c215"),s=a((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}));r({target:"Date",proto:!0,arity:1,forced:s},{toJSON:function(e){var t=i(this),n=o(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},"96ec":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".uni-status-bar[data-v-285e3a40]{height:20px}",""]),e.exports=t},"9a02":function(e,t,n){var r=n("a0c9");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("2267c912",r,!0,{sourceMap:!1,shadowMode:!1})},"9b4e":function(e,t,n){"use strict";n.r(t);var r=n("89ff"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},"9df0":function(e,t,n){"use strict";n.r(t);var r=n("7404"),a=n("003c");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("8ed3");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"55cfca04",null,!1,r["a"],void 0);t["default"]=s.exports},"9f32":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".uni-nav-bar-text[data-v-36458f7c]{font-size:14px}.uni-nav-bar-right-text[data-v-36458f7c]{font-size:12px}.uni-navbar__content[data-v-36458f7c]{position:relative;background-color:initial}.uni-navbar-btn-text[data-v-36458f7c]{display:flex;flex-direction:column;justify-content:flex-start;align-items:center;line-height:12px}.uni-navbar__header[data-v-36458f7c]{display:flex;padding:0 10px;flex-direction:row;height:44px;font-size:12px}.uni-navbar__header-btns[data-v-36458f7c]{overflow:hidden;display:flex;flex-wrap:nowrap;flex-direction:row;width:%?120?%;justify-content:center;align-items:center;cursor:pointer}.uni-navbar__header-btns-left[data-v-36458f7c]{display:flex;width:%?120?%;justify-content:flex-start;align-items:center}.uni-navbar__header-btns-right[data-v-36458f7c]{display:flex;flex-direction:row;justify-content:flex-end;align-items:center}.uni-navbar__header-container[data-v-36458f7c]{display:flex;flex:1;padding:0 10px;overflow:hidden}.uni-navbar__header-container-inner[data-v-36458f7c]{display:flex;flex:1;flex-direction:row;align-items:center;justify-content:center;font-size:12px;overflow:hidden}.uni-navbar__placeholder-view[data-v-36458f7c]{height:44px}.uni-navbar--fixed[data-v-36458f7c]{position:fixed;z-index:99;left:var(--window-left);right:var(--window-right)}.uni-navbar--shadow[data-v-36458f7c]{box-shadow:0 1px 6px #ccc}.uni-navbar--border[data-v-36458f7c]{border-bottom-width:%?1?%;border-bottom-style:solid;border-bottom-color:#eee}.uni-ellipsis-1[data-v-36458f7c]{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}",""]),e.exports=t},"9f3e":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-55cfca04], uni-scroll-view[data-v-55cfca04], uni-swiper-item[data-v-55cfca04]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-badge[data-v-55cfca04]{border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;\ndisplay:flex;\nflex-direction:row;line-height:11px;text-align:center;font-size:11px;color:#fff}.u-badge--dot[data-v-55cfca04]{height:8px;width:8px}.u-badge--inverted[data-v-55cfca04]{font-size:13px}.u-badge--not-dot[data-v-55cfca04]{padding:2px 5px}.u-badge--horn[data-v-55cfca04]{border-bottom-left-radius:0}.u-badge--primary[data-v-55cfca04]{background-color:#3c9cff}.u-badge--primary--inverted[data-v-55cfca04]{color:#3c9cff}.u-badge--error[data-v-55cfca04]{background-color:#f56c6c}.u-badge--error--inverted[data-v-55cfca04]{color:#f56c6c}.u-badge--success[data-v-55cfca04]{background-color:#5ac725}.u-badge--success--inverted[data-v-55cfca04]{color:#5ac725}.u-badge--info[data-v-55cfca04]{background-color:#909399}.u-badge--info--inverted[data-v-55cfca04]{color:#909399}.u-badge--warning[data-v-55cfca04]{background-color:#f9ae3d}.u-badge--warning--inverted[data-v-55cfca04]{color:#f9ae3d}",""]),e.exports=t},a0c9:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-4dbd7d4a], uni-scroll-view[data-v-4dbd7d4a], uni-swiper-item[data-v-4dbd7d4a]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-radio[data-v-4dbd7d4a]{\ndisplay:flex;\nflex-direction:row;overflow:hidden;flex-direction:row;align-items:center}.u-radio-label--left[data-v-4dbd7d4a]{flex-direction:row}.u-radio-label--right[data-v-4dbd7d4a]{flex-direction:row-reverse;justify-content:space-between}.u-radio__icon-wrap[data-v-4dbd7d4a]{box-sizing:border-box;transition-property:border-color,background-color,color;transition-duration:.2s;color:#606266;\ndisplay:flex;\nflex-direction:row;align-items:center;justify-content:center;color:transparent;text-align:center;margin-right:6px;font-size:20px;border-width:1px;border-color:#c8c9cc;border-style:solid}.u-radio__icon-wrap--circle[data-v-4dbd7d4a]{border-radius:100%}.u-radio__icon-wrap--square[data-v-4dbd7d4a]{border-radius:3px}.u-radio__icon-wrap--checked[data-v-4dbd7d4a]{color:#fff;background-color:red;border-color:#2979ff}.u-radio__icon-wrap--disabled[data-v-4dbd7d4a]{background-color:#ebedf0!important}.u-radio__icon-wrap--disabled--checked[data-v-4dbd7d4a]{color:#c8c9cc!important}.u-radio__label[data-v-4dbd7d4a]{word-wrap:break-word;margin-left:5px;margin-right:12px;color:#606266;font-size:15px}.u-radio__label--disabled[data-v-4dbd7d4a]{color:#c8c9cc}",""]),e.exports=t},a3f2:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uIcon:n("165f").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-radio",class:["u-radio-label--"+e.parentData.iconPlacement,e.parentData.borderBottom&&"column"===e.parentData.placement&&"u-border-bottom"],style:[e.radioStyle],on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.wrapperClickHandler.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-radio__icon-wrap",class:e.iconClasses,style:[e.iconWrapStyle],on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.iconClickHandler.apply(void 0,arguments)}}},[e._t("icon",[n("u-icon",{staticClass:"u-radio__icon-wrap__icon",attrs:{name:"checkbox-mark",size:e.elIconSize,color:e.elIconColor}})])],2),e._t("default",[n("v-uni-text",{staticClass:"u-radio__text",style:{color:e.elDisabled?e.elInactiveColor:e.elLabelColor,fontSize:e.elLabelSize,lineHeight:e.elLabelSize},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.labelClickHandler.apply(void 0,arguments)}}},[e._v(e._s(e.label))])])],2)},i=[]},a3fc:function(e,t,n){(function(e){function n(e,t){for(var n=0,r=e.length-1;r>=0;r--){var a=e[r];"."===a?e.splice(r,1):".."===a?(e.splice(r,1),n++):n&&(e.splice(r,1),n--)}if(t)for(;n--;n)e.unshift("..");return e}function r(e,t){if(e.filter)return e.filter(t);for(var n=[],r=0;r<e.length;r++)t(e[r],r,e)&&n.push(e[r]);return n}t.resolve=function(){for(var t="",a=!1,i=arguments.length-1;i>=-1&&!a;i--){var o=i>=0?arguments[i]:e.cwd();if("string"!==typeof o)throw new TypeError("Arguments to path.resolve must be strings");o&&(t=o+"/"+t,a="/"===o.charAt(0))}return t=n(r(t.split("/"),(function(e){return!!e})),!a).join("/"),(a?"/":"")+t||"."},t.normalize=function(e){var i=t.isAbsolute(e),o="/"===a(e,-1);return e=n(r(e.split("/"),(function(e){return!!e})),!i).join("/"),e||i||(e="."),e&&o&&(e+="/"),(i?"/":"")+e},t.isAbsolute=function(e){return"/"===e.charAt(0)},t.join=function(){var e=Array.prototype.slice.call(arguments,0);return t.normalize(r(e,(function(e,t){if("string"!==typeof e)throw new TypeError("Arguments to path.join must be strings");return e})).join("/"))},t.relative=function(e,n){function r(e){for(var t=0;t<e.length;t++)if(""!==e[t])break;for(var n=e.length-1;n>=0;n--)if(""!==e[n])break;return t>n?[]:e.slice(t,n-t+1)}e=t.resolve(e).substr(1),n=t.resolve(n).substr(1);for(var a=r(e.split("/")),i=r(n.split("/")),o=Math.min(a.length,i.length),s=o,u=0;u<o;u++)if(a[u]!==i[u]){s=u;break}var c=[];for(u=s;u<a.length;u++)c.push("..");return c=c.concat(i.slice(s)),c.join("/")},t.sep="/",t.delimiter=":",t.dirname=function(e){if("string"!==typeof e&&(e+=""),0===e.length)return".";for(var t=e.charCodeAt(0),n=47===t,r=-1,a=!0,i=e.length-1;i>=1;--i)if(t=e.charCodeAt(i),47===t){if(!a){r=i;break}}else a=!1;return-1===r?n?"/":".":n&&1===r?"/":e.slice(0,r)},t.basename=function(e,t){var n=function(e){"string"!==typeof e&&(e+="");var t,n=0,r=-1,a=!0;for(t=e.length-1;t>=0;--t)if(47===e.charCodeAt(t)){if(!a){n=t+1;break}}else-1===r&&(a=!1,r=t+1);return-1===r?"":e.slice(n,r)}(e);return t&&n.substr(-1*t.length)===t&&(n=n.substr(0,n.length-t.length)),n},t.extname=function(e){"string"!==typeof e&&(e+="");for(var t=-1,n=0,r=-1,a=!0,i=0,o=e.length-1;o>=0;--o){var s=e.charCodeAt(o);if(47!==s)-1===r&&(a=!1,r=o+1),46===s?-1===t?t=o:1!==i&&(i=1):-1!==t&&(i=-1);else if(!a){n=o+1;break}}return-1===t||-1===r||0===i||1===i&&t===r-1&&t===n+1?"":e.slice(t,r)};var a="b"==="ab".substr(-1)?function(e,t,n){return e.substr(t,n)}:function(e,t,n){return t<0&&(t=e.length+t),e.substr(t,n)}}).call(this,n("28d0"))},a53c:function(e,t,n){var r=n("ec2c");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("578ed5e3",r,!0,{sourceMap:!1,shadowMode:!1})},a559:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-03e1ba13], uni-scroll-view[data-v-03e1ba13], uni-swiper-item[data-v-03e1ba13]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-form-item[data-v-03e1ba13]{\ndisplay:flex;\nflex-direction:column;font-size:14px;color:#303133}.u-form-item__body[data-v-03e1ba13]{\ndisplay:flex;\nflex-direction:row;padding:10px 0}.u-form-item__body__left[data-v-03e1ba13]{\ndisplay:flex;\nflex-direction:row;align-items:center}.u-form-item__body__left__content[data-v-03e1ba13]{position:relative;\ndisplay:flex;\nflex-direction:row;align-items:center;padding-right:%?10?%;flex:1}.u-form-item__body__left__content__icon[data-v-03e1ba13]{margin-right:%?8?%}.u-form-item__body__left__content__required[data-v-03e1ba13]{position:absolute;left:-9px;color:#f56c6c;line-height:20px;font-size:20px;top:3px}.u-form-item__body__left__content__label[data-v-03e1ba13]{\ndisplay:flex;\nflex-direction:row;align-items:center;flex:1;color:#303133;font-size:15px}.u-form-item__body__right[data-v-03e1ba13]{flex:1}.u-form-item__body__right__content[data-v-03e1ba13]{\ndisplay:flex;\nflex-direction:row;align-items:center;flex:1}.u-form-item__body__right__content__slot[data-v-03e1ba13]{flex:1;\ndisplay:flex;\nflex-direction:row;align-items:center}.u-form-item__body__right__content__icon[data-v-03e1ba13]{margin-left:%?10?%;color:#c0c4cc;font-size:%?30?%}.u-form-item__body__right__message[data-v-03e1ba13]{font-size:12px;line-height:12px;color:#f56c6c}",""]),e.exports=t},a8fd:function(e,t,n){"use strict";n.r(t);var r=n("60ad"),a=n("9b4e");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=s.exports},a9f5:function(e,t,n){"use strict";n.r(t);var r=n("23c8"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},aa3b:function(e,t,n){var r=n("96ec");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("0e22cd8c",r,!0,{sourceMap:!1,shadowMode:!1})},ab56:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{label:{type:String,default:uni.$u.props.formItem.label},prop:{type:String,default:uni.$u.props.formItem.prop},borderBottom:{type:[String,Boolean],default:uni.$u.props.formItem.borderBottom},labelPosition:{type:String,default:uni.$u.props.formItem.labelPosition},labelWidth:{type:[String,Number],default:uni.$u.props.formItem.labelWidth},rightIcon:{type:String,default:uni.$u.props.formItem.rightIcon},leftIcon:{type:String,default:uni.$u.props.formItem.leftIcon},required:{type:Boolean,default:uni.$u.props.formItem.required},leftIconStyle:{type:[String,Object],default:uni.$u.props.formItem.leftIconStyle}}};t.default=r},acb1:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.default)(e,t)},n("7a76"),n("c9b5"),n("6a54");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("e668"))},ace0:function(e,t,n){var r=n("a559");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("3f548c71",r,!0,{sourceMap:!1,shadowMode:!1})},af24:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-textarea",class:e.textareaClass,style:[e.textareaStyle]},[n("v-uni-textarea",{staticClass:"u-textarea__field",style:{height:e.$u.addUnit(e.height)},attrs:{value:e.innerValue,placeholder:e.placeholder,"placeholder-style":e.$u.addStyle(e.placeholderStyle,"string"),"placeholder-class":e.placeholderClass,disabled:e.disabled,focus:e.focus,autoHeight:e.autoHeight,fixed:e.fixed,cursorSpacing:e.cursorSpacing,cursor:e.cursor,showConfirmBar:e.showConfirmBar,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,disableDefaultPadding:e.disableDefaultPadding,holdKeyboard:e.holdKeyboard,maxlength:e.maxlength,confirmType:e.confirmType,ignoreCompositionEvent:e.ignoreCompositionEvent},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},linechange:function(t){arguments[0]=t=e.$handleEvent(t),e.onLinechange.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)},keyboardheightchange:function(t){arguments[0]=t=e.$handleEvent(t),e.onKeyboardheightchange.apply(void 0,arguments)}}}),e.count?n("v-uni-text",{staticClass:"u-textarea__count",style:{"background-color":e.disabled?"transparent":"#fff"}},[e._v(e._s(e.innerValue.length)+"/"+e._s(e.maxlength))]):e._e()],1)},a=[]},b0e5:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uniIcons:n("28af").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"uni-navbar",class:{"uni-dark":e.dark,"uni-nvue-fixed":e.fixed}},[n("v-uni-view",{staticClass:"uni-navbar__content",class:{"uni-navbar--fixed":e.fixed,"uni-navbar--shadow":e.shadow,"uni-navbar--border":e.border},style:{"background-color":e.themeBgColor,"border-bottom-color":e.themeColor}},[e.statusBar?n("status-bar"):e._e(),n("v-uni-view",{staticClass:"uni-navbar__header",style:{color:e.themeColor,backgroundColor:e.themeBgColor,height:e.navbarHeight}},[n("v-uni-view",{staticClass:"uni-navbar__header-btns uni-navbar__header-btns-left",style:{width:e.leftIconWidth},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLeft.apply(void 0,arguments)}}},[e._t("left",[e.leftIcon.length>0?n("v-uni-view",{staticClass:"uni-navbar__content_view"},[n("uni-icons",{attrs:{color:e.themeColor,type:e.leftIcon,size:"20"}})],1):e._e(),e.leftText.length?n("v-uni-view",{staticClass:"uni-navbar-btn-text",class:{"uni-navbar-btn-icon-left":!e.leftIcon.length>0}},[n("v-uni-text",{style:{color:e.themeColor,fontSize:"12px"}},[e._v(e._s(e.leftText))])],1):e._e()])],2),n("v-uni-view",{staticClass:"uni-navbar__header-container ",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickTitle.apply(void 0,arguments)}}},[e._t("default",[e.title.length>0?n("v-uni-view",{staticClass:"uni-navbar__header-container-inner"},[n("v-uni-text",{staticClass:"uni-nav-bar-text uni-ellipsis-1",style:{color:e.themeColor}},[e._v(e._s(e.title))])],1):e._e()])],2),n("v-uni-view",{staticClass:"uni-navbar__header-btns uni-navbar__header-btns-right",style:{width:e.rightIconWidth},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickRight.apply(void 0,arguments)}}},[e._t("right",[e.rightIcon.length?n("v-uni-view",[n("uni-icons",{attrs:{color:e.themeColor,type:e.rightIcon,size:"22"}})],1):e._e(),e.rightText.length&&!e.rightIcon.length?n("v-uni-view",{staticClass:"uni-navbar-btn-text"},[n("v-uni-text",{staticClass:"uni-nav-bar-right-text",style:{color:e.themeColor}},[e._v(e._s(e.rightText))])],1):e._e()])],2)],1)],1),e.fixed?n("v-uni-view",{staticClass:"uni-navbar__placeholder"},[e.statusBar?n("status-bar"):e._e(),n("v-uni-view",{staticClass:"uni-navbar__placeholder-view",style:{height:e.navbarHeight}})],1):e._e()],1)},i=[]},b109:function(e,t,n){"use strict";var r=n("ace0"),a=n.n(r);a.a},b248:function(e,t,n){"use strict";n.r(t);var r=n("6ddf"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},b40c:function(e,t,n){"use strict";n.r(t);var r=n("cf61"),a=n("b248");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("0bdc");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"91d5fe04",null,!1,r["a"],void 0);t["default"]=s.exports},b5d0:function(e,t,n){"use strict";var r=n("9a02"),a=n.n(r);a.a},b5f3:function(e,t,n){var r=n("e7b4");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("0940a5da",r,!0,{sourceMap:!1,shadowMode:!1})},b907:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-ed1d90b6], uni-scroll-view[data-v-ed1d90b6], uni-swiper-item[data-v-ed1d90b6]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-text[data-v-ed1d90b6]{\ndisplay:flex;\nflex-direction:row;align-items:center;flex-wrap:nowrap;flex:1;width:100%}.u-text__price[data-v-ed1d90b6]{font-size:14px;color:#606266}.u-text__value[data-v-ed1d90b6]{font-size:14px;\ndisplay:flex;\nflex-direction:row;color:#606266;flex-wrap:wrap;text-overflow:ellipsis;align-items:center}.u-text__value--primary[data-v-ed1d90b6]{color:#3c9cff}.u-text__value--warning[data-v-ed1d90b6]{color:#f9ae3d}.u-text__value--success[data-v-ed1d90b6]{color:#5ac725}.u-text__value--info[data-v-ed1d90b6]{color:#909399}.u-text__value--error[data-v-ed1d90b6]{color:#f56c6c}.u-text__value--main[data-v-ed1d90b6]{color:#303133}.u-text__value--content[data-v-ed1d90b6]{color:#606266}.u-text__value--tips[data-v-ed1d90b6]{color:#909193}.u-text__value--light[data-v-ed1d90b6]{color:#c0c4cc}",""]),e.exports=t},b98a:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{name:{type:[String,Number,Boolean],default:uni.$u.props.radio.name},shape:{type:String,default:uni.$u.props.radio.shape},disabled:{type:[String,Boolean],default:uni.$u.props.radio.disabled},labelDisabled:{type:[String,Boolean],default:uni.$u.props.radio.labelDisabled},activeColor:{type:String,default:uni.$u.props.radio.activeColor},inactiveColor:{type:String,default:uni.$u.props.radio.inactiveColor},iconSize:{type:[String,Number],default:uni.$u.props.radio.iconSize},labelSize:{type:[String,Number],default:uni.$u.props.radio.labelSize},label:{type:[String,Number],default:uni.$u.props.radio.label},size:{type:[String,Number],default:uni.$u.props.radio.size},color:{type:String,default:uni.$u.props.radio.color},labelColor:{type:String,default:uni.$u.props.radio.labelColor}}};t.default=r},b9a5:function(e,t,n){"use strict";n.r(t);var r=n("0ef8"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},bb76:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{type:{type:String,default:uni.$u.props.text.type},show:{type:Boolean,default:uni.$u.props.text.show},text:{type:[String,Number],default:uni.$u.props.text.text},prefixIcon:{type:String,default:uni.$u.props.text.prefixIcon},suffixIcon:{type:String,default:uni.$u.props.text.suffixIcon},mode:{type:String,default:uni.$u.props.text.mode},href:{type:String,default:uni.$u.props.text.href},format:{type:[String,Function],default:uni.$u.props.text.format},call:{type:Boolean,default:uni.$u.props.text.call},openType:{type:String,default:uni.$u.props.text.openType},bold:{type:Boolean,default:uni.$u.props.text.bold},block:{type:Boolean,default:uni.$u.props.text.block},lines:{type:[String,Number],default:uni.$u.props.text.lines},color:{type:String,default:uni.$u.props.text.color},size:{type:[String,Number],default:uni.$u.props.text.size},iconStyle:{type:[Object,String],default:uni.$u.props.text.iconStyle},decoration:{type:String,default:uni.$u.props.text.decoration},margin:{type:[Object,String,Number],default:uni.$u.props.text.margin},lineHeight:{type:[String,Number],default:uni.$u.props.text.lineHeight},align:{type:String,default:uni.$u.props.text.align},wordWrap:{type:String,default:uni.$u.props.text.wordWrap}}};t.default=r},bf29:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa"),n("c223"),n("bf0f"),n("2797"),n("aa9c"),n("5ef2");var r={name:"uploadImage",emits:["uploadFiles","choose","delFile"],props:{filesList:{type:Array,default:function(){return[]}},disabled:{type:Boolean,default:!1},disablePreview:{type:Boolean,default:!1},limit:{type:[Number,String],default:9},imageStyles:{type:Object,default:function(){return{width:"auto",height:"auto",border:{}}}},delIcon:{type:Boolean,default:!0},readonly:{type:Boolean,default:!1}},computed:{styles:function(){return Object.assign({width:"auto",height:"auto",border:{}},this.imageStyles)},boxStyle:function(){var e=this.styles,t=e.width,n=void 0===t?"auto":t,r=e.height,a=void 0===r?"auto":r,i={};"auto"===a?"auto"!==n?(i.height=this.value2px(n),i["padding-top"]=0):i.height=0:(i.height=this.value2px(a),i["padding-top"]=0),i.width="auto"===n?"auto"!==a?this.value2px(a):"33.3%":this.value2px(n);var o="";for(var s in i)o+="".concat(s,":").concat(i[s],";");return o},borderStyle:function(){var e=this.styles.border,t={};if("boolean"===typeof e)t.border=e?"1px #eee solid":"none";else{var n=e&&e.width||1;n=this.value2px(n);var r=e&&e.radius||3;r=this.value2px(r),t={"border-width":n,"border-style":e&&e.style||"solid","border-color":e&&e.color||"#eee","border-radius":r}}var a="";for(var i in t)a+="".concat(i,":").concat(t[i],";");return a}},methods:{uploadFiles:function(e,t){this.$emit("uploadFiles",e)},choose:function(){this.$emit("choose")},delFile:function(e){this.$emit("delFile",e)},prviewImage:function(e,t){var n=[];1===Number(this.limit)&&this.disablePreview&&!this.disabled&&this.$emit("choose"),this.disablePreview||(this.filesList.forEach((function(e){n.push(e.url)})),uni.previewImage({urls:n,current:t}))},value2px:function(e){return"number"===typeof e?e+="px":-1===e.indexOf("%")&&(e=-1!==e.indexOf("px")?e:e+"px"),e}}};t.default=r},c2db:function(e,t,n){var r=n("5d01");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("18354ba5",r,!0,{sourceMap:!1,shadowMode:!1})},c359:function(e,t,n){"use strict";n.r(t);var r=n("47c4"),a=n("3bde");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("da7e");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"7a78d4df",null,!1,r["a"],void 0);t["default"]=s.exports},c3e3:function(e,t,n){"use strict";n.r(t);var r=n("795f"),a=n("daf9");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=s.exports},c451:function(e,t,n){var r=n("51f2");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("76a3eea6",r,!0,{sourceMap:!1,shadowMode:!1})},c821:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"uni-file-picker__files"},[e.readonly?e._e():n("v-uni-view",{staticClass:"files-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.choose.apply(void 0,arguments)}}},[e._t("default")],2),e.list.length>0?n("v-uni-view",{staticClass:"uni-file-picker__lists is-text-box",style:e.borderStyle},e._l(e.list,(function(t,r){return n("v-uni-view",{key:r,staticClass:"uni-file-picker__lists-box",class:{"files-border":0!==r&&e.styles.dividline},style:0!==r&&e.styles.dividline&&e.borderLineStyle},[n("v-uni-view",{staticClass:"uni-file-picker__item"},[n("v-uni-view",{staticClass:"files__name"},[e._v(e._s(t.name))]),e.delIcon&&!e.readonly?n("v-uni-view",{staticClass:"icon-del-box icon-files",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.delFile(r)}}},[n("v-uni-view",{staticClass:"icon-del icon-files"}),n("v-uni-view",{staticClass:"icon-del rotate"})],1):e._e()],1),t.progress&&100!==t.progress||0===t.progress?n("v-uni-view",{staticClass:"file-picker__progress"},[n("v-uni-progress",{staticClass:"file-picker__progress-item",attrs:{percent:-1===t.progress?0:t.progress,"stroke-width":"4",backgroundColor:t.errMsg?"#ff5a5f":"#EBEBEB"}})],1):e._e(),"error"===t.status?n("v-uni-view",{staticClass:"file-picker__mask",on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.uploadFiles(t,r)}}},[e._v("点击重试")]):e._e()],1)})),1):e._e()],1)},a=[]},c891:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa"),n("bf0f"),n("2797"),n("aa9c"),n("c223"),n("5ef2");var r={name:"uploadFile",emits:["uploadFiles","choose","delFile"],props:{filesList:{type:Array,default:function(){return[]}},delIcon:{type:Boolean,default:!0},limit:{type:[Number,String],default:9},showType:{type:String,default:""},listStyles:{type:Object,default:function(){return{border:!0,dividline:!0,borderStyle:{}}}},readonly:{type:Boolean,default:!1}},computed:{list:function(){var e=[];return this.filesList.forEach((function(t){e.push(t)})),e},styles:function(){return Object.assign({border:!0,dividline:!0,"border-style":{}},this.listStyles)},borderStyle:function(){var e=this.styles,t=e.borderStyle,n=e.border,r={};if(n){var a=t&&t.width||1;a=this.value2px(a);var i=t&&t.radius||5;i=this.value2px(i),r={"border-width":a,"border-style":t&&t.style||"solid","border-color":t&&t.color||"#eee","border-radius":i}}else r.border="none";var o="";for(var s in r)o+="".concat(s,":").concat(r[s],";");return o},borderLineStyle:function(){var e={},t=this.styles.borderStyle;if(t&&t.color&&(e["border-color"]=t.color),t&&t.width){var n=t&&t.width||1,r=t&&t.style||0;"number"===typeof n?n+="px":n=n.indexOf("px")?n:n+"px",e["border-width"]=n,"number"===typeof r?r+="px":r=r.indexOf("px")?r:r+"px",e["border-top-style"]=r}var a="";for(var i in e)a+="".concat(i,":").concat(e[i],";");return a}},methods:{uploadFiles:function(e,t){this.$emit("uploadFiles",{item:e,index:t})},choose:function(){this.$emit("choose")},delFile:function(e){this.$emit("delFile",e)},value2px:function(e){return"number"===typeof e?e+="px":e=-1!==e.indexOf("px")?e:e+"px",e}}};t.default=r},cad9:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,a.default)();return function(){var n,a=(0,r.default)(e);if(t){var o=(0,r.default)(this).constructor;n=Reflect.construct(a,arguments,o)}else n=a.apply(this,arguments);return(0,i.default)(this,n)}},n("6a88"),n("bf0f"),n("7996");var r=o(n("f1f8")),a=o(n("6c31")),i=o(n("62b0"));function o(e){return e&&e.__esModule?e:{default:e}}},cb61:function(e,t,n){"use strict";n.r(t);var r=n("1052"),a=n("cffa");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("8049");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"509aa77e",null,!1,r["a"],void 0);t["default"]=s.exports},cf61:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{ref:"u-col",staticClass:"u-col",class:["u-col-"+e.span],style:[e.colStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[e._t("default")],2)},a=[]},cffa:function(e,t,n){"use strict";n.r(t);var r=n("f28f"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},d23e:function(e,t,n){"use strict";n.r(t);var r=n("b0e5"),a=n("f864");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("ef52");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"36458f7c",null,!1,r["a"],void 0);t["default"]=s.exports},d248:function(e,t,n){"use strict";var r=n("698d"),a=n.n(r);a.a},d29e:function(e,t,n){"use strict";n.r(t);var r=n("2230"),a=n("b9a5");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("7834");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"ed1d90b6",null,!1,r["a"],void 0);t["default"]=s.exports},d2c4:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,n("6a88"),n("bf0f"),n("7996"),n("aa9c");var r=i(n("e668")),a=i(n("6c31"));function i(e){return e&&e.__esModule?e:{default:e}}function o(e,n,i){return(0,a.default)()?t.default=o=Reflect.construct.bind():t.default=o=function(e,t,n){var a=[null];a.push.apply(a,t);var i=Function.bind.apply(e,a),o=new i;return n&&(0,r.default)(o,n.prototype),o},o.apply(null,arguments)}},d441:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},n("5ef2"),n("c9b5"),n("bf0f"),n("ab80")},d598:function(e,t,n){"use strict";n.r(t);var r=n("2fc5"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},d712:function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.get_files_and_is_max=t.get_file_info=t.get_file_ext=t.get_file_data=t.get_extname=void 0;var a=r(n("2634")),i=r(n("2fdc"));n("20f3"),n("5c47"),n("a1c1"),n("bf0f"),n("2797"),n("5ef2"),n("aa9c"),n("c223");var o=function(e){var t=e.lastIndexOf("."),n=e.length;return{name:e.substring(0,t),ext:e.substring(t+1,n)}};t.get_file_ext=o;t.get_extname=function(e){if(Array.isArray(e))return e;var t=e.replace(/(\[|\])/g,"");return t.split(",")};t.get_files_and_is_max=function(e,t){var n=[],r=[];return t&&0!==t.length?(e.tempFiles.forEach((function(e){var a=o(e.name),i=a.ext.toLowerCase();-1!==t.indexOf(i)&&(r.push(e),n.push(e.path))})),r.length!==e.tempFiles.length&&uni.showToast({title:"当前选择了".concat(e.tempFiles.length,"个文件 ，").concat(e.tempFiles.length-r.length," 个文件格式不正确"),icon:"none",duration:5e3}),{filePaths:n,files:r}):{filePaths:n,files:r}};var s=function(e){return new Promise((function(t,n){uni.getImageInfo({src:e,success:function(e){t(e)},fail:function(e){n(e)}})}))};t.get_file_info=s;var u=function(){var e=(0,i.default)((0,a.default)().mark((function e(t){var n,r,i,u,c,l=arguments;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=l.length>1&&void 0!==l[1]?l[1]:"image",r=o(t.name),i=r.ext.toLowerCase(),u={name:t.name,uuid:t.uuid,extname:i||"",cloudPath:t.cloudPath,fileType:t.fileType,thumbTempFilePath:t.thumbTempFilePath,url:t.path||t.path,size:t.size,image:{},path:t.path,video:{}},"image"!==n){e.next=14;break}return e.next=7,s(t.path);case 7:c=e.sent,delete u.video,u.image.width=c.width,u.image.height=c.height,u.image.location=c.path,e.next=15;break;case 14:delete u.image;case 15:return e.abrupt("return",u);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();t.get_file_data=u},d843:function(e,t,n){"use strict";var r=n("5ff1"),a=n.n(r);a.a},da54:function(e,t,n){"use strict";n.r(t);var r=n("30ee"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},da7e:function(e,t,n){"use strict";var r=n("fac4"),a=n.n(r);a.a},daf9:function(e,t,n){"use strict";n.r(t);var r=n("f106"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},dbe1:function(e,t,n){"use strict";n.r(t);var r=n("1b01"),a=n("01fc");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("71bf");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"285e3a40",null,!1,r["a"],void 0);t["default"]=s.exports},dfd5:function(e,t,n){"use strict";n.r(t);var r=n("6684"),a=n("a9f5");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("f0de");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"4236db40",null,!1,r["a"],void 0);t["default"]=s.exports},e497:function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("473f"),n("bf0f"),n("f7a5"),n("18f7"),n("de6c"),n("fd3c");var a=r(n("5de6")),i=r(n("9b1b")),o=r(n("2634")),s=r(n("2fdc")),u=r(n("5c0e")),c={name:"u-tabs",mixins:[uni.$u.mpMixin,uni.$u.mixin,u.default],data:function(){return{firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}},watch:{current:{immediate:!0,handler:function(e,t){var n=this;e!==this.innerCurrent&&(this.innerCurrent=e,this.$nextTick((function(){n.resize()})))}},list:function(){var e=this;this.$nextTick((function(){e.resize()}))}},computed:{textStyle:function(){var e=this;return function(t){var n={},r=t===e.innerCurrent?uni.$u.addStyle(e.activeStyle):uni.$u.addStyle(e.inactiveStyle);return e.list[t].disabled&&(n.color="#c8c9cc"),uni.$u.deepMerge(r,n)}},propsBadge:function(){return uni.$u.props.badge}},mounted:function(){var e=this;return(0,s.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.init();case 1:case"end":return t.stop()}}),t)})))()},methods:{setLineLeft:function(){var e=this,t=this.list[this.innerCurrent];if(t){var n=this.list.slice(0,this.innerCurrent).reduce((function(e,t){return e+t.rect.width}),0),r=uni.$u.getPx(this.lineWidth);this.lineOffsetLeft=n+(t.rect.width-r)/2,this.firstTime&&setTimeout((function(){e.firstTime=!1}),10)}},animation:function(e){},clickHandler:function(e,t){this.$emit("click",(0,i.default)((0,i.default)({},e),{},{index:t})),e.disabled||(this.innerCurrent=t,this.resize(),this.$emit("change",(0,i.default)((0,i.default)({},e),{},{index:t})))},init:function(){var e=this;uni.$u.sleep().then((function(){e.resize()}))},setScrollLeft:function(){var e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce((function(e,t){return e+t.rect.width}),0),n=uni.$u.sys().windowWidth,r=t-(this.tabsRect.width-e.rect.width)/2-(n-this.tabsRect.right)/2+this.tabsRect.left/2;r=Math.min(r,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,r)},resize:function(){var e=this;0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((function(t){var n=(0,a.default)(t,2),r=n[0],i=n[1],o=void 0===i?[]:i;e.tabsRect=r,e.scrollViewWidth=0,o.map((function(t,n){e.scrollViewWidth+=t.width,e.list[n].rect=t})),e.setLineLeft(),e.setScrollLeft()}))},getTabsRect:function(){var e=this;return new Promise((function(t){e.queryRect("u-tabs__wrapper__scroll-view").then((function(e){return t(e)}))}))},getAllItemRect:function(){var e=this;return new Promise((function(t){var n=e.list.map((function(t,n){return e.queryRect("u-tabs__wrapper__nav__item-".concat(n),!0)}));Promise.all(n).then((function(e){return t(e)}))}))},queryRect:function(e,t){var n=this;return new Promise((function(t){n.$uGetRect(".".concat(e)).then((function(e){t(e)}))}))}}};t.default=c},e668:function(e,t,n){"use strict";function r(e,n){return t.default=r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,n)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("8a8d")},e7b4:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".add[data-v-7aac8588]{width:100%;padding:0 %?30?%;box-sizing:border-box}.add .nav-left[data-v-7aac8588]{display:flex;align-items:center;width:auto;color:#fff}.add .nav-left uni-image[data-v-7aac8588]{width:%?40?%;height:%?40?%}.add_body[data-v-7aac8588]{box-sizing:border-box;width:100%;height:100vh;display:flex;flex-direction:column;align-items:center}.add_body .add_content[data-v-7aac8588]{width:%?720?%;height:100%}.add_body .add_content[data-v-7aac8588]  .uni-date__x-input{height:-webkit-fit-content;height:fit-content;line-height:24px}.add_body .add_content[data-v-7aac8588]  .u-form-item__body__left{display:flex;align-items:flex-start}.add_body .add_content[data-v-7aac8588]  .u-textarea{padding:0}.add_body .add_content .operator[data-v-7aac8588]{display:flex;padding:16px 0;gap:16px}",""]),e.exports=t},ea01:function(e,t,n){var r=n("9f3e");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("77a71c1e",r,!0,{sourceMap:!1,shadowMode:!1})},ec2c:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"uni-view[data-v-2b5fb029], uni-scroll-view[data-v-2b5fb029], uni-swiper-item[data-v-2b5fb029]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-link[data-v-2b5fb029]{line-height:1;\ndisplay:flex;\nflex-direction:row;flex-wrap:wrap;flex:1}",""]),e.exports=t},ed86:function(e,t,n){"use strict";var r=n("c2db"),a=n.n(r);a.a},ef52:function(e,t,n){"use strict";var r=n("2bde"),a=n.n(r);a.a},f0de:function(e,t,n){"use strict";var r=n("7eb0"),a=n.n(r);a.a},f106:function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("f441")),i=r(n("56f9")),o={name:"u--form",mixins:[uni.$u.mpMixin,i.default,uni.$u.mixin],components:{uvForm:a.default},created:function(){this.children=[]},methods:{setRules:function(e){this.$refs.uForm.setRules(e)},validate:function(){return this.$refs.uForm.validate()},validateField:function(e,t){return this.$refs.uForm.validateField(e,t)},resetFields:function(){return this.$refs.uForm.resetFields()},clearValidate:function(e){return this.$refs.uForm.clearValidate(e)},setMpData:function(){this.$refs.uForm.children=this.children}}};t.default=o},f1f8:function(e,t,n){"use strict";function r(e){return t.default=r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},r(e)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("8a8d"),n("926e")},f28f:function(e,t,n){"use strict";(function(e,r){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("b7c7")),o=a(n("9b1b")),s=a(n("2634")),u=a(n("2fdc"));n("64aa"),n("bf0f"),n("2797"),n("aa9c"),n("dd2b"),n("5c47"),n("0506"),n("dc8a"),n("c223"),n("a1c1"),n("18f7"),n("de6c"),n("bd06"),n("e966"),n("20f3");var c=n("86b2"),l=n("d712"),d=a(n("6bf8")),f=a(n("2f8c")),p={name:"uniFilePicker",components:{uploadImage:d.default,uploadFile:f.default},options:{virtualHost:!0},emits:["select","success","fail","progress","delete","update:modelValue","input"],props:{modelValue:{type:[Array,Object],default:function(){return[]}},value:{type:[Array,Object],default:function(){return[]}},disabled:{type:Boolean,default:!1},disablePreview:{type:Boolean,default:!1},delIcon:{type:Boolean,default:!0},autoUpload:{type:Boolean,default:!0},limit:{type:[Number,String],default:9},mode:{type:String,default:"grid"},fileMediatype:{type:String,default:"image"},fileExtname:{type:[Array,String],default:function(){return[]}},title:{type:String,default:""},listStyles:{type:Object,default:function(){return{border:!0,dividline:!0,borderStyle:{}}}},imageStyles:{type:Object,default:function(){return{width:"auto",height:"auto"}}},readonly:{type:Boolean,default:!1},returnType:{type:String,default:"array"},sizeType:{type:Array,default:function(){return["original","compressed"]}},sourceType:{type:Array,default:function(){return["album","camera"]}},provider:{type:String,default:""}},data:function(){return{files:[],localValue:[]}},watch:{value:{handler:function(e,t){this.setValue(e,t)},immediate:!0},modelValue:{handler:function(e,t){this.setValue(e,t)},immediate:!0}},computed:{filesList:function(){var e=[];return this.files.forEach((function(t){e.push(t)})),e},showType:function(){return"image"===this.fileMediatype?this.mode:"list"},limitLength:function(){return"object"===this.returnType?1:this.limit?this.limit>=9?9:this.limit:1}},created:function(){e.config&&e.config.provider||(this.noSpace=!0,e.chooseAndUploadFile=c.chooseAndUploadFile),this.form=this.getForm("uniForms"),this.formItem=this.getForm("uniFormsItem"),this.form&&this.formItem&&this.formItem.name&&(this.rename=this.formItem.name,this.form.inputChildrens.push(this))},methods:{clearFiles:function(e){var t=this;0===e||e?this.files.splice(e,1):(this.files=[],this.$nextTick((function(){t.setEmit()}))),this.$nextTick((function(){t.setEmit()}))},upload:function(){var e=[];return this.files.forEach((function(t,n){"ready"!==t.status&&"error"!==t.status||e.push(Object.assign({},t))})),this.uploadFiles(e)},setValue:function(e,t){var n=this;return(0,u.default)((0,s.default)().mark((function t(){var r,a,i,o;return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=function(){var e=(0,u.default)((0,s.default)().mark((function e(t){var r,a;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=/cloud:\/\/([\w.]+\/?)\S*/,a="",a=t.fileID?t.fileID:t.url,!r.test(a)){e.next=8;break}return t.fileID=a,e.next=7,n.getTempFileURL(a);case 7:t.url=e.sent;case 8:return t.url&&(t.path=t.url),e.abrupt("return",t);case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),"object"!==n.returnType){t.next=10;break}if(!e){t.next=7;break}return t.next=5,r(e);case 5:t.next=8;break;case 7:e={};case 8:t.next=19;break;case 10:e||(e=[]),a=0;case 12:if(!(a<e.length)){t.next=19;break}return i=e[a],t.next=16,r(i);case 16:a++,t.next=12;break;case 19:n.localValue=e,n.form&&n.formItem&&!n.is_reset&&(n.is_reset=!1,n.formItem.setValue(n.localValue)),o=Object.keys(e).length>0?e:[],n.files=[].concat(o);case 23:case"end":return t.stop()}}),t)})))()},choose:function(){this.disabled||(this.files.length>=Number(this.limitLength)&&"grid"!==this.showType&&"array"===this.returnType?uni.showToast({title:"您最多选择 ".concat(this.limitLength," 个文件"),icon:"none"}):this.chooseFiles())},chooseFiles:function(){var t=this,n=(0,l.get_extname)(this.fileExtname);e.chooseAndUploadFile({type:this.fileMediatype,compressed:!1,sizeType:this.sizeType,sourceType:this.sourceType,extension:n.length>0?n:void 0,count:this.limitLength-this.files.length,onChooseFile:this.chooseFileCallback,onUploadProgress:function(e){t.setProgress(e,e.index)}}).then((function(e){t.setSuccessAndError(e.tempFiles)})).catch((function(e){r.log("选择失败",e)}))},chooseFileCallback:function(e){var t=this;return(0,u.default)((0,s.default)().mark((function n(){var r,a,i,u,c,d,f,p;return(0,s.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:r=(0,l.get_extname)(t.fileExtname),a=1===Number(t.limitLength)&&t.disablePreview&&!t.disabled||"object"===t.returnType,a&&(t.files=[]),i=(0,l.get_files_and_is_max)(e,r),u=i.filePaths,c=i.files,r&&r.length>0||(u=e.tempFilePaths,c=e.tempFiles),d=[],f=0;case 7:if(!(f<c.length)){n.next=21;break}if(!(t.limitLength-t.files.length<=0)){n.next=10;break}return n.abrupt("break",21);case 10:return c[f].uuid=Date.now(),n.next=13,(0,l.get_file_data)(c[f],t.fileMediatype);case 13:p=n.sent,p.progress=0,p.status="ready",t.files.push(p),d.push((0,o.default)((0,o.default)({},p),{},{file:c[f]}));case 18:f++,n.next=7;break;case 21:t.$emit("select",{tempFiles:d,tempFilePaths:u}),e.tempFiles=c,t.autoUpload&&!t.noSpace||(e.tempFiles=[]),e.tempFiles.forEach((function(e,n){t.provider&&(e.provider=t.provider);var r=e.name.split("."),a=r.pop(),i=r.join(".").replace(/[\s\/\?<>\\:\*\|":]/g,"_");e.cloudPath=i+"_"+Date.now()+"_"+n+"."+a}));case 25:case"end":return n.stop()}}),n)})))()},uploadFiles:function(e){var t=this;return e=[].concat(e),c.uploadCloudFiles.call(this,e,5,(function(e){t.setProgress(e,e.index,!0)})).then((function(e){return t.setSuccessAndError(e),e})).catch((function(e){r.log(e)}))},setSuccessAndError:function(e,t){var n=this;return(0,u.default)((0,s.default)().mark((function t(){var r,a,i,o,u,c,l;return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:r=[],a=[],i=[],o=[],u=(0,s.default)().mark((function t(u){var c,l,d;return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(c=e[u],l=c.uuid?n.files.findIndex((function(e){return e.uuid===c.uuid})):c.index,-1!==l&&n.files){t.next=4;break}return t.abrupt("return","break");case 4:if("request:fail"!==c.errMsg){t.next=12;break}n.files[l].url=c.path,n.files[l].status="error",n.files[l].errMsg=c.errMsg,a.push(n.files[l]),o.push(n.files[l].url),t.next=26;break;case 12:if(n.files[l].errMsg="",n.files[l].fileID=c.url,d=/cloud:\/\/([\w.]+\/?)\S*/,!d.test(c.url)){t.next=21;break}return t.next=18,n.getTempFileURL(c.url);case 18:n.files[l].url=t.sent,t.next=22;break;case 21:n.files[l].url=c.url;case 22:n.files[l].status="success",n.files[l].progress+=1,r.push(n.files[l]),i.push(n.files[l].fileID);case 26:case"end":return t.stop()}}),t)})),c=0;case 6:if(!(c<e.length)){t.next=14;break}return t.delegateYield(u(c),"t0",8);case 8:if(l=t.t0,"break"!==l){t.next=11;break}return t.abrupt("break",14);case 11:c++,t.next=6;break;case 14:r.length>0&&(n.setEmit(),n.$emit("success",{tempFiles:n.backObject(r),tempFilePaths:i})),a.length>0&&n.$emit("fail",{tempFiles:n.backObject(a),tempFilePaths:o});case 16:case"end":return t.stop()}}),t)})))()},setProgress:function(e,t,n){this.files.length;var r=Math.round(100*e.loaded/e.total),a=t;n||(a=this.files.findIndex((function(t){return t.uuid===e.tempFile.uuid}))),-1!==a&&this.files[a]&&(this.files[a].progress=r-1,this.$emit("progress",{index:a,progress:parseInt(r),tempFile:this.files[a]}))},delFile:function(e){var t=this;this.$emit("delete",{index:e,tempFile:this.files[e],tempFilePath:this.files[e].url}),this.files.splice(e,1),this.$nextTick((function(){t.setEmit()}))},getFileExt:function(e){var t=e.lastIndexOf("."),n=e.length;return{name:e.substring(0,t),ext:e.substring(t+1,n)}},setEmit:function(){var e=[];"object"===this.returnType?(e=this.backObject(this.files)[0],this.localValue=e||null):(e=this.backObject(this.files),this.localValue||(this.localValue=[]),this.localValue=(0,i.default)(e)),this.$emit("input",this.localValue)},backObject:function(e){var t=[];return e.forEach((function(e){t.push({extname:e.extname,fileType:e.fileType,image:e.image,name:e.name,path:e.path,size:e.size,fileID:e.fileID,url:e.url,uuid:e.uuid,status:e.status,cloudPath:e.cloudPath})})),t},getTempFileURL:function(t){return(0,u.default)((0,s.default)().mark((function n(){var r;return(0,s.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t={fileList:[].concat(t)},n.next=3,e.getTempFileURL(t);case 3:return r=n.sent,n.abrupt("return",r.fileList[0].tempFileURL||"");case 5:case"end":return n.stop()}}),n)})))()},getForm:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniForms",t=this.$parent,n=t.$options.name;while(n!==e){if(t=t.$parent,!t)return!1;n=t.$options.name}return t}}};t.default=p}).call(this,n("861b")["uniCloud"],n("ba7c")["default"])},f37b:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-text",{staticClass:"u-link",style:[e.linkStyle,e.$u.addStyle(e.customStyle)],on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.openLink.apply(void 0,arguments)}}},[e._v(e._s(e.text))])},a=[]},f3ee:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/login/login"},{path:"pages/index/index",style:{navigationBarTitleText:"职业健康达人",titleNView:!1,navigationStyle:"custom"}},{path:"pages/login/bindPhoneNum"},{path:"pages/login/bindWxInfo"},{path:"pages/index/signature"},{path:"pages/login/zfbAutho",style:{navigationBarTitleText:"支付宝授权"}},{path:"pages/index/search"},{path:"pages/reorientation/reorientation",style:{navigationBarTitleText:"",enablePullDownRefresh:!1}},{path:"pages/institution/index"},{path:"pages/institution/tjRecord"},{path:"pages/institution/tjResult"},{path:"pages/institution/tjBooking"},{path:"pages/institution/tjAppoint"},{path:"pages/institution/tjMessage"},{path:"pages/institution/tjAuth"},{path:"pages/institution/institution"},{path:"pages/institution/jgDetail"},{path:"pages/institution/jgForm"},{path:"pages/institution/zdResult"},{path:"pages/institution/ysReport"},{path:"pages/institution/addInformation"},{path:"pages/lifeCycle/lifeCycle",style:{navigationBarTitleText:"生命周期管理"}},{path:"pages/workInjuryRecognition/index"},{path:"pages/workInjuryRecognition/add"},{path:"pages/workInjuryRecognition/detail"}],subPackages:[{root:"pages_train",pages:[{path:"pages/training/faceInput"},{path:"pages/training/faceValid"},{path:"pages/training/courses/courses"},{path:"pages/training/courses/course"},{path:"pages/training/courses/courseWithoutPersonal"},{path:"pages/training/courses/document"},{path:"pages/training/courses/search"},{path:"pages/training/myTraining",style:{enablePullDownRefresh:!0}},{path:"pages/training/detail",style:{enablePullDownRefresh:!0}},{path:"pages/training/test"},{path:"pages/training/myCourses",style:{enablePullDownRefresh:!0}},{path:"pages/training/publicCourses",style:{enablePullDownRefresh:!0}},{path:"pages/training/testAnswer"},{path:"pages/training/testResult"},{path:"pages/training/certificate"},{path:"pages/training/publicCourseDetail"}]},{root:"pages_learning",pages:[{path:"pages/learning/artInfo"},{path:"pages/learning/industryNews"}]},{root:"pages_remote",pages:[{path:"pages/remote/list"},{path:"pages/remote/meeting"}]},{root:"pages_user",pages:[{path:"pages/user/boundEnterprise"},{path:"pages/user/h5login"},{path:"pages/user/ppe"},{path:"pages/user/info"},{path:"pages/user/modify"},{path:"pages/user/modifyPhone"},{path:"pages/user/comment"},{path:"pages/user/myTraining"},{path:"pages/identify/index"},{path:"pages/identify/apply"},{path:"pages/identify/jgForm"},{path:"pages/identify/jgDetail"},{path:"pages/identify/jdResult"},{path:"pages/user/complaints/list",style:{softinputMode:"adjustResize",enablePullDownRefresh:!0}},{path:"pages/user/signImg",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/signature",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/checkDetail",style:{enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"pages/user/ppeSign",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/complaints/detail",style:{softinputMode:"adjustResize"}},{path:"pages/user/complaints/scoring"},{path:"pages/user/physicalExamination"},{path:"pages/user/occupationalHistory"},{path:"pages/user/physicalAndCheckResult"},{path:"pages/user/diseasesOverhaul"},{path:"pages/user/diseasesOverhaulDetail"},{path:"pages/user/message"},{path:"pages/user/violationInfo"},{path:"pages/user/questionnaire"},{path:"pages/user/questionnaireDetail"},{path:"pages/user/tjAppointmentInfo"},{path:"pages/user/tjAppointmentDetailInfo"},{path:"pages/user/employeeList"},{path:"pages/user/addItems"},{path:"pages/user/indicatorsTrend"},{path:"pages/eHealthRecord/index"},{path:"pages/eHealthRecord/auth"},{path:"pages/eHealthRecord/complaint"},{path:"pages/eHealthRecord/basicInfo"},{path:"pages/eHealthRecord/exam"},{path:"pages/eHealthRecord/diagnose"},{path:"pages/eHealthRecord/injury"},{path:"pages/eHealthRecord/healthManage"},{path:"pages/eHealthRecord/servic"},{path:"pages/eHealthRecord/report"},{path:"pages/eHealthRecord/warning"},{path:"pages/eHealthRecord/harmFactor"},{path:"pages/workInjury/query"}]},{root:"pages_lifeCycle",pages:[{path:"/pages/followUp/followUp",style:{navigationBarTitleText:"随访记录"}},{path:"/pages/Appointment/Appointment",style:{navigationBarTitleText:"就诊预约"}},{path:"/pages/Appointment/AppointmentRecord",style:{navigationBarTitleText:"预约记录"}},{path:"/pages/MedicationServices/MedicationServices",style:{navigationBarTitleText:"用药服务"}},{path:"/pages/MedicationServices/MedicationServicesInfo",style:{navigationBarTitleText:"用药详情"}},{path:"/pages/treatmentService/treatmentService",style:{navigationBarTitleText:"诊疗服务"}},{path:"/pages/treatmentService/treatmentServiceInfo",style:{navigationBarTitleText:"诊疗详情"}},{path:"/pages/recoveredServices/recoveredServices",style:{navigationBarTitleText:"康复指导服务"}},{path:"/pages/recoveredServices/addrecoveredServices",style:{navigationBarTitleText:"申请康复指导"}},{path:"/pages/recoveredServices/recoveredServicesRecord",style:{navigationBarTitleText:"服务记录"}}]}],globalStyle:{"mp-360":{navigationStyle:"custom"},"mp-alipay":{transparentTitle:"always",allowsBounceVertical:"NO"},navigationStyle:"custom",navigationBarTextStyle:"black","app-plus":{scrollIndicator:"none",bounce:"none"}},usingComponts:!0,easycom:{custom:{autoscan:!1,"grace(.*)":"@/graceUI/components/grace$1.vue"}},condition:{current:0,list:[{name:"",path:"",query:""}]}}},f441:function(e,t,n){"use strict";n.r(t);var r=n("5ef4"),a=n("8d0d");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"d782867e",null,!1,r["a"],void 0);t["default"]=s.exports},f478:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},n("7a76"),n("c9b5")},f555:function(e,t,n){"use strict";var r=n("85c1"),a=n("ab4a"),i=n("e4ca"),o=n("471d"),s=n("af9e"),u=r.RegExp,c=u.prototype,l=a&&s((function(){var e=!0;try{u(".","d")}catch(l){e=!1}var t={},n="",r=e?"dgimsy":"gimsy",a=function(e,r){Object.defineProperty(t,e,{get:function(){return n+=r,!0}})},i={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var o in e&&(i.hasIndices="d"),i)a(o,i[o]);var s=Object.getOwnPropertyDescriptor(c,"flags").get.call(t);return s!==r||n!==r}));l&&i(c,"flags",{configurable:!0,get:o})},f864:function(e,t,n){"use strict";n.r(t);var r=n("6f0c"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},fa22:function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("ff4d")),i={name:"u-link",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],computed:{linkStyle:function(){var e={color:this.color,fontSize:uni.$u.addUnit(this.fontSize),lineHeight:uni.$u.addUnit(uni.$u.getPx(this.fontSize)+2),textDecoration:this.underLine?"underline":"none"};return e}},methods:{openLink:function(){window.open(this.href),this.$emit("click")}}};t.default=i},fac4:function(e,t,n){var r=n("7391");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("b8f32bc0",r,!0,{sourceMap:!1,shadowMode:!1})},fe00:function(e,t,n){"use strict";n.r(t);var r=n("4846"),a=n("da54");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=s.exports},fea2:function(e,t,n){"use strict";n.r(t);var r=n("70db"),a=n("8565");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=s.exports},fef6:function(e,t,n){"use strict";n.r(t);var r=n("0322"),a=n("d598");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("2a68");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"7aac8588",null,!1,r["a"],void 0);t["default"]=s.exports},ff4d:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={props:{color:{type:String,default:uni.$u.props.link.color},fontSize:{type:[String,Number],default:uni.$u.props.link.fontSize},underLine:{type:Boolean,default:uni.$u.props.link.underLine},href:{type:String,default:uni.$u.props.link.href},mpTips:{type:String,default:uni.$u.props.link.mpTips},lineColor:{type:String,default:uni.$u.props.link.lineColor},text:{type:String,default:uni.$u.props.link.text}}};t.default=r}}]);