(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-diagnose"],{"092e":function(t,e,a){"use strict";a.r(e);var i=a("b9e4"),n=a("cdfb");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("4c97");var o=a("828b"),l=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"627031ea",null,!1,i["a"],void 0);e["default"]=l.exports},"1cb9":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("e40e")),s={getEHealthRecordAuthList:function(t){return(0,n.default)({url:"manage/eHealthRecord/getEHealthRecordAuthList",method:"get",data:t})},handleEHealthRecordAuth:function(t){return(0,n.default)({url:"manage/eHealthRecord/handleEHealthRecordAuth",method:"post",data:t})},getEHealthRecordBaseInfo:function(t){return(0,n.default)({url:"manage/eHealthRecord/getEHealthRecordBaseInfo",method:"get",data:t})},updateEHealthRecordBaseInfo:function(t){return(0,n.default)({url:"manage/eHealthRecord/updateEHealthRecordBaseInfo",method:"post",data:t})},getSupervisionList:function(t){return(0,n.default)({url:"manage/eHealthRecord/getSupervisionList",method:"post",data:t})},postComplaint:function(t){return(0,n.default)({url:"manage/eHealthRecord/postComplaint",method:"post",data:t})},getLaborIsEdit:function(t){return(0,n.default)({url:"manage/eHealthRecord/getLaborIsEdit",method:"get",data:t})},addEmploymentHistory:function(t){return(0,n.default)({url:"manage/eHealthRecord/addEmploymentHistory",method:"post",data:t})},editEmploymentHistory:function(t){return(0,n.default)({url:"manage/eHealthRecord/editEmploymentHistory",method:"post",data:t})},deleteEmploymentHistory:function(t){return(0,n.default)({url:"manage/eHealthRecord/deleteEmploymentHistory",method:"post",data:t})},getEmploymentHistory:function(t){return(0,n.default)({url:"manage/eHealthRecord/getEmploymentHistory",method:"get",data:t})},getDiaList:function(t){return(0,n.default)({url:"manage/eHealthRecord/getDiaList",method:"post",data:t})},getIdentificationList:function(t){return(0,n.default)({url:"manage/eHealthRecord/getIdentificationList",method:"post",data:t})},findHarmFactors:function(t){return(0,n.default)({url:"manage/eHealthRecord/findHarmFactors",method:"get",data:t})}};e.default=s},"23e6":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var n=i(a("8e98")),s={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:n.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=s},"28af":function(t,e,a){"use strict";a.r(e);var i=a("8f2b"),n=a("76e3");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("bb00");var o=a("828b"),l=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"5c0264f4",null,!1,i["a"],void 0);e["default"]=l.exports},3129:function(t,e,a){var i=a("63f7");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("0a470295",i,!0,{sourceMap:!1,shadowMode:!1})},"3c55":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("2634")),s=i(a("2fdc")),o=i(a("9b1b")),l=i(a("1cb9")),r=a("8f59"),c=i(a("07b0")),d={data:function(){return{tabs:["诊断记录","鉴定记录","就诊记录"],currentTab:0,isRefreshing:!1,diagnosisRecords:[],identificationRecords:[],medicalRecords:[],postData:{status:"",idCardType:"1",idCardCode:"",pageNum:1,pageSize:100}}},computed:(0,o.default)({},(0,r.mapGetters)(["userInfo"])),mounted:function(){var t;this.postData.idCardCode=(null===(t=this.userInfo)||void 0===t?void 0:t.idNo)||"",this.getDiagnosisList(),this.getIdentificationList()},methods:{downloadCertificate:function(t){t.certificateUrl?window.open(t.certificateUrl,"_blank"):uni.showToast({title:"当前诊断记录暂无诊断证明书",icon:"none"})},getDiagnosisList:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l.default.getDiaList(t.postData);case 2:a=e.sent,t.diagnosisRecords=a.data;case 4:case"end":return e.stop()}}),e)})))()},getIdentificationList:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l.default.getIdentificationList(t.postData);case 2:a=e.sent,t.identificationRecords=a.data;case 4:case"end":return e.stop()}}),e)})))()},switchTab:function(t){this.currentTab=t},toggleExpand:function(t){this.medicalRecords[t].expanded=!this.medicalRecords[t].expanded},formatDate:function(t){return(0,c.default)(t).format("YYYY-MM-DD")},formatDateYear:function(t){return(0,c.default)(t).format("YYYY")+"年"},formatDateMonthDay:function(t){return(0,c.default)(t).format("MM")+"月"+(0,c.default)(t).format("DD")+"日"},getStatusClass:function(t){return{"-1":"status-danger",0:"status-warning",1:"status-success",2:"status-info",4:"status-warning",99:"status-success"}[t]||""},getDiagnosisResult:function(t){return t.diagnosisConclusionDescription?t.diagnosisConclusionDescription:t.hasOccupationalDisease?"患有职业病":"未患有职业病"},getDeterminationResult:function(t){return t.determinationConclusionDescription?t.determinationConclusionDescription:t.hasOccupationalDisease?"患有职业病":"未患有职业病"}}};e.default=d},"4c97":function(t,e,a){"use strict";var i=a("c2b6"),n=a.n(i);n.a},"63f7":function(t,e,a){var i=a("c86c"),n=a("2ec5"),s=a("e549");e=i(!1);var o=n(s);e.push([t.i,"@font-face{font-family:uniicons;src:url("+o+') format("truetype")}.uni-icons[data-v-5c0264f4]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},"76e3":function(t,e,a){"use strict";a.r(e);var i=a("23e6"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"8e98":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},"8f2b":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},n=[]},b9e4:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={gracePage:a("93fe").default,uniIcons:a("28af").default,uButton:a("7a42").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"诊断医疗记录"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"tabs"},t._l(t.tabs,(function(e,i){return a("v-uni-view",{key:i,staticClass:"tab-item",class:{active:t.currentTab===i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchTab(i)}}},[a("v-uni-text",[t._v(t._s(e))])],1)})),1),a("v-uni-scroll-view",{staticClass:"content-area",attrs:{"scroll-y":!0}},[0===t.currentTab?a("v-uni-view",{staticClass:"record-list"},[0===t.diagnosisRecords.length?a("v-uni-view",{staticClass:"empty-data"},[a("v-uni-view",{staticClass:"empty-icon"},[a("uni-icons",{attrs:{type:"info",size:"60",color:"#c0c4cc"}})],1),a("v-uni-view",{staticClass:"empty-text"},[t._v("暂无诊断记录")])],1):t._e(),t._l(t.diagnosisRecords,(function(e,i){return a("v-uni-view",{key:i,staticClass:"record-card"},[a("v-uni-view",{staticClass:"time-line"},[a("v-uni-view",{staticClass:"date-box"},[a("v-uni-text",{staticClass:"date-year"},[t._v(t._s(t.formatDateYear(e.diagnosisDate)))]),a("v-uni-text",{staticClass:"date"},[t._v(t._s(t.formatDateMonthDay(e.diagnosisDate)))])],1),a("v-uni-view",{staticClass:"line"})],1),a("v-uni-view",{staticClass:"card-content"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("用人单位名称：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.employerName))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("诊断结论：")]),a("v-uni-text",{staticClass:"value highlight"},[t._v(t._s(t.getDiagnosisResult(e)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("处理意见：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.treatmentOpinion))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("诊断机构：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.diagnosisInstitution))])],1),a("v-uni-view",{staticClass:"info-item"},[a("u-button",{staticStyle:{flex:"1"},attrs:{type:"primary",size:"mini"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.downloadCertificate(e)}}},[t._v("点击下载诊断证明书")])],1)],1)],1)}))],2):t._e(),1===t.currentTab?a("v-uni-view",{staticClass:"record-list"},[0===t.identificationRecords.length?a("v-uni-view",{staticClass:"empty-data"},[a("v-uni-view",{staticClass:"empty-icon"},[a("uni-icons",{attrs:{type:"info",size:"60",color:"#c0c4cc"}})],1),a("v-uni-view",{staticClass:"empty-text"},[t._v("暂无鉴定记录")])],1):t._e(),t._l(t.identificationRecords,(function(e,i){return a("v-uni-view",{key:i,staticClass:"record-card"},[a("v-uni-view",{staticClass:"time-line"},[a("v-uni-view",{staticClass:"date-box"},[a("v-uni-text",{staticClass:"date-year"},[t._v(t._s(t.formatDateYear(e.determinationDate)))]),a("v-uni-text",{staticClass:"date"},[t._v(t._s(t.formatDateMonthDay(e.determinationDate)))])],1),a("v-uni-view",{staticClass:"line"})],1),a("v-uni-view",{staticClass:"card-content"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("鉴定类别：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s("2"===e.determinationCategory?"再鉴定":"首次鉴定"))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("用人单位名称：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.employerName))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("申请鉴定主要理由：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.applicationReason))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("鉴定依据：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.determinationBasis))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("鉴定结论：")]),a("v-uni-text",{staticClass:"value highlight"},[t._v(t._s(t.getDeterminationResult(e)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("诊断鉴定委员会：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.determinationCommittee))])],1)],1)],1)}))],2):t._e(),2===t.currentTab?a("v-uni-view",{staticClass:"record-list"},[0===t.medicalRecords.length?a("v-uni-view",{staticClass:"empty-data"},[a("v-uni-view",{staticClass:"empty-icon"},[a("uni-icons",{attrs:{type:"info",size:"60",color:"#c0c4cc"}})],1),a("v-uni-view",{staticClass:"empty-text"},[t._v("暂无就诊记录")])],1):t._e(),t._l(t.medicalRecords,(function(e,i){return a("v-uni-view",{key:i,staticClass:"record-card"},[a("v-uni-view",{staticClass:"time-line"},[a("v-uni-view",{staticClass:"date-box"},[a("v-uni-text",{staticClass:"date-year"},[t._v(t._s(e.date?e.date.substring(0,4)+"年":""))]),a("v-uni-text",{staticClass:"date"},[t._v(t._s(e.date?e.date.substring(5,7)+"月"+e.date.substring(8,10)+"日":""))])],1),a("v-uni-view",{staticClass:"line"})],1),a("v-uni-view",{staticClass:"card-content"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-text",{staticClass:"hospital"},[t._v(t._s(e.hospital))]),a("v-uni-text",{staticClass:"department"},[t._v(t._s(e.department))])],1),a("v-uni-view",{staticClass:"detail-content",class:{expanded:e.expanded}},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("主诉：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.complaint))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("诊断结果：")]),a("v-uni-text",{staticClass:"value highlight"},[t._v(t._s(e.diagnosis))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("治疗措施：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(e.treatment))])],1)],1),a("v-uni-view",{staticClass:"expand-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleExpand(i)}}},[a("v-uni-text",[t._v(t._s(e.expanded?"收起":"展开"))]),a("uni-icons",{attrs:{type:e.expanded?"top":"bottom",size:"14"}})],1)],1)],1)}))],2):t._e()],1)],1)],1)},s=[]},bb00:function(t,e,a){"use strict";var i=a("3129"),n=a.n(i);n.a},c2b6:function(t,e,a){var i=a("e5ec");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("efb51804",i,!0,{sourceMap:!1,shadowMode:!1})},cdfb:function(t,e,a){"use strict";a.r(e);var i=a("3c55"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},e549:function(t,e,a){t.exports=a.p+"assets/uni.75745d34.ttf"},e5ec:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.container[data-v-627031ea]{display:flex;flex-direction:column;height:100%;background-color:#f5f5f5}.tabs[data-v-627031ea]{display:flex;height:%?88?%;background-color:#fff;border-bottom:1px solid #ebeef5;flex-shrink:0;width:107%;position:relative;left:-12px}.tab-item[data-v-627031ea]{flex:1;display:flex;justify-content:center;align-items:center;font-size:14px;color:#606266;position:relative}.tab-item.active[data-v-627031ea]{color:#409eff;font-weight:500}.tab-item.active[data-v-627031ea]::after{content:"";position:absolute;bottom:0;width:%?40?%;height:%?4?%;background-color:#409eff;border-radius:%?2?%}.content-area[data-v-627031ea]{flex:1;overflow:auto}.record-list[data-v-627031ea]{padding:%?20?%}.record-card[data-v-627031ea]{display:flex;margin-bottom:%?30?%}.time-line[data-v-627031ea]{width:%?138?%;display:flex;flex-direction:column;align-items:center;flex-shrink:0}.date-box[data-v-627031ea]{background-color:#409eff;padding:%?10?% %?20?%;border-radius:%?6?%;display:flex;flex-direction:column;align-items:center;min-width:%?100?%}.date-year[data-v-627031ea]{color:#fff;font-size:12px;margin-bottom:%?4?%}.date[data-v-627031ea]{color:#fff;font-size:12px}.line[data-v-627031ea]{width:%?2?%;height:100%;background-color:#dcdfe6;margin-top:%?20?%}.card-content[data-v-627031ea]{flex:1;background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-left:%?20?%;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.05)}.info-item[data-v-627031ea]{margin-bottom:%?20?%}.label[data-v-627031ea]{color:#909399;font-size:14px}.value[data-v-627031ea]{color:#303133;font-size:14px}.highlight[data-v-627031ea]{color:#409eff;font-weight:500}.type-tag[data-v-627031ea]{display:inline-block;padding:%?6?% %?20?%;background-color:#ecf5ff;color:#409eff;border-radius:%?4?%;font-size:12px;margin-bottom:%?20?%}.status-tag[data-v-627031ea]{display:inline-block;padding:%?4?% %?16?%;border-radius:%?20?%;font-size:%?24?%;margin-bottom:%?20?%}.status-tag.status-success[data-v-627031ea]{background:#e1f3d8;color:#67c23a}.status-tag.status-warning[data-v-627031ea]{background:#fdf6ec;color:#e6a23c}.status-tag.status-info[data-v-627031ea]{background:#ecf5ff;color:#409eff}.status-tag.status-danger[data-v-627031ea]{background:#fef0f0;color:#f56c6c}.first-identification .type-tag[data-v-627031ea]{background-color:#f0f9eb;color:#67c23a}.header[data-v-627031ea]{margin-bottom:%?20?%}.hospital[data-v-627031ea]{font-size:16px;color:#303133;font-weight:500;margin-right:%?20?%}.department[data-v-627031ea]{font-size:14px;color:#909399}.detail-content[data-v-627031ea]{max-height:%?104?%;overflow:hidden;transition:max-height .3s ease-in-out}.detail-content.expanded[data-v-627031ea]{max-height:%?800?%}.expand-btn[data-v-627031ea]{display:flex;align-items:center;justify-content:center;margin-top:%?20?%;color:#909399;font-size:14px}.empty-data[data-v-627031ea]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0}.empty-data .empty-icon[data-v-627031ea]{margin-bottom:%?20?%}.empty-data .empty-text[data-v-627031ea]{font-size:%?28?%;color:#909399}',""]),t.exports=e}}]);