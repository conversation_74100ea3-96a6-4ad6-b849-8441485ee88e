(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-occupationalHistory"],{1751:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={gracePage:i("93fe").default,uniPopup:i("7975").default,uniDatetimePicker:i("d374").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[i("my-header",{attrs:{slot:"gHeader",title:"个人职业史"},slot:"gHeader"}),i("v-uni-view",{staticClass:"grace-body",staticStyle:{"padding-right":"0"},attrs:{slot:"gBody"},slot:"gBody"},[i("v-uni-view",{staticClass:"addHistory",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addHistory.apply(void 0,arguments)}}},[t._v("+")]),i("v-uni-view",{staticClass:"cu-item"},[i("v-uni-view",{staticClass:"grace-table"},[i("v-uni-view",{staticClass:"grace-theader",staticStyle:{"background-color":"rgba(54, 135, 255, 0.8)",color:"#fff"}},[i("v-uni-text",{staticClass:"grace-td",staticStyle:{width:"320rpx","font-weight":"bold",border:"none","font-size":"30rpx"}},[t._v("起止时间")]),i("v-uni-text",{staticClass:"grace-td",staticStyle:{width:"320rpx","font-weight":"bold",border:"none","font-size":"30rpx","text-align":"center","padding-left":"80rpx"}},[t._v("工作单位")]),i("v-uni-text",{staticClass:"grace-td",staticStyle:{width:"10em","font-weight":"bold",border:"none","font-size":"30rpx","text-align":"center","padding-left":"20rpx"}},[t._v("操作")])],1),t._l(t.tableData,(function(e,a){return i("v-uni-view",{key:a,staticClass:"grace-tbody",staticStyle:{"border-left":"none"}},[i("v-uni-view",{staticClass:"grace-td td",staticStyle:{width:"525rpx"}},[i("v-uni-text",[t._v(t._s(e.timeSlot))])],1),i("v-uni-view",{staticClass:"grace-td td",staticStyle:{width:"510rpx"}},[i("v-uni-text",[t._v(t._s(e.workUnit))])],1),i("v-uni-view",{staticClass:"grace-td td",staticStyle:{width:"355rpx"}},[i("v-uni-view",{staticClass:"tableBut",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.viewDetail(e,a)}}},[t._v("查看")])],1)],1)}))],2)],1),i("uni-popup",{ref:"popup",attrs:{type:"dialog"}},[i("v-uni-view",{staticClass:"form-box"},[i("v-uni-form",{on:{submit:function(e){arguments[0]=e=t.$handleEvent(e),t.editSubmit.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticClass:"requireStar",staticStyle:{"font-weight":"600"}},[t._v("起止时间")]),i("v-uni-view",{staticClass:"example-body"},[t.currentIsEdit?i("v-uni-view",{staticStyle:{display:"flex","align-items":"center"}},[i("uni-datetime-picker",{attrs:{placeholder:"请选择开始日期",type:"date",name:"timeFrom"},model:{value:t.startTime,callback:function(e){t.startTime=e},expression:"startTime"}}),i("v-uni-text",[t._v("至")]),i("uni-datetime-picker",{attrs:{placeholder:"请选择结束日期",type:"date",name:"timeTo"},model:{value:t.endTime,callback:function(e){t.endTime=e},expression:"endTime"}})],1):i("v-uni-view",{staticClass:"detailMsg"},[t._v(t._s(t.detailForm.timeSlot))])],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticClass:"requireStar",staticStyle:{"font-weight":"600"}},[t._v("工作单位")]),i("v-uni-view",{staticClass:"example-body"},[t.currentIsEdit?i("v-uni-input",{attrs:{type:"text",placeholder:"请输入工作单位",name:"workUnit",value:t.detailForm.workUnit,"placeholder-class":"placeholder-style"}}):i("v-uni-view",{staticClass:"detailMsg"},[t._v(t._s(t.detailForm.workUnit))])],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticStyle:{"font-weight":"600"}},[t._v("车间")]),i("v-uni-view",{staticClass:"example-body"},[t.currentIsEdit?i("v-uni-input",{attrs:{type:"text",placeholder:"请输入所在车间",name:"workshop",value:t.detailForm.workshop,"placeholder-class":"placeholder-style"}}):i("v-uni-view",{staticClass:"detailMsg"},[t._v(t._s(t.detailForm.workshop))])],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticStyle:{"font-weight":"600"}},[t._v("岗位")]),i("v-uni-view",{staticClass:"example-body"},[t.currentIsEdit?i("v-uni-input",{attrs:{type:"text",placeholder:"请输入工作岗位",name:"station",value:t.detailForm.station,"placeholder-class":"placeholder-style"}}):i("v-uni-view",{staticClass:"detailMsg"},[t._v(t._s(t.detailForm.station))])],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticStyle:{"font-weight":"600"}},[t._v("工种")]),i("v-uni-view",{staticClass:"example-body"},[t.currentIsEdit?i("v-uni-input",{attrs:{type:"text",placeholder:"请输入所属工种",name:"workType",value:t.detailForm.workType,"placeholder-class":"placeholder-style"}}):i("v-uni-view",{staticClass:"detailMsg"},[t._v(t._s(t.detailForm.workType))])],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column",staticStyle:{"margin-top":"80rpx"}},[i("v-uni-view",{staticClass:"example-body",staticStyle:{display:"flex","justify-content":"space-between"}},[t.currentIsEdit?i("v-uni-button",{staticStyle:{"border-radius":"5rpx","background-color":"#42b983",color:"#fff"},attrs:{size:"mini","form-type":"submit"}},[t._v("保存")]):i("v-uni-button",{staticStyle:{"border-radius":"5rpx"},attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleEdit.apply(void 0,arguments)}}},[t._v("编辑")]),t.currentIsEdit?i("v-uni-button",{staticStyle:{"border-radius":"5rpx"},attrs:{size:"mini",type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelEdit.apply(void 0,arguments)}}},[t._v("取消")]):t._e(),i("v-uni-button",{staticStyle:{"border-radius":"5rpx"},attrs:{type:"warn",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteOne.apply(void 0,arguments)}}},[t._v("删除")])],1)],1)],1)],1)],1),i("uni-popup",{ref:"addPopup",attrs:{type:"dialog"}},[i("v-uni-view",{staticClass:"form-box"},[i("v-uni-form",{on:{submit:function(e){arguments[0]=e=t.$handleEvent(e),t.formSubmit.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticClass:"requireStar",staticStyle:{"font-weight":"600"}},[t._v("起止时间")]),i("v-uni-view",{staticClass:"example-body",staticStyle:{display:"flex","align-items":"center"}},[i("uni-datetime-picker",{attrs:{placeholder:"请选择开始日期",type:"date",name:"timeFrom"},model:{value:t.startTime,callback:function(e){t.startTime=e},expression:"startTime"}}),i("v-uni-text",[t._v("至")]),i("uni-datetime-picker",{attrs:{placeholder:"请选择结束日期",type:"date",name:"timeTo"},model:{value:t.endTime,callback:function(e){t.endTime=e},expression:"endTime"}})],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticClass:"requireStar",staticStyle:{"font-weight":"600"}},[t._v("工作单位")]),i("v-uni-view",{staticClass:"example-body"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入工作单位",name:"workUnit","placeholder-class":"placeholder-style"},model:{value:t.workUnit,callback:function(e){t.workUnit=e},expression:"workUnit"}})],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticStyle:{"font-weight":"600"}},[t._v("车间")]),i("v-uni-view",{staticClass:"example-body"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入所在车间",name:"workshop","placeholder-class":"placeholder-style"},model:{value:t.workshop,callback:function(e){t.workshop=e},expression:"workshop"}})],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticStyle:{"font-weight":"600"}},[t._v("岗位")]),i("v-uni-view",{staticClass:"example-body"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入工作岗位",name:"station","placeholder-class":"placeholder-style"},model:{value:t.station,callback:function(e){t.station=e},expression:"station"}})],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column"},[i("v-uni-view",{staticStyle:{"font-weight":"600"}},[t._v("工种")]),i("v-uni-view",{staticClass:"example-body"},[i("v-uni-input",{attrs:{type:"text",placeholder:"请输入所属工种",name:"workType","placeholder-class":"placeholder-style"},model:{value:t.workType,callback:function(e){t.workType=e},expression:"workType"}})],1)],1),i("v-uni-view",{staticClass:"uni-form-item uni-column",staticStyle:{"margin-top":"80rpx"}},[i("v-uni-view",{staticClass:"example-body",staticStyle:{"text-align":"center"}},[i("v-uni-button",{attrs:{type:"primary","form-type":"submit",size:"mini"}},[t._v("保存")]),i("v-uni-button",{staticStyle:{"margin-left":"50rpx"},attrs:{type:"warn",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelAdd.apply(void 0,arguments)}}},[t._v("取消")])],1)],1)],1)],1)],1)],1)],1)},r=[]},"31da":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("dd2b"),i("bf0f"),i("2797"),i("aa9c");var n=a(i("b7c7")),r=a(i("9b1b")),s=a(i("2634")),o=a(i("2fdc")),d=a(i("fcf3")),l=a(i("7d61")),u={data:function(){return{startTime:"",endTime:"",workUnit:"",workshop:"",station:"",workType:"",range:"",historyId:"",hasEditPermission:!0,currentIsEdit:!1,newAddForm:{},editForm:{},detailForm:{},tableData:[],tableConfig:[{label:"起止时间",align:"center",width:90,key:"name"},{label:"工作单位",align:"center",width:90,key:"age"},{label:"操作",align:"center",width:50}]}},onLoad:function(){var t=this.$store.state.user.userInfo.employeeId;if(this.getHistoryList(t),"object"===(0,d.default)(this.$store.state.user.userInfo.companyId)){var e=this.$store.state.user.userInfo.companyId;this.getLaborIsEdit(e[e.length-1])}else{var i=this.$store.state.user.userInfo.companyId;this.getLaborIsEdit(i)}},methods:{from_list_get_time_ms:function(t){return t.map((function(t,e){return new Date(t["start"]).getTime()}))},is_time_coss:function(t){var e=t.length;return t.map((function(i,a){return!(a<e)||!(t[a+1]-i<=864e5)}))},getLaborIsEdit:function(t){var e=this;return(0,o.default)((0,s.default)().mark((function i(){var a;return(0,s.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,l.default.getLaborIsEdit({companyId:t});case 2:a=i.sent,e.hasEditPermission=a.data[0].lobarIsEdit;case 4:case"end":return i.stop()}}),i)})))()},getHistoryList:function(t){var e=this;return(0,o.default)((0,s.default)().mark((function i(){var a,n;return(0,s.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,l.default.getHistoryList({userId:t});case 2:a=i.sent,n=a.data.map((function(t){return t.timeSlot=t.entryTime+"至"+t.leaveTime,t})),e.tableData=n;case 5:case"end":return i.stop()}}),i)})))()},viewDetail:function(t,e){this.historyId=t._id,this.detailForm=(0,r.default)((0,r.default)({},t),{},{index:e}),this.range=this.detailForm.timeSlot.split("至"),this.$refs.popup.open()},closeDialog:function(){this.$refs.popup.close()},handleEdit:function(){this.hasEditPermission?this.currentIsEdit=!0:uni.showToast({title:"您暂无操作权限",icon:"error",duration:1500})},cancelEdit:function(){this.currentIsEdit=!1,this.$refs.popup.close()},addHistory:function(){this.hasEditPermission?(this.range="",this.startTime="",this.endTime="",this.workUnit="",this.workshop="",this.station="",this.workType="",this.$refs.addPopup.open()):uni.showToast({title:"您暂无操作权限",icon:"error",duration:1500})},cancelAdd:function(){var t=this;setTimeout((function(){t.newAddForm={}}),1e3),this.$refs.addPopup.close()},editSubmit:function(t){var e=this;return(0,o.default)((0,s.default)().mark((function i(){var a,o,d,u;return(0,s.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.editForm=(0,r.default)((0,r.default)({},t.detail.value),{},{entryTime:e.startTime,leaveTime:e.endTime}),e.startTime&&e.endTime&&t.detail.value.workUnit){i.next=4;break}return uni.showToast({title:"请完成必选项！",icon:"error",duration:1500}),i.abrupt("return");case 4:if(!(e.tableData.length>1)){i.next=14;break}if(a=(0,n.default)(e.tableData),a.splice(e.detailForm.index,1),o=[],a.forEach((function(t){var e=new Date(t.timeSlot.split("至")[0]),i=new Date(t.timeSlot.split("至")[1]);o.push({start:e,end:i})})),d={start:new Date(e.editForm.entryTime),end:new Date(e.editForm.leaveTime)},u=e.getHasOverlap(o,d),!u){i.next=14;break}return uni.showToast({title:"时间段冲突",icon:"error",duration:1500}),i.abrupt("return");case 14:return i.next=16,l.default.editHistoryList({_id:e.historyId,newData:e.editForm});case 16:e.currentIsEdit=!1,e.$refs.popup.close(),uni.showToast({title:"修改成功！",icon:"success"}),e.getHistoryList(e.$store.state.user.userInfo.employeeId);case 20:case"end":return i.stop()}}),i)})))()},getHasOverlap:function(t,e){for(var i=!1,a=0;a<t.length;a++){var n=t[a];if(e.start<=n.end&&e.start>=n.start){i=!0;break}if(e.end<=n.end&&e.end>=n.start){i=!0;break}}return i},deleteOne:function(){var t=this;return(0,o.default)((0,s.default)().mark((function e(){return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.hasEditPermission){e.next=3;break}return uni.showToast({title:"您暂无操作权限",icon:"error",duration:1500}),e.abrupt("return");case 3:return e.next=5,l.default.deleteHistoryList({_id:t.historyId});case 5:e.sent,t.$refs.popup.close(),uni.showToast({title:"删除成功！",icon:"success"}),t.getHistoryList(t.$store.state.user.userInfo.employeeId);case 9:case"end":return e.stop()}}),e)})))()},formSubmit:function(t){var e=this;return(0,o.default)((0,s.default)().mark((function i(){var a,o,d,u;return(0,s.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.newAddForm=(0,r.default)((0,r.default)({},t.detail.value),{},{entryTime:e.startTime,leaveTime:e.endTime,employeeId:e.$store.state.user.userInfo.employeeId}),e.startTime&&e.endTime&&t.detail.value.workUnit){i.next=4;break}return uni.showToast({title:"请完成必选项！",icon:"error",duration:1500}),i.abrupt("return");case 4:if(0===e.tableData.length){i.next=13;break}if(a=(0,n.default)(e.tableData),o=[],a.forEach((function(t){var e=new Date(t.timeSlot.split("至")[0]),i=new Date(t.timeSlot.split("至")[1]);o.push({start:e,end:i})})),d={start:new Date(e.newAddForm.entryTime),end:new Date(e.newAddForm.leaveTime)},u=e.getHasOverlap(o,d),!u){i.next=13;break}return uni.showToast({title:"时间段冲突",icon:"error",duration:1500}),i.abrupt("return");case 13:return i.next=15,l.default.addHistoryList(e.newAddForm);case 15:i.sent,e.$refs.addPopup.close(),uni.showToast({title:"添加成功！",icon:"success"}),e.newAddForm={},e.getHistoryList(e.$store.state.user.userInfo.employeeId);case 20:case"end":return i.stop()}}),i)})))()}}};e.default=u},"577b":function(t,e,i){var a=i("6034");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("1aef4b6b",a,!0,{sourceMap:!1,shadowMode:!1})},6034:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'.addHistory[data-v-184da3f4]{position:fixed;left:%?10?%;top:80%;width:%?80?%;height:%?80?%;font-size:%?60?%;text-align:center;line-height:%?80?%;color:#fff;border-radius:50%;background-color:rgba(0,122,255,.8);box-shadow:%?-15?% %?10?% %?15?% #aaa;z-index:2}@media screen and (min-width:960px){.addHistory[data-v-184da3f4]{left:38%}}.tableHeader[data-v-184da3f4]{font-size:11px;padding:0;background-color:#eee;font-weight:800;height:%?50?%}.tableBut[data-v-184da3f4]{font-size:%?30?%;color:#4a8df0}.uni-table-td[data-v-184da3f4]{padding:6px 8px}.form-box[data-v-184da3f4]{background-color:#f0f8ff;height:70vh;width:80vw;padding:%?10?% %?20?%;border-radius:%?8?%}uni-input[data-v-184da3f4]{box-sizing:border-box;height:%?70?%;border:%?0.5?% solid #ccc;padding:0 0 0 %?20?%;border-radius:%?8?%}.placeholder-style[data-v-184da3f4]{font-size:12px}uni-input[data-v-184da3f4]:hover{border:1px solid #2979ff;transition:all .5s}.uni-form-item[data-v-184da3f4]{margin-top:%?15?%}.example-body[data-v-184da3f4]{margin-top:%?5?%}.detailMsg[data-v-184da3f4]{width:100%;height:%?80?%;line-height:%?80?%;margin-top:%?10?%;padding-left:%?10?%;font-size:%?36?%;font-weight:500;border-left:%?15?% solid rgba(0,122,255,.8);border-radius:%?10?%;overflow:hidden;word-break:break-all;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:2;white-space:nowrap}.requireStar[data-v-184da3f4]::before{content:"*";color:red;font-size:%?40?%}.grace-td.td[data-v-184da3f4]{display:flex;align-items:center;justify-content:center;line-height:%?50?%;font-size:%?32?%;border-right:none;border-left:none;color:#3e3e3e;padding:%?30?% %?10?%}',""]),t.exports=e},"7d61":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("e40e")),r={stationInfo2:function(t){return(0,n.default)({url:"manage/adminuser/stationInfo2",method:"post",data:t})},getCheckResult2:function(t){return(0,n.default)({url:"manage/adminuser/getCheckResult2",method:"post",data:t})},stationInfo:function(t){return(0,n.default)({url:"manage/adminuser/stationInfo",method:"post",data:t})},getCheckResult:function(t){return(0,n.default)({url:"manage/adminuser/getCheckResult",method:"post",data:t})},getDefendproducts:function(t){return(0,n.default)({url:"manage/adminuser/getDefendproducts",method:"post",data:t})},receiveProducts:function(t){return(0,n.default)({url:"manage/adminuser/receiveProducts",method:"post",data:t})},getLaborIsEdit:function(t){return(0,n.default)({url:"manage/adminuser/getLaborIsEdit",method:"get",data:t})},getHistoryList:function(t){return(0,n.default)({url:"manage/adminuser/getHistoryList",method:"get",data:t})},addHistoryList:function(t){return(0,n.default)({url:"manage/adminuser/addHistoryList",method:"post",data:t})},deleteHistoryList:function(t){return(0,n.default)({url:"manage/adminuser/deleteHistoryList",method:"post",data:t})},editHistoryList:function(t){return(0,n.default)({url:"manage/adminuser/editHistoryList",method:"post",data:t})},ppeCabinetQRcode:function(t){return(0,n.default)({url:"mqtt/issueOrder",method:"post",data:t})},getPPEVideo:function(t){return(0,n.default)({url:"app/ppeVideo/getPPEVideo",method:"get",data:t})},getStationChange:function(t){return(0,n.default)({url:"manage/adminuser/getStationChange",method:"get",data:t})}},s=r;e.default=s},a48c:function(t,e,i){"use strict";var a=i("577b"),n=i.n(a);n.a},c172:function(t,e,i){"use strict";i.r(e);var a=i("1751"),n=i("c563");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("a48c");var s=i("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"184da3f4",null,!1,a["a"],void 0);e["default"]=o.exports},c563:function(t,e,i){"use strict";i.r(e);var a=i("31da"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a}}]);