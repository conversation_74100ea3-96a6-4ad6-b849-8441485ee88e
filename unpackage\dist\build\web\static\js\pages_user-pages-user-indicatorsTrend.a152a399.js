(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-indicatorsTrend"],{"0b75":function(t,e,i){"use strict";i.r(e);var a=i("299d"),n=i("2440");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("70b3");var s=i("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"13f1a55b",null,!1,a["a"],void 0);e["default"]=o.exports},1365:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'.chartContainer[data-v-13f1a55b]{width:100%;height:100%}.infoCard[data-v-13f1a55b]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.infoCard .cardTitle[data-v-13f1a55b]{margin-bottom:16px}.infoCard .cardTitle .titleText[data-v-13f1a55b]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555;display:flex;justify-content:space-between}.infoCard .cardTitle .titlePoint[data-v-13f1a55b]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.infoCard .cardItem[data-v-13f1a55b]{display:flex;margin-bottom:12px;justify-content:space-between;font-size:%?24?%;font-weight:400;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardItem[data-v-13f1a55b]:last-child{margin-bottom:0}.container[data-v-13f1a55b]{display:flex;flex-direction:column;align-items:center;height:55vh;width:100%}uni-picker .uni-input[data-v-13f1a55b]{color:#a8abb2}uni-picker .uni-input[data-v-13f1a55b]::after{content:">";display:inline-block;-webkit-transform:rotate(90deg) scaleY(1.5) translateY(-.25em);transform:rotate(90deg) scaleY(1.5) translateY(-.25em);margin-right:1em}',""]),t.exports=e},"1b87":function(t,e,i){"use strict";(function(t){i("6a54");var a=i("f5bd").default,n=i("3639").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("19d2")),s=a(i("8300")),o={dataset:{source:[]},tooltip:{trigger:"axis"},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#999"}},boundaryGap:!1}],yAxis:[{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DCDCDC"}},axisLine:{show:!1,lineStyle:{color:"#A4A4A4"}},axisTick:{show:!1},nameTextStyle:{color:"#A4A4A4"},splitArea:{show:!1}}],series:[{name:"指标",type:"line",encode:{x:"year",y:"result"},lineStyle:{normal:{width:8,color:{type:"linear",colorStops:[{offset:0,color:"#A9F387"},{offset:1,color:"#48D8BF"}],globalCoord:!1},shadowColor:"rgba(72,216,191, 0.3)",shadowBlur:10,shadowOffsetY:20}},itemStyle:{normal:{color:"#fff",borderColor:"#A9F387",borderWidth:10}},label:{normal:{show:!0,position:"top",color:"#A9F387"}},smooth:!0}]},l={data:function(){return{userInfo:{gender:"",idNo:"",marriage:"",name:"",phoneNum:""},ec:{option:{}},pickerArray:[{value:"10046",label:"血红蛋白"},{value:"10054",label:"血小板计数"},{value:"10924",label:"体重"},{value:"11158",label:"BMI"}],pickerLabel:"",pickerIndex:0,chartData:[],chartInstance:null}},mounted:function(){t.log("echarts",r),this.initChartH5(),this.getData(),this.pickerLabel=this.pickerArray[this.pickerIndex].label},components:{},methods:{getData:function(){var t=this;s.default.getIndicatorsTrend({itmcod:this.pickerArray[this.pickerIndex].value}).then((function(e){t.userInfo=e.data.userInfo,o.dataset.source=e.data.list,t.chartInstance.setOption(o)}))},bindPickerChange:function(t){this.pickerIndex=t.detail.value,this.pickerLabel=this.pickerArray[this.pickerIndex].label,o.series[0].name=this.pickerLabel,this.getData()},initChart:function(t,e,i,a){var n=r.init(t,null,{width:e,height:i,devicePixelRatio:a});return t.setChart(n),n.setOption(o),n},initChartH5:function(){var t=document.getElementById("chartContainer");this.chartInstance=r.init(t),this.chartInstance.setOption(o)}}};e.default=l}).call(this,i("ba7c")["default"])},2440:function(t,e,i){"use strict";i.r(e);var a=i("1b87"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"299d":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={gracePage:i("93fe").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[i("my-header",{attrs:{slot:"gHeader",title:"体检趋势"},slot:"gHeader"}),i("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},[i("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[i("v-uni-view",{staticClass:"cardTitle"},[i("v-uni-view",{staticClass:"titlePoint"}),i("v-uni-view",{staticClass:"titleText"},[i("v-uni-text",[t._v("基本信息")])],1)],1),i("v-uni-view",{staticClass:"cardItem"},[i("v-uni-view",[i("v-uni-text",[t._v("姓名："+t._s(t.userInfo.name))])],1),i("v-uni-view",[i("v-uni-text",[t._v("年龄："+t._s(t.userInfo.age))])],1),i("v-uni-view",[i("v-uni-text",[t._v("婚姻状况："+t._s(t.userInfo.marriage))])],1)],1),i("v-uni-view",{staticClass:"cardItem"},[i("v-uni-view",[i("v-uni-text",[t._v("手机："+t._s(t.userInfo.phoneNum))])],1)],1),i("v-uni-view",{staticClass:"cardItem"},[i("v-uni-view",[i("v-uni-text",[t._v("证件号："+t._s(t.userInfo.idNo))])],1)],1)],1),i("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[i("v-uni-view",{staticClass:"cardTitle"},[i("v-uni-view",{staticClass:"titlePoint"}),i("v-uni-view",{staticClass:"titleText"},[i("v-uni-view",[t._v("指标变化趋势图")]),i("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("v-uni-view",{staticStyle:{"margin-right":"1em"}},[t._v("检查指标")]),i("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:t.pickerIndex,range:t.pickerArray},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.pickerLabel))])],1)],1)],1)],1),i("v-uni-view",{staticClass:"cardItem"},[i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticStyle:{width:"100%",height:"100%"}},[i("v-uni-view",{ref:"chartContainer",staticClass:"chartContainer",attrs:{id:"chartContainer"}})],1)],1)],1)],1)],1)],1)},r=[]},"70b3":function(t,e,i){"use strict";var a=i("ad57"),n=i.n(a);n.a},ad57:function(t,e,i){var a=i("1365");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("0117c680",a,!0,{sourceMap:!1,shadowMode:!1})}}]);