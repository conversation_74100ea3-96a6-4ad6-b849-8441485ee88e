<template>
	<view class="institution">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="@/static/leftArrow.svg" mode=""></image>
					补充资料
				</view>
			</block>
		</uni-nav-bar>
		<view class="form" id="section-6">
			<u--form labelWidth="auto" labelPosition="top" v-show="type=='zd'">
				<view class="title">待补充的材料</view>
				<u-form-item label="身份证正反面">
					<UploadFile :fileList="fileList1" name="1" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/diagnosis/uploadIdCard'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="劳动相关证明">
					<UploadFile :fileList="fileList2" name="2" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/diagnosis/uploadEmploymentRelationProof'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="劳动者职业史和职业病危害接触史">
					<UploadFile :fileList="fileList3" name="3" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/diagnosis/uploadOccupationalHistory'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="劳动者职业健康检查结果">
					<UploadFile :fileList="fileList4" name="4" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/diagnosis/uploadExaminationResult'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="工作场所职业病危害因素检测结果">
					<UploadFile :fileList="fileList5" name="5" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/diagnosis/uploadDetectionResult'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="个人计量监测档案">
					<UploadFile :fileList="fileList6" name="6" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/diagnosis/uploadPersonalDoseRecord'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
			</u--form>
			<u--form labelWidth="auto" labelPosition="top" v-show="type=='jd'">
				<view class="title">请在待补充的材料后打 "✓"</view>
				<view class="application_table">
					<view class="form-content">
						<view class="form-item">
							<view class="item-label">（一）职业病诊断证明书;</view>
							<view class="checkbox-container">
								<u-checkbox v-model="formData.applicationFileRecord.hasDiagnosisCertificate"
									:checked="formData.applicationFileRecord.hasDiagnosisCertificate"
									@change="formData.applicationFileRecord.hasDiagnosisCertificate = !formData.applicationFileRecord.hasDiagnosisCertificate,certificateList=[]" />
							</view>
						</view>
						<view class="form-item">
							<view class="item-label">（二）首次职业病诊断鉴定书;</view>
							<view class="checkbox-container">
								<u-checkbox v-model="formData.applicationFileRecord.hasFirstIdentificationReport"
									:checked="formData.applicationFileRecord.hasFirstIdentificationReport"
									@change="formData.applicationFileRecord.hasFirstIdentificationReport = !formData.applicationFileRecord.hasFirstIdentificationReport,identificationReportList=[]" />
							</view>
						</view>
						<view class="form-item">
							<view class="item-label">（三）其他有关资料</view>
						</view>
						<view class="sub-items">
							<view class="form-item">
								<view class="item-label">1.
									劳动者职业史和职业病危害接触史（包括在岗时间、工种、岗位、接触的职业病危害因素名称等）;</view>
								<view class="checkbox-container">
									<u-checkbox :v-model="formData.applicationFileRecord.hasOtherJobHistory"
										:checked="formData.applicationFileRecord.hasOtherJobHistory"
										@change="formData.applicationFileRecord.hasOtherJobHistory = !formData.applicationFileRecord.hasOtherJobHistory,otherJobHistoryList=[]" />
								</view>
							</view>
							<view class="form-item">
								<view class="item-label">2. 劳动者职业健康检查结果;</view>
								<view class="checkbox-container">
									<u-checkbox v-model="formData.applicationFileRecord.hasOtherExaminationReport"
										:checked="formData.applicationFileRecord.hasOtherExaminationReport"
										@change="formData.applicationFileRecord.hasOtherExaminationReport = !formData.applicationFileRecord.hasOtherExaminationReport,examinationReportList=[]" />
								</view>
							</view>
							<view class="form-item">
								<view class="item-label">3. 工作场所职业病危害因素检测结果;</view>
								<view class="checkbox-container">
									<u-checkbox v-model="formData.applicationFileRecord.hasOtherDetectionReport"
										:checked="formData.applicationFileRecord.hasOtherDetectionReport"
										@change="formData.applicationFileRecord.hasOtherDetectionReport = !formData.applicationFileRecord.hasOtherDetectionReport,detectionReportList=[]" />
								</view>
							</view>
							<view class="form-item">
								<view class="item-label">4. 个人剂量监测档案（限于接触职业性放射性危害的劳动者）;</view>
								<view class="checkbox-container">
									<u-checkbox v-model="formData.applicationFileRecord.hasOtherPersonalDoseRecord"
										:checked="formData.applicationFileRecord.hasOtherPersonalDoseRecord"
										@change="formData.applicationFileRecord.hasOtherPersonalDoseRecord = !formData.applicationFileRecord.hasOtherPersonalDoseRecord ,doseRecordtList=[]" />
								</view>
							</view>
							<view class="form-item">
								<view class="item-label">5. 劳动者身份证复印件;</view>
								<view class="checkbox-container">
									<u-checkbox v-model="formData.applicationFileRecord.hasOtherPersonalIdCard"
										:checked="formData.applicationFileRecord.hasOtherPersonalIdCard"
										@change="formData.applicationFileRecord.hasOtherPersonalIdCard = !formData.applicationFileRecord.hasOtherPersonalIdCard ,personalIdCardList=[]" />
								</view>
							</view>
							<view class="form-item">
								<view class="item-label">6. 授权委托书及代理人身份证复印件;</view>
								<view class="checkbox-container">
									<u-checkbox v-model="formData.applicationFileRecord.hasOtherDepute"
										:checked="formData.applicationFileRecord.hasOtherDepute"
										@change="formData.applicationFileRecord.hasOtherDepute = !formData.applicationFileRecord.hasOtherDepute ,deputeList=[]" />
								</view>
							</view>
							<view class="form-item">
								<view class="item-label">7. 与鉴定有关的其他资料.</view>
								<view class="checkbox-container">
									<u-checkbox v-model="formData.applicationFileRecord.hasOther"
										:checked="formData.applicationFileRecord.hasOther"
										@change="formData.applicationFileRecord.hasOther = !formData.applicationFileRecord.hasOther ,otherList=[]" />
								</view>
							</view>
						</view>
					</view>
					<view class="form-note">
						上述第（三）项资料，当事人如以职业病诊断或首次职业病鉴定时提供的资料为准，可以不再提供请在备注中说明。
					</view>
					<view class="form-remark">
						<view class="remark-label">备注:</view>
						<u--input v-model="formData.applicationFileRecord.remark" type="textarea" :rows="2"
							placeholder="请在此处填写备注信息" />
					</view>
				</view>
				<u-form-item label="上传职业病诊断证明书" v-show="formData.applicationFileRecord.hasDiagnosisCertificate">
					<UploadFile :fileList="certificateList" name="1" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadDiagnosisCertificate'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传首次职业病诊断鉴定书" v-show="formData.applicationFileRecord.hasFirstIdentificationReport">
					<UploadFile :fileList="identificationReportList" name="2" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadFirstIdentificationReport'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传劳动者职业史和职业病危害接触史" v-show="formData.applicationFileRecord.hasOtherJobHistory">
					<UploadFile :fileList="otherJobHistoryList" name="3" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadJobHistory'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传劳动者职业健康检查结果" v-show="formData.applicationFileRecord.hasOtherExaminationReport">
					<UploadFile :fileList="examinationReportList" name="4" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadExaminationReport'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传工作场所职业病危害因素检测结果" v-show="formData.applicationFileRecord.hasOtherDetectionReport">
					<UploadFile :fileList="detectionReportList" name="5" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadDetectionReport'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传个人剂量监测档案" v-show="formData.applicationFileRecord.hasOtherPersonalDoseRecord">
					<UploadFile :fileList="doseRecordtList" name="6" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadPersonalDoseRecord'"
						:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传劳动者身份证复印件" v-show="formData.applicationFileRecord.hasOtherPersonalIdCard">
					<UploadFile :fileList="personalIdCardList" name="7" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadIdCard'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传授权委托书及代理人身份证复印件" v-show="formData.applicationFileRecord.hasOtherDepute">
					<UploadFile :fileList="deputeList" name="8" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadDepute'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
				<u-form-item label="上传与鉴定有关的其他资料" v-show="formData.applicationFileRecord.hasOther">
					<UploadFile :fileList="otherList" name="9" :maxCount="2"
						:uploadUrl="config.apiServer + 'app/identify/uploadOtherFile'" :diagnosisId="diagnosisId"
						@afterRead="afterReadIdCard" @delete="deleteFile"></UploadFile>
				</u-form-item>
			</u--form>
		</view>
		<view class="u-tabbar">
			<text class="btn close" @click="handleCancel">取消</text>
			<text class="btn next" @click="handleSave">提交</text>
		</view>
	</view>
</template>

<script>
	import diagnosisApi from '@/api/diagnosis'
	import identifyApi from '@/api/identify'
	import UploadFile from '../../components/uploadFile.vue';
	import config from '@/common.js';
	export default {
		components: {
			UploadFile,
		},
		data() {
			return {
				config: config,
				fileList1: [],
				fileList2: [],
				fileList3: [],
				fileList4: [],
				fileList5: [],
				fileList6: [],
				fileNameListZd: [],
				fileNameListJd: [],
				diagnosisId: '',
				type: '',
				identifyId: '',
				certificateList: [],
				identificationReportList: [],
				otherJobHistoryList: [],
				examinationReportList: [],
				detectionReportList: [],
				doseRecordtList: [],
				personalIdCardList: [],
				deputeList: [],
				otherList: [],
				formData: {
					diagnosisId: '', //首次鉴定
					firstIdentificationId: '', //再次鉴定
					applicant: 1,
					workerName: '',
					workerGender: '',
					workerBirthday: '',
					workerContactPhone: '',
					workerAddress: '',
					workerIdCardType: '',
					workerIdCardCode: '',
					workerZipCode: '',
					workerMailAddress: '',
					workerRegisteredResidenceAreaCode: '',
					workerRegisteredResidenceAddress: '',
					workerUsualAreaCode: '',
					workerUsualAddress: '',
					workerPastMedicalHistory: '',
					applicationDate: new Date().toISOString().split('T')[0],
					applicationReason: '',
					type: '',
					jobHistoryList: [],
					workerAgent: {
						agentName: "",
						agentIdCardCode: "",
						agentContactPhone: "",
						relationship: ""
					},
					empName: '',
					empCreditCode: '',
					empAreaCode: '',
					empAddress: '',
					empContactPerson: '',
					empContactPhone: '',
					empZipCode: '',
					empIndustryCode: '',
					empEconomicTypeCode: '',
					empEnterpriseScaleCode: '',
					empEstablishmentDate: '',
					empTotalStaffNum: '',
					empProductionWorkerNum: '',
					empExternalStaffNum: '',
					empExposureHazardStaffNum: '',
					workEmpName: '',
					workEmpCreditCode: '',
					workEmpAreaCode: '',
					workEmpAddress: '',
					workEmpContactPerson: '',
					workEmpContactPhone: '',
					workEmpZipCode: '',
					workEmpIndustryCode: '',
					workEmpEconomicTypeCode: '',
					workEmpEnterpriseScaleCode: '',
					workEmpEstablishmentDate: '',
					workEmpTotalStaffNum: '',
					workEmpProductionWorkerNum: '',
					workEmpExternalStaffNum: '',
					workEmpExposureHazardStaffNum: '',
					hasAgent: false,
					fileList: [],
					applicationFileRecord: {
						hasDiagnosisCertificate: false,
						hasFirstIdentificationReport: false,
						hasOtherJobHistory: false,
						hasOtherExaminationReport: false,
						hasOtherDetectionReport: false,
						hasOtherPersonalDoseRecord: false,
						hasOtherPersonalIdCard: false,
						hasOtherDepute: false,
						hasOther: false,
						remark: ""
					},
				},
			}
		},
		onLoad(options) {
			this.type = options.type
			if (options.type == 'zd') {
				this.diagnosisId = options.id
				this.getZdList()
			} else {
				this.identifyId = options.id
				this.getJdList()
			}
		},
		methods: {
			async getZdList() {
				const res = await diagnosisApi.getProvideFile({
					id: this.diagnosisId
				})
				this.fileNameListZd = res.data.data
				this.fileNameListZd.forEach(item => {
					if (item.fileClassify == 'DIAGNOSIS_ID_CARD') {
						this.fileList1 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'EMPLOYMENT_RELATION_PROOF') {
						this.fileList2 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'OCCUPATIONAL_HISTORY') {
						this.fileList3 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'EXAMINATION_RESULT') {
						this.fileList4 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'DETECTION_RESULT') {
						this.fileList5 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'PERSONAL_DOSE_RECORD') {
						this.fileList6 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
				})
			},
			// 获取鉴定详情
			async getDetail(id) {
				const res = await identifyApi.getIdentificationDetail(id)

				// 先设置基本数据
				this.formData = {
					...res.data
				}

				// 确保 applicationFileRecord 存在并且是响应式的
				if (res.data && res.data.applicationFileRecord) {
					// 使用 Vue.set 来确保响应性
					this.$set(this.formData, 'applicationFileRecord', {
						hasDiagnosisCertificate: Boolean(res.data.applicationFileRecord
							.hasDiagnosisCertificate),
						hasFirstIdentificationReport: Boolean(res.data.applicationFileRecord
							.hasFirstIdentificationReport),
						hasOtherJobHistory: Boolean(res.data.applicationFileRecord.hasOtherJobHistory),
						hasOtherExaminationReport: Boolean(res.data.applicationFileRecord
							.hasOtherExaminationReport),
						hasOtherDetectionReport: Boolean(res.data.applicationFileRecord
							.hasOtherDetectionReport),
						hasOtherPersonalDoseRecord: Boolean(res.data.applicationFileRecord
							.hasOtherPersonalDoseRecord),
						hasOtherPersonalIdCard: Boolean(res.data.applicationFileRecord.hasOtherPersonalIdCard),
						hasOtherDepute: Boolean(res.data.applicationFileRecord.hasOtherDepute),
						hasOther: Boolean(res.data.applicationFileRecord.hasOther),
						remark: res.data.applicationFileRecord.remark || ''
					})
				} else {
					// 如果没有数据，设置默认值
					this.$set(this.formData, 'applicationFileRecord', {
						hasDiagnosisCertificate: false,
						hasFirstIdentificationReport: false,
						hasOtherJobHistory: false,
						hasOtherExaminationReport: false,
						hasOtherDetectionReport: false,
						hasOtherPersonalDoseRecord: false,
						hasOtherPersonalIdCard: false,
						hasOtherDepute: false,
						hasOther: false,
						remark: ''
					})
				}

				// 设置文件列表
				this.certificateList = res.data.fileList?.diagnosisCertificate || []
				this.identificationReportList = res.data.fileList?.firstIdentificationReport || []
				this.otherJobHistoryList = res.data.fileList?.jobHistory || []
				this.examinationReportList = res.data.fileList?.examinationReport || []
				this.detectionReportList = res.data.fileList?.detectionReport || []
				this.doseRecordtList = res.data.fileList?.personalDoseRecord || []
				this.personalIdCardList = res.data.fileList?.idCard || []
				this.deputeList = res.data.fileList?.depute || []
				this.otherList = res.data.fileList?.other || []

				// 设置代理人信息
				if (!this.formData.workerAgent) {
					this.$set(this.formData, 'workerAgent', {
						agentName: "",
						agentIdCardCode: "",
						agentContactPhone: "",
						relationship: ""
					})
				}
			},
			async getJdList() {
				this.getDetail({
					userId: this.identifyId
				})
			},
			// 删除图片
			deleteFile(event) {
				console.log('event', event);
				this[`fileList${event.name}`].splice(event.index, 1);
			},
			back() {
				uni.navigateBack()
			},
			handleCancel() {
				uni.showModal({
					title: '提示',
					content: '数据还未保存，您确定要取消并返回吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				})
			},
			handleSave() {
				if (this.type == 'jd' && this.formData.type == '1') {
					if (this.identificationReportList.length == 0) {
						uni.showToast({
							title: '首次职业病诊断鉴定书是必填项，请上传相关文件',
							icon: 'none'
						});
						return false;
					}
				}
				uni.showModal({
					title: '提示',
					content: '提交后数据不能修改，您确定要提交吗？',
					success: async (res) => {
						if (res.confirm) {
							if (this.type == 'zd') {
								try {
									const res = await diagnosisApi.submitDiagnosis({
										id: this.diagnosisId
									})
									if (res.data.success) {
										uni.showToast({
											title: "提交成功",
											icon: 'success',
											duration: 1200
										})
										setTimeout(() => {
											uni.navigateBack();
										}, 1200)
									} else {
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
									}
								} catch (error) {
									console.log(error)
								}
							} else {
								this.formData.fileList = []
								this.formData.fileList = [
									...this.certificateList,
									...this.identificationReportList,
									...this.otherJobHistoryList,
									...this.examinationReportList,
									...this.detectionReportList,
									...this.doseRecordtList,
									...this.personalIdCardList,
									...this.deputeList,
									...this.otherList
								]
								let params = {
									...this.formData,
									workerIdCardType: this.idcardType?.find(item => item.dictLabel === this
										.formData
										.workerIdCardType)?.dictCode,
								};
								params.jobHistoryList?.forEach(item => {
									item.jobHistoryHazardList?.forEach(ele => {
										const hazard = this.hazard?.find(val => val
											.dictLabel === ele.hazardCode);
										if (hazard) ele.hazardCode = hazard.dictCode;
									});
								});
								const res = await identifyApi.updateIdentification(params);
								if (res.data.success) {
									// 提交表单
									const res2 = await identifyApi.submitIdentify({
										id: this.formData.id
									})
									if (res2.data.success) {
										uni.showToast({
											title: "提交成功",
											icon: 'success',
											duration: 1200
										})
										setTimeout(() => {
											uni.navigateBack()
										}, 1200)
									} else {
										uni.showToast({
											title: res2.data.msg || "提交失败",
											icon: 'none'
										})
									}
								}
							}
						}
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.institution {
		width: 100%;
		// height: 100vh;
		padding: 0 30rpx;
		box-sizing: border-box;
	}

	.nav-left {
		display: flex;
		align-items: center;
		width: auto;
		color: #fff;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.form {
		width: 100%;
		padding-bottom: 150rpx;

		.title {
			font-family: Source Han Sans;
			font-size: 16px;
			font-weight: bold;
			color: #3D3D3D;
		}

		.addBlList {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 30rpx;

			view {
				width: 102px;
				height: 32px;
				text-align: center;
				line-height: 32px;
				border-radius: 4px;
				background: #4163E1;
				color: #fff;
			}
		}
	}

	#section-6 {
		.title {
			margin-bottom: 32rpx;
		}

		.u-form {
			.u-form-item {
				padding: 0 24rpx;
				box-sizing: border-box;
				box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.0997), 0px 2px 4px 0px rgba(0, 0, 0, 0.0372);
				margin-bottom: 32rpx;
			}
		}
	}


	.u-tabbar {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 56px;
		background: #FFFFFF;
		box-shadow: 0px 1px 7px 1px rgba(0, 0, 0, 0.2046);
		display: flex;
		align-items: center;
		justify-content: flex-end;


	}

	.btn {
		width: 148rpx;
		height: 64rpx;
		line-height: 64rpx;
		text-align: center;
		border-radius: 4px;
		background: #F4F4F5;
		border: 1px solid #C7C9CC;
		margin-right: 40rpx;

		&.next {
			background: #4163E1;
			border: 1px solid #4163E1;
			color: #fff;
		}
	}

	.form-content {
		margin-bottom: 20px;
	}

	.form-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 8rpx 0;
		border-bottom: 2rpx dashed #ebeef5;
	}

	.form-item:hover {
		background-color: #f5f7fa;
	}

	.item-label {
		flex: 1;
		font-size: 28rpx;
		line-height: 1.5;
	}

	.checkbox-container {
		width: 30px;
		height: 30px;
	}


	.form-note {
		margin: 20px 0;
		padding: 10rpx;
		background-color: #f4f4f5;
		// border-left: 4px solid #409eff;
		color: #606266;
		font-size: 14px;
		line-height: 1.6;
	}

	.form-remark {
		margin: 20px 0;
	}

	.remark-label {
		margin-bottom: 10px;
		font-size: 14px;
	}
</style>