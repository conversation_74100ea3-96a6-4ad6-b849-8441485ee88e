// const accountInfo = uni.getAccountInfoSync();
import envConfig from './env.js';
let imgUrl;
let apiServer;
apiServer = envConfig[process.env.ENV_TYPE].baseUrl + '/';
imgUrl = envConfig[process.env.ENV_TYPE].baseUrl;
// console.log(process.env.ENV_TYPE, '获取当前处于哪个开发环境');
// console.log(apiServer, '获取处于当前开发环境的url');
// console.log(imgUrl, '获取处于当前开发环境的imageurl');
module.exports = {
  // 版本号
  version: '1.0.249',

  apiServer,

  wxH5: {
    appid: 'wx8d9eada10facd67a',
  },
  imgUrl,
  // 给图片的相对路径加上一个后端oapi的前缀
  imgPath(img) {
    if (img && typeof img == 'string') {
      if (img.startsWith('blob')) return img;
      return (img.startsWith('http') ? '' : imgUrl) + img.replace(/\\/g, '/');
    }
    return '';
  },
  // 随机返回一个默认图片 这里设置成oapi的图片
  randomDefaultImg(e) {
    let defaultImg = [
      '/static/images/bgBlue.png',
      '/static/images/bgOrange.png',
      '/static/images/bgGreen.png',
      '/static/images/bgPurple.png',
    ];
    let src = defaultImg[parseInt(Math.random() * (defaultImg.length - 1))];
    return src;
  },
};
