<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="职业健康检查报告查询" />

		<view class=" grace-body container" slot="gBody">
			<!-- 搜索区域 -->
			<view class="search-section">
				<view class="search-type">
					<button class="popup-btn" :class="searchType === '时间点' ? 'current' : ''"
						@click="onSearchTypeChange('时间点')">时间点</button>
					<button class="popup-btn" :class="searchType === '时间段' ? 'current' : ''"
						@click="onSearchTypeChange('时间段')">时间段</button>
				</view>

				<view class="date-picker" v-show="searchType === '时间点'">
					<uni-datetime-picker type="date" :value="singleDate" @change="onSingleDateChange" />
				</view>

				<view class="date-range" v-show="searchType === '时间段'">
					<uni-datetime-picker type="daterange" :value="[startDate, endDate]" @change="onDateRangeChange" />
				</view>
			</view>

			<!-- 顶部个人信息卡片 -->
			<view class="card grace-box-shadow infoCardBg" style="position: relative;padding: 5px 10px;">
				<image :src="infoCardImg" style="width: 100%;height:165px;"></image>
				<view style="position: absolute;top: 21px;">
					<view class="infoCard">
						<text class="nameStyle">{{ userInfo.name }}</text>
						<text class="sexStyle">
							{{ (userInfo.gender == '0' ? '男' : '女') + ' ' + userInfo.age }}岁</text>
					</view>
					<br />
					<br />
					<!-- <view class="personInfo infoCard" style="color:#fff">企业：{{ userInfo.company || '' }}</view> -->
					<view class="personInfo infoCard" style="color:#fff">部门：{{ userInfo.department || '' }}</view>
					<view class="personInfo infoCard" style="color:#fff">工种：{{ userInfo.workType || '' }}</view>
					<view class="personInfo infoCard" style="color:#fff">工龄：{{ userInfo.workYears || '' }}</view>
					<!-- <view class="personInfo infoCard grace-ellipsis nameColor">职业病危害因素：{{ harmFactors || '无' }}
					</view>
					<view class="personInfo infoCard grace-ellipsis nameColor">可能导致的职业病：{{ illnessInfo || '无' }}
					</view> -->
				</view>
			</view>

			<!-- 列表区域 -->
			<view class="health-check-item" v-for="(item, index) in healthCheckList" :key="index">
				<view class="item-header">
					<view class="org-name">{{ item.checkDate }}</view>
					<view class="date">{{ item.orgName }}</view>
				</view>

				<view class="item-content">
					<!-- <view class="org-name">
            <uni-icons custom-prefix="iconfont" type="hospital" size="16" color="#666"></uni-icons>
            <text>{{ item.orgName }}</text>
          </view> -->

					<view class="check-info">
						<view class="info-item">
							<text class="label">体检类型：</text>
							<text class="value value-right">{{ item.examType }}</text>
						</view>
						<view class="info-item">
							<text class="label">检查类型：</text>
							<text class="value value-right type-tag" :class="getTypeClass(item.checkType)">{{ item.checkType }}</text>
						</view>
						<view class="info-item">
							<text class="label">体检结论：</text>
							<span>
								<text class="value value-right" style="margin-right: 0.5em;" v-for="(item, i) in item.conclusion"
									:key="i" :class="getConclusionClass(item)">{{
										item }}</text>
							</span>
						</view>
					</view>
				</view>

				<view class="item-footer">
					<uni-button size="mini" type="primary" @click="viewDetail(item)">查看详情</uni-button>
				</view>
			</view>

			<!-- 空数据状态 -->
			<view v-if="isEmpty" class="empty-state">
				<image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无体检记录</text>
				<text class="empty-subtext">您还没有相关的体检记录</text>
			</view>

			<!-- 详情弹窗 -->
			<uni-popup ref="detailPopup" type="center">
				<view class="detail-popup">
					<view class="popup-header">
						<text class="title">体检详情</text>
						<uni-icons type="close" size="20" color="#666" @click="closeDetail"></uni-icons>
					</view>

					<scroll-view class="popup-content" scroll-y>
						<view class="detail-section">
							<view class="section-title">检查项目</view>
							<view v-for="(dept, deptIndex) in currentDetail.departments" :key="deptIndex" class="department-section">
								<view class="department-title">{{ dept.departmentName }}</view>
								<view v-for="(project, projectIndex) in dept.checkProjects" :key="projectIndex" class="project-item">
									<view class="project-title">{{ project.projectName }}</view>
									<view v-for="(item, itemIndex) in project.checkItems" :key="itemIndex" class="check-item">
										<view class="item-row">
											<text class="label">{{ item.itemId.projectName }}：</text>
											<view class="value-group">
												<text class="value">{{ item.result }}</text>
												<text class="unit">{{ item.itemId.msrunt }}</text>
											</view>
										</view>
										<view class="item-row">
											<text class="label">参考范围：</text>
											<text class="value value-right">
												{{ (item.standardValueMin || '') + '-' + (item.standardValueMax || '') }}</text>
										</view>
										<view class="item-row">
											<text class="label">结论：</text>
											<text class="value value-right">{{ item.conclusion || '-' }}</text>
										</view>
									</view>
								</view>
								<view class="department-summary" v-if="dept.summary">
									<text class="label">科室小结：</text>
									<text class="value value-right">{{ dept.summary }}</text>
								</view>
							</view>
						</view>

						<view class="detail-section">
							<view class="section-title">体检总结</view>
							<view class="summary-item">
								<text class="label">健康总结：</text>
								<text class="value value-right">{{ currentDetail.healthSummary || '暂无' }}</text>
							</view>
							<view class="summary-item">
								<text class="label">职业健康总结：</text>
								<text class="value value-right">{{ currentDetail.jobSummary || '暂无' }}</text>
							</view>
							<view class="summary-item">
								<text class="label">建议：</text>
								<text class="value value-right">{{ currentDetail.suggestion || '暂无' }}</text>
							</view>
							<view class="summary-item">
								<text class="label">职业健康结论：</text>
								<text class="value conclusion-tag value-right" v-for="(item, i) in currentDetail.jobConclusion" :key="i"
									style="margin-right: 0.5em;" :class="getConclusionClass(item)">{{ item }}</text>
							</view>
						</view>
					</scroll-view>
				</view>
			</uni-popup>
		</view>
	</gracePage>
</template>

<script>
import healthApi from '@/api/health.js'
import recordApi from '@/api/record.js'

const infoCardImg = require('@/static/编组@3x.png');
export default {
	data() {
		return {
			infoCardImg: infoCardImg,
			userInfo: {
				gender: "0", // 0: 男 1: 女
				name: "",
				age: "",
				department: "",
				workType: "",
				workYears: "",
			},
			searchType: '时间点',
			singleDate: '',
			startDate: '',
			endDate: '',
			currentDetail: {},
			healthCheckList: [],
			originalHealthCheckList: [],
			isEmpty: false
		};
	},
	async mounted() {
		try {
			// 获取基本信息
			await this.getBasicInfo();

			// 获取体检记录
			const res = await healthApi.reportList();
			if (!res.data || res.data.length === 0) {
				this.isEmpty = true;
				uni.showToast({
					title: '暂无体检记录',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			this.processExamList(res.data);
		} catch (error) {
			console.error('获取体检记录失败:', error);
			this.isEmpty = true;
			uni.showToast({
				title: '获取体检记录失败',
				icon: 'none',
				duration: 2000
			});
		}
	},
	methods: {
		// 获取基本信息
		async getBasicInfo() {
			try {
				const res = await healthApi.getEHealthRecordBaseInfo();
				if (res.data) {
					// 更新用户信息
					this.userInfo = {
						...this.userInfo,
						name: res.data.name || '',
						gender: res.data.gender || '0',
						age: res.data.age || '',
						department: res.data.department || '',
						workType: res.data.workType || '',
						workYears: res.data.workYears || '',
					};
				}
			} catch (error) {
				console.error('获取基本信息失败:', error);
			}
		},

		processExamList(reports) {
			// 按时间排序
			reports.sort((a, b) => new Date(b.registerTime) - new Date(a.registerTime));

			const processedList = reports.map(report => {
				const checkDate = new Date(report.registerTime).toLocaleDateString();

				// 处理体检结论
				// 1 目前未见异常
				// 2 复查
				// 3 疑似职业病
				// 4 禁忌证
				// 5 其他疾病或异常
				let conclusion = [];
				report.jobConclusion.map(item => {
					switch (item) {
						case 1:
							conclusion.push('目前未见异常');
							break;
						case 2:
							conclusion.push('复查');
							break;
						case 3:
							conclusion.push('疑似职业病');
							break;
						case 4:
							conclusion.push('禁忌证');
							break;
						case 5:
							conclusion.push('其他疾病或异常');
							break;
						default:
							break;
					}
				});
				return {
					checkDate,
					orgName: report.physicalOrgID.name,
					examType: this.getExamType(report.examType),
					checkType: this.getCheckType(report.checkType),
					conclusion,
					fullData: report
				};
			});

			this.healthCheckList = processedList;
			this.originalHealthCheckList = [...processedList];
			this.isEmpty = this.healthCheckList.length === 0;
		},

		getExamType(type) {
			const typeMap = {
				'1': '上岗前职业健康检查',
				'2': '在岗期间职业健康检查',
				'3': '离岗时职业健康检查',
				'4': '应急健康检查'
			};
			return typeMap[type] || '未知类型';
		},

		getCheckType(type) {
			const typeMap = {
				'1': '初检',
				'2': '复查'
			};
			return typeMap[type] || '未知类型';
		},

		onSearchTypeChange(val) {
			this.searchType = val;
			this.singleDate = '';
			this.startDate = '';
			this.endDate = '';
			this.resetFilter();
		},
		onSingleDateChange(date) {
			this.singleDate = date;
			if (!date) {
				this.resetFilter();
				return;
			}
			this.filterByDate(date);
		},
		onDateRangeChange(dates) {
			[this.startDate, this.endDate] = dates;
			if (!dates[0] || !dates[1]) {
				this.resetFilter();
				return;
			}
			this.filterByDateRange(dates[0], dates[1]);
		},
		resetFilter() {
			this.healthCheckList = [...this.originalHealthCheckList];
			this.isEmpty = this.healthCheckList.length === 0;
		},
		filterByDate(date) {
			if (!date) return;
			const targetDate = new Date(date);
			targetDate.setHours(23, 59, 59, 999);

			this.healthCheckList = this.originalHealthCheckList.filter(item => {
				const itemDate = new Date(item.checkDate);
				return itemDate <= targetDate;
			});

			this.isEmpty = this.healthCheckList.length === 0;
		},
		filterByDateRange(start, end) {
			if (!start || !end) return;
			const startDate = new Date(start);
			const endDate = new Date(end);
			endDate.setHours(23, 59, 59, 999);

			this.healthCheckList = this.originalHealthCheckList.filter(item => {
				const itemDate = new Date(item.checkDate);
				return itemDate >= startDate && itemDate <= endDate;
			});

			this.isEmpty = this.healthCheckList.length === 0;
		},
		getTypeClass(type) {
			const classMap = {
				'初检': 'type-primary',
				'复查': 'type-warning'
			};
			return classMap[type] || 'type-default';
		},
		getConclusionClass(conclusion) {
			const classMap = {
				'目前未见异常': 'conclusion-normal',
				'禁忌证': 'conclusion-danger',
				'疑似职业病': 'conclusion-danger',
				'复查': 'conclusion-warning',
				'其他疾病或异常': 'conclusion-alert'
			};
			return classMap[conclusion] || '';
		},
		viewDetail(item) {
			this.currentDetail = {
				name: item.fullData.name,
				gender: item.fullData.gender === '0' ? '男' : '女',
				age: this.calculateAge(item.fullData.birthDate),
				companyName: item.fullData.EnterpriseID,
				workType: item.fullData.jobConclusion?.join('、') || '暂无',
				departments: item.fullData.checkDepartments,
				healthSummary: item.fullData.healthSummary,
				jobSummary: item.fullData.jobSummary,
				suggestion: item.fullData.suggestion,
				jobConclusion: item.conclusion
			};
			this.$refs.detailPopup.open();
		},
		closeDetail() {
			this.$refs.detailPopup.close();
		},
		calculateAge(birthDate) {
			const birth = new Date(birthDate);
			const today = new Date();
			let age = today.getFullYear() - birth.getFullYear();
			const monthDiff = today.getMonth() - birth.getMonth();
			if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
				age--;
			}
			return age.toString();
		}
	}
};
</script>

<style>
page {
	height: 100%;
}

.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #f5f5f5;
}

.search-section {
	padding: 20rpx;
	background-color: #ffffff;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

	width: 107%;
	box-sizing: border-box;
	position: relative;
	left: -24rpx;
	margin-bottom: 30rpx;
}

.search-type {
	margin-bottom: 20rpx;
	display: flex;

	.current {
		background-color: #008AFF;
		color: #fff;
	}
}

.date-picker,
.date-range {
	padding: 10rpx 0;
}

.list-container {
	flex: 1;
	overflow: auto;
	padding: 20rpx;
}

.health-check-item {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.date {
	font-size: 14px;
	color: #666666;
}

.type-tag {
	padding: 4rpx 16rpx;
	border-radius: 4rpx;
	font-size: 12px;
}

.type-primary {
	background-color: #e6f4ff;
	color: #1890ff;
}

.type-warning {
	background-color: #fff7e6;
	color: #fa8c16;
}

.type-default {
	background-color: #f5f5f5;
	color: #666666;
}

.item-content {
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.org-name {
	display: flex;
	align-items: center;
	/* margin-bottom: 20rpx; */
	font-size: 16px;
	color: #333333;
}

.org-name text {
	margin-left: 10rpx;
}

.check-info {
	margin-top: 20rpx;
}

.info-item {
	margin-bottom: 16rpx;
	font-size: 14px;
	color: #666666;
	display: flex;
	justify-content: space-between;
}

.label {
	color: #999999;
	flex-shrink: 0;
}

.value {
	color: #333333;
	/* flex: 1; */
}

.item-row {
	display: flex;
	align-items: flex-start;
	margin-bottom: 8rpx;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;
	justify-content: space-between;
}

.value-right {
	text-align: right;
	justify-content: flex-end;
}

.conclusion-normal {
	color: #52c41a;
}

.conclusion-warning {
	color: #fa8c16;
}

.conclusion-danger {
	color: #f5222d;
}

.conclusion-alert {
	color: #722ed1;
}

.item-footer {
	margin-top: 20rpx;
	display: flex;
	justify-content: flex-end;
}

.detail-popup {
	width: 600rpx;
	background-color: #ffffff;
	border-radius: 12rpx;
}

.popup-header {
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px solid #f0f0f0;
}

.popup-header .title {
	font-size: 16px;
	font-weight: 500;
	color: #333333;
}

.popup-content {
	height: 800rpx;
	padding: 30rpx;
	box-sizing: border-box;
}

.detail-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 15px;
	font-weight: 500;
	color: #333333;
	margin-bottom: 20rpx;
	padding-left: 20rpx;
	border-left: 4px solid #1890ff;
}

.detail-item {
	margin-bottom: 16rpx;
	font-size: 14px;
	color: #666666;
}

.popup-btn {
	flex: 1;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;

	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}

	.empty-text {
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 16rpx;
	}

	.empty-subtext {
		font-size: 28rpx;
		color: #999999;
	}
}

.department-section {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;

	.department-title {
		font-size: 15px;
		font-weight: 500;
		color: #333333;
		margin-bottom: 16rpx;
		padding-left: 16rpx;
		border-left: 4px solid #1890ff;
	}

	.project-item {
		margin-bottom: 20rpx;
		padding: 16rpx;
		background-color: #ffffff;
		border-radius: 6rpx;
		width: 100%;
		box-sizing: border-box;
		overflow: hidden;

		.project-title {
			font-size: 14px;
			font-weight: 500;
			color: #333333;
			margin-bottom: 12rpx;
			word-break: break-all;
		}

		.check-item {
			margin-bottom: 12rpx;
			padding: 8rpx 0;
			width: 100%;
			box-sizing: border-box;
			overflow: hidden;

			.item-row {
				display: flex;
				align-items: flex-start;
				margin-bottom: 8rpx;
				width: 100%;
				box-sizing: border-box;
				overflow: hidden;

				.label {
					flex-shrink: 0;
					margin-right: 8rpx;
				}

				.value-group {
					display: flex;
					align-items: center;
					gap: 4rpx;
					flex: 1;
					min-width: 0;

					.value {
						color: #333333;
						word-break: break-all;
					}

					.unit {
						color: #999999;
						font-size: 24rpx;
						flex-shrink: 0;
					}
				}

				.value {
					flex: 1;
					color: #333333;
					word-break: break-all;

					display: flex;
					flex-direction: row-reverse
				}
			}
		}
	}

	.department-summary {
		margin-top: 16rpx;
		padding: 16rpx;
		background-color: #ffffff;
		border-radius: 6rpx;
		font-size: 14px;
		width: 100%;
		box-sizing: border-box;
		overflow: hidden;
		display: flex;
		justify-content: space-between;

		.label {
			display: block;
			margin-bottom: 8rpx;
		}

		.value {
			word-break: break-all;
		}
	}
}

.summary-item {
	margin-bottom: 20rpx;
	font-size: 14px;
	color: #666666;
	display: flex;
	justify-content: space-between;
}

.card {
	border-radius: 10px;
	padding: 25px 10px;
	margin-bottom: 20px;
}

.infoCardBg {
	background-size: 100% 100%;
	box-shadow: 0px 0px 0px #fff;
}

.infoCard {
	margin-left: 15px;

	.nameStyle {
		width: 51px;
		height: 20px;
		font-size: 20px;
		font-family: PingFang SC, PingFang SC-Semibold;
		font-weight: 600;
		text-align: left;
		color: #fff;
		line-height: 20px;
		margin-bottom: 15px;
	}

	.sexStyle {
		width: 39px;
		height: 14px;
		color: #fff;
		font-size: 14px;
		line-height: 14px;
		margin-left: 16px;
	}

	.personInfo {
		height: 14px;
		font-size: 14px;
		font-weight: 300;
		text-align: left;
		color: #000;
		line-height: 14px;
		margin-top: 15px;
	}

	.nameColor {
		color: #fff;
	}
}
</style>
