{
	"pages": [
		{
			"path": "pages/login/login"
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "职业健康达人",
				"titleNView": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/bindPhoneNum"
		},
		{
			"path": "pages/login/bindWxInfo"
		},
		{
			"path": "pages/index/signature"
		},
		{
			"path": "pages/login/zfbAutho",
			"style": {
				"navigationBarTitleText": "支付宝授权"
			}
		},
		{
			"path": "pages/index/search"
		},
		{
			"path": "pages/reorientation/reorientation",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/institution/index"
		},
		{
			"path": "pages/institution/tjRecord"
		},
		{
			"path": "pages/institution/tjResult"
		},
		{
			"path": "pages/institution/tjBooking"
		},
		{
			"path": "pages/institution/tjAppoint"
		},
		{
			"path": "pages/institution/tjMessage"
		},
		{
			"path": "pages/institution/tjAuth"
		},
		{
			"path": "pages/institution/institution"
		},
		{
			"path": "pages/institution/jgDetail"
		},
		{
			"path": "pages/institution/jgForm"
		},
		{
			"path": "pages/institution/zdResult"
		},
		{
			"path": "pages/institution/ysReport"
		},
		{
			"path": "pages/institution/addInformation"
		},
		{
			"path": "pages/lifeCycle/lifeCycle",
			"style": {
				"navigationBarTitleText": "生命周期管理"
			}
		},
		{
			"path": "pages/workInjuryRecognition/index"
		},
		{
			"path": "pages/workInjuryRecognition/add"
		},
		{
			"path": "pages/workInjuryRecognition/detail"
		}
		// {
		// 	"path" : "pages/institution/tjRecord/tjRecord",
		// 	"style" : 
		// 	{
		// 		"navigationBarTitleText" : ""
		// 	}
		// }
	],
	"subPackages": [
		{
			"root": "pages_train",
			"pages": [
				{
					"path": "pages/training/faceInput"
				},
				{
					"path": "pages/training/faceValid"
				},
				{
					"path": "pages/training/courses/courses"
				},
				{
					"path": "pages/training/courses/course"
				},
				{
					"path": "pages/training/courses/courseWithoutPersonal"
				},
				{
					"path": "pages/training/courses/document"
				},
				{
					"path": "pages/training/courses/search"
				},
				{
					"path": "pages/training/myTraining",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "pages/training/detail",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "pages/training/test"
				},
				{
					"path": "pages/training/myCourses",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "pages/training/publicCourses",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "pages/training/testAnswer"
				},
				{
					"path": "pages/training/testResult"
				},
				{
					"path": "pages/training/certificate"
				},
				{
					"path": "pages/training/publicCourseDetail"
				}
			]
		},
		{
			"root": "pages_learning",
			"pages": [
				{
					"path": "pages/learning/artInfo"
				},
				{
					"path": "pages/learning/industryNews"
				}
			]
		},
		{
			"root": "pages_remote",
			"pages": [{
					"path": "pages/remote/list"
				},
				{
					"path": "pages/remote/meeting"
				}
			]
		},
		{
			"root": "pages_user",
			"pages": [
				{
					"path": "pages/user/boundEnterprise"
				},
				{
					"path": "pages/user/h5login"
				},
				{
					"path": "pages/user/ppe"
				},
				{
					"path": "pages/user/info"
				},
				{
					"path": "pages/user/modify"
				},
				{
					"path": "pages/user/modifyPhone"
				},
				{
					"path": "pages/user/comment"
				},
				{
					"path": "pages/user/myTraining"
				},
				{
					"path": "pages/identify/index"
				},
				{
					"path": "pages/identify/apply"
				},
				{
					"path": "pages/identify/jgForm"
				},
				{
					"path": "pages/identify/jgDetail"
				},
				{
					"path": "pages/identify/jdResult"
				},
				{
					"path": "pages/user/complaints/list",
					"style": {
						"softinputMode": "adjustResize",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "pages/user/signImg",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"pageOrientation": "landscape"
					}
				},
				{
					"path": "pages/user/signature",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"pageOrientation": "landscape"
					}
				},
				{
					"path": "pages/user/checkDetail",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/user/ppeSign",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom",
						"pageOrientation": "landscape"
					}
				},
				{
					"path": "pages/user/complaints/detail",
					"style": {
						"softinputMode": "adjustResize"
					}
				},
				{
					"path": "pages/user/complaints/scoring"
				},
				{
					"path": "pages/user/physicalExamination"
				},
				{
					"path": "pages/user/occupationalHistory"
				},
				{
					"path": "pages/user/physicalAndCheckResult"
				},
				{
					"path": "pages/user/diseasesOverhaul"
				},
				{
					"path": "pages/user/diseasesOverhaulDetail"
				},
				{
					"path": "pages/user/message"
				},
				{
					"path": "pages/user/violationInfo"
				},
				{
					"path": "pages/user/questionnaire"
				},
				{
					"path": "pages/user/questionnaireDetail"
				},
				{
					"path": "pages/user/tjAppointmentInfo"
				},
				{
					"path": "pages/user/tjAppointmentDetailInfo"
				},
				{
					"path": "pages/user/employeeList"
				},
				{
					"path": "pages/user/addItems"
				},
				{
					"path": "pages/user/indicatorsTrend"
				},
				{
					"path": "pages/eHealthRecord/index"
				},
				{
					"path": "pages/eHealthRecord/auth"
				},
				{
					"path": "pages/eHealthRecord/complaint"
				},
				{
					"path": "pages/eHealthRecord/basicInfo"
				},
				{
					"path": "pages/eHealthRecord/exam"
				},
				{
					"path": "pages/eHealthRecord/diagnose"
				},
				{
					"path": "pages/eHealthRecord/injury"
				},
				{
					"path": "pages/eHealthRecord/healthManage"
				},
				{
					"path": "pages/eHealthRecord/servic"
				},
				{
					"path": "pages/eHealthRecord/report"
				},
				{
					"path": "pages/eHealthRecord/warning"
				},
				{
					"path": "pages/eHealthRecord/harmFactor"
				},
				{
					"path": "pages/workInjury/query"
				}
			]
		},
		{
			"root": "pages_lifeCycle",
			"pages": [
				{
					"path": "/pages/followUp/followUp",
					"style": {
						"navigationBarTitleText": "随访记录"
					}
				},
				{
					"path": "/pages/Appointment/Appointment",
					"style": {
						"navigationBarTitleText": "就诊预约"
					}
				},
				{
					"path": "/pages/Appointment/AppointmentRecord",
					"style": {
						"navigationBarTitleText": "预约记录"
					}
				},
				{
					"path": "/pages/MedicationServices/MedicationServices",
					"style": {
						"navigationBarTitleText": "用药服务"
					}
				},
				{
					"path": "/pages/MedicationServices/MedicationServicesInfo",
					"style": {
						"navigationBarTitleText": "用药详情"
					}
				},
				{
					"path": "/pages/treatmentService/treatmentService",
					"style": {
						"navigationBarTitleText": "诊疗服务"
					}
				},
				{
					"path": "/pages/treatmentService/treatmentServiceInfo",
					"style": {
						"navigationBarTitleText": "诊疗详情"
					}
				},
				{
					"path": "/pages/recoveredServices/recoveredServices",
					"style": {
						"navigationBarTitleText": "康复指导服务"
					}
				},
				{
					"path": "/pages/recoveredServices/addrecoveredServices",
					"style": {
						"navigationBarTitleText": "申请康复指导"
					}
				},
				{
					"path": "/pages/recoveredServices/recoveredServicesRecord",
					"style": {
						"navigationBarTitleText": "服务记录"
					}
				}
			]
		}
	],
	"globalStyle": {
		"mp-360": {
			"navigationStyle": "custom"
		},
		"mp-alipay": {
			"transparentTitle": "always",
			"allowsBounceVertical": "NO"
		},
		"navigationStyle": "custom",
		"navigationBarTextStyle": "black",
		"app-plus": {
			"scrollIndicator": "none",
			"bounce": "none"
		}
	},
	"usingComponts": true,
	"easycom": {
		"custom": {
			"autoscan": false,
			"grace(.*)": "@/graceUI/components/grace$1.vue"
		}
	},
	"condition": {
		"current": 0,
		"list": [
			{
				"name": "",
				"path": "",
				"query": ""
			}
		]
	}
}