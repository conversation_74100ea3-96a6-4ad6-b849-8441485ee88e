<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="在线申请" />
		<view slot="gBody" class="grace-body content">
			<view class="institution">
				<view class="section-body">
					<view class="form" id="section-1" v-if="currentView==1">
						<view class="title">职业病诊断申请表</view>
						<u--form :model="formData" labelWidth="auto" labelPosition="left">
							<u-form-item label="姓名">
								<u--input placeholder="请输入姓名" v-model="formData.workerName" clearable></u--input>
							</u-form-item>
							<u-form-item label="性别">
								<u-radio-group placement="row" v-model="formData.workerGender">
									<u-radio label="男" name="1" style="margin-left: 10px;"></u-radio>
									<u-radio label="女" name="2" style="margin-left: 10px;"></u-radio>
									<u-radio label="未知" name="3" style="margin-left: 10px;"></u-radio>
								</u-radio-group>
							</u-form-item>
							<u-form-item label="出生日期" prop="workerBirthday">
								<uni-datetime-picker type="date" v-model="formData.workerBirthday" />
							</u-form-item>
							<u-form-item label="联系电话">
								<u--input placeholder="请输入联系电话" v-model="formData.workerContactPhone"></u--input>
							</u-form-item>
							<u-form-item label="住址">
								<u--input placeholder="请输入住址" v-model="formData.workerAddress"></u--input>
							</u-form-item>
							<u-form-item label="证件类别" @click="IDCardShow=true">
								<!-- <u--input placeholder="请选择证件类别" v-model="formData.workerIdCardType"></u--input>
						<u-picker :show="IDCardShow" :columns="idcardType2" @cancel="IDCardShow=false"
							@confirm="handleSectionIDCard"></u-picker> -->
								<uni-data-select v-model="formData.workerIdCardType"
									:localdata="idcardType2"></uni-data-select>
							</u-form-item>
							<u-form-item label="证件号码">
								<u--input placeholder="请输入证件号码" v-model="formData.workerIdCardCode"></u--input>
							</u-form-item>
							<u-form-item label="邮政编码">
								<u--input placeholder="请输入邮政编码" v-model="formData.workerZipCode"></u--input>
							</u-form-item>
							<u-form-item label="通讯地址">
								<u--input placeholder="请输入通讯地址" v-model="formData.workerMailAddress"></u--input>
							</u-form-item>
							<u-form-item label="既往病史">
								<u--input placeholder="请输入既往病史" v-model="formData.workerPastMedicalHistory"></u--input>
							</u-form-item>
							<u-form-item label="户籍所在地">
								<uni-data-picker :localdata="areaList2"
									v-model="formData.workerRegisteredResidenceAreaCode" popup-title="选择户籍所在地"
									@change="onAddressChange" placeholder="请选择户籍所在地">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="户籍详细地址">
								<u--input placeholder="请输入详细地址"
									v-model="formData.workerRegisteredResidenceAddress"></u--input>
							</u-form-item>
							<u-form-item label="经常居住地">
								<uni-data-picker :localdata="areaList2" v-model="formData.workerUsualAreaCode"
									popup-title="选择经常居住地" @change="onUsualAddressChange" placeholder="请选择经常居住地">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="居住地详细地址">
								<u--input placeholder="请输入详细地址" v-model="formData.workerUsualAddress"></u--input>
							</u-form-item>
							<u-form-item label="申请日期">
								<uni-datetime-picker type="date" v-model="formData.applicationDate" disabled />
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-2" v-if="currentView==2">
						<view class="title">劳动者职业史和职业病危害接触史</view>
						<view class="addBlList" style="margin-bottom: 20rpx;">
							<view @click="handleAddBli">添加职业史</view>
						</view>
						<u-collapse accordion>

							<u-collapse-item v-for="(item,index) in formData.jobHistoryList" :key="item.id || index">
								<text slot="title" class="u-page__item__title__slot-title">工作单位{{index+1}}</text>
								<text slot="value" class="u-page__item__title__slot-title" style="color: #409EFF;">展开
								</text>
								<text slot="right-icon" class="u-page__item__title__slot-title">
									<u-icon name="arrow-right-double" color="#409EFF"></u-icon>
								</text>
								<u--form labelWidth="auto" labelPosition="left">
									<u-form-item label="工作单位">
										<u--input placeholder="请输入工作单位" v-model="item.empName"></u--input>
									</u-form-item>
									<u-form-item label="岗位">
										<u--input placeholder="请输入岗位" v-model="item.post"></u--input>
									</u-form-item>
									<u-form-item label="操作过程">
										<u--input placeholder="请输入操作过程" v-model="item.operationProcess"></u--input>
									</u-form-item>
									<u-form-item label="防护措施">
										<u--input placeholder="请输入防护措施" v-model="item.protectiveMeasure"></u--input>
									</u-form-item>
									<u-form-item label="个人防护">
										<u--input placeholder="请输入个人防护" v-model="item.personalProtection"></u--input>
									</u-form-item>
									<view class="addBlList" style="margin:20rpx 0">
										<view @click="handleAddItem(item)">添加接触史</view>
									</view>
									<u-collapse accordion style="height: 50vh;overflow-y: auto;">
										<u-collapse-item v-for="(ele,hzIdx) in item.jobHistoryHazardList">
											<text slot="title"
												class="u-page__item__title__slot-title">类别{{hzIdx+1}}</text>
											<text slot="value" class="u-page__item__title__slot-title"
												style="color: #409EFF;">展开</text>
											<text slot="right-icon" class="u-page__item__title__slot-title">
												<u-icon name="arrow-right-double" color="#409EFF"></u-icon>
											</text>
											<u--form labelWidth="auto">
												<u-form-item label="危害因素编码" @click="ygrShow=true">
													<!-- <u--input placeholder="请选择危害因素编码" v-model="ele.hazardCode"></u--input>
											<u-picker :show="ygrShow" :columns="hazard2" @cancel="ygrShow=false"
												@confirm="handleYgrShow(ele, $event)"></u-picker> -->
													<uni-data-select v-model="ele.hazardCode" :localdata="hazard2"
														@change="handleYgrShow(item,ele,$event,hzIdx)"></uni-data-select>
												</u-form-item>
												<u-form-item label="浓度">
													<u--input placeholder="请输入浓度"
														v-model="ele.concentration"></u--input>
												</u-form-item>
												<u-form-item label="接触时间">
													<u--input placeholder="请输入接触时间"
														v-model="ele.contactTime"></u--input>
												</u-form-item>
												<u-form-item label="接触开始时间">
													<uni-datetime-picker type="date" v-model="ele.startContactDate" />
												</u-form-item>
												<u-form-item label="接触结束时间">
													<uni-datetime-picker type="date" v-model="ele.endContactDate" />
												</u-form-item>
												<u-form-item>
													<u-button type="error" size="mini"
														@click="handleDeleteHazard(item, ele, hzIdx)">删除接触史</u-button>
												</u-form-item>
											</u--form>
										</u-collapse-item>
									</u-collapse>
									<u-form-item>
										<u-button v-show="formData.id" type="primary" size="mini"
											@click="handleSaveJob(item)">保存职业史/接触史</u-button>
										<u-button style="margin-left: 5px;" type="error" size="mini"
											@click="handleDeleteJob(item, index)">删除职业史</u-button>
									</u-form-item>
								</u--form>
							</u-collapse-item>
						</u-collapse>

					</view>
					<view class="form" id="section-3" v-if="currentView==3">
						<view class="title">用人单位信息</view>
						<u--form labelWidth="auto" labelPosition="left">
							<u-form-item label="用人单位名称">
								<u--input placeholder="请选择用人单位名称" v-model="formData.empName"></u--input>
							</u-form-item>
							<u-form-item label="统一社会信用代码">
								<u--input placeholder="请输入统一社会信用代码" v-model.trim="formData.empCreditCode"></u--input>
							</u-form-item>
							<u-form-item label="所在地区" @click="AreaCodeShow=true">
								<uni-data-picker :localdata="areaList2" v-model="formData.empAreaCode"
									popup-title="选择所在地区" @change="onEmpAreaChange" placeholder="请选择所在地区">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="地址">
								<u--input placeholder="请输入地址" v-model="formData.empAddress"></u--input>
							</u-form-item>
							<u-form-item label="联系人">
								<u--input placeholder="请输入联系人" v-model="formData.empContactPerson"></u--input>
							</u-form-item>
							<u-form-item label="联系方式">
								<u--input placeholder="请输入联系方式" v-model="formData.empContactPhone"></u--input>
							</u-form-item>
							<u-form-item label="邮编">
								<u--input placeholder="请输入邮编" v-model="formData.empZipCode"></u--input>
							</u-form-item>
							<u-form-item label="行业" @click="hyShow = true">
								<u--input placeholder="请选择行业" v-model="formData.empIndustryCode"></u--input>
								<u-picker :show="hyShow" :columns="[['炼铁','钢压延加工','硅冶炼','其他常用有色金属冶炼']]"
									@cancel="hyShow=false" @confirm="handleSectionIndustry"></u-picker>
							</u-form-item>
							<u-form-item label="经济类型" @click="jjShow = true">
								<u--input placeholder="请选择经济类型" v-model="formData.empEconomicTypeCode"></u--input>
								<u-picker :show="jjShow"
									:columns="[['内资','国有全资','集体全资','股份合作','有限责任(公司)','其他有限责任(公司)']]"
									@cancel="jjShow=false" @confirm="handleSectionEconomic"></u-picker>
							</u-form-item>
							<u-form-item label="企业规模" @click="qyShow = true">
								<u--input placeholder="请选择企业规模" v-model="formData.empEnterpriseScaleCode"></u--input>
								<u-picker :show="qyShow" :columns="[['大','中','小','微型']]" @cancel="qyShow=false"
									@confirm="handleSectionEnterprise"></u-picker>
							</u-form-item>
							<u-form-item label="单位成立时间">
								<uni-datetime-picker type="date" v-model="formData.empEstablishmentDate" />
							</u-form-item>
							<u-form-item label="职工总人数">
								<u--input placeholder="请输入职工总人数" v-model="formData.empTotalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="生产工人总数">
								<u--input placeholder="请输入生产工人总数" v-model="formData.empProductionWorkerNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="外委人员数">
								<u--input placeholder="请输入外委人员数" v-model="formData.empExternalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="接触有毒有害作业人数">
								<u--input placeholder="请输入接触有毒有害作业人数"
									v-model="formData.empExposureHazardStaffNum" type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-4" v-if="currentView==4">
						<view class="title">用工单位信息</view>
						<u--form labelWidth="auto" labelPosition="left">
							<u-form-item label="用工单位名称">
								<u--input placeholder="请输入用工单位名称" v-model="formData.workEmpName"></u--input>
							</u-form-item>
							<u-form-item label="统一社会信用代码">
								<u--input placeholder="请输入统一社会信用代码"
									v-model.trim="formData.workEmpCreditCode"></u--input>
							</u-form-item>
							<u-form-item label="所在地区" @click="AreaCodeShow=true">
								<uni-data-picker :localdata="areaList2" v-model="formData.workEmpAreaCode"
									popup-title="选择所在地区" @change="onWorkAreaCodeChange" placeholder="请选择所在地区">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="地址">
								<u--input placeholder="请输入地址" v-model="formData.workEmpAddress"></u--input>
							</u-form-item>
							<u-form-item label="联系人">
								<u--input placeholder="请输入联系人" v-model="formData.workEmpContactPerson"></u--input>
							</u-form-item>
							<u-form-item label="联系方式">
								<u--input placeholder="请输入联系方式" v-model="formData.workEmpContactPhone"></u--input>
							</u-form-item>
							<u-form-item label="邮编">
								<u--input placeholder="请输入邮编" v-model="formData.workEmpZipCode"></u--input>
							</u-form-item>
							<u-form-item label="行业" @click="hyShow2 = true">
								<u--input placeholder="请选择行业" v-model="formData.workEmpIndustryCode"></u--input>
								<u-picker :show="hyShow2" :columns="[['炼铁','钢压延加工','硅冶炼','其他常用有色金属冶炼']]"
									@cancel="hyShow2=false" @confirm="handleSectionIndustry2"></u-picker>
							</u-form-item>
							<u-form-item label="经济类型" @click="jjShow2 = true">
								<u--input placeholder="请选择经济类型" v-model="formData.workEmpEconomicTypeCode"></u--input>
								<u-picker :show="jjShow2"
									:columns="[['内资','国有全资','集体全资','股份合作','有限责任(公司)','其他有限责任(公司)']]"
									@cancel="jjShow2=false" @confirm="handleSectionEconomic2"></u-picker>
							</u-form-item>
							<u-form-item label="企业规模" @click="qyShow2 = true">
								<u--input placeholder="请选择企业规模"
									v-model="formData.workEmpEnterpriseScaleCode"></u--input>
								<u-picker :show="qyShow2" :columns="[['大','中','小','微型']]" @cancel="qyShow2=false"
									@confirm="handleSectionEnterprise2"></u-picker>
							</u-form-item>
							<u-form-item label="单位成立时间">
								<uni-datetime-picker type="date" v-model="formData.workEmpEstablishmentDate" />
							</u-form-item>
							<u-form-item label="职工总人数">
								<u--input placeholder="请输入职工总人数" v-model="formData.workEmpTotalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="生产工人总数">
								<u--input placeholder="请输入生产工人总数" v-model="formData.workEmpProductionWorkerNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="外委人员数">
								<u--input placeholder="请输入外委人员数" v-model="formData.workEmpExternalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="接触有毒有害作业人数">
								<u--input placeholder="请输入接触有毒有害作业人数"
									v-model="formData.workEmpExposureHazardStaffNum" type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-5" v-if="currentView==5">
						<view class="title">委托代理人信息</view>
						<u--form labelWidth="auto" labelPosition="left">
							<u-form-item label="是否有委托代理人">
								<u-radio-group v-model="formData.hasAgent" placement="row">
									<u-radio label="是" :name="true" />
									<u-radio label="否" :name="false" />
								</u-radio-group>
							</u-form-item>
							<u-form-item label="代理人姓名" v-show="formData.hasAgent">
								<u--input placeholder="请输入代理人姓名" v-model="formData.workerAgent.agentName"></u--input>
							</u-form-item>
							<u-form-item label="与当事人关系" v-show="formData.hasAgent">
								<u--input placeholder="请输入与当事人关系"
									v-model="formData.workerAgent.relationship"></u--input>
							</u-form-item>
							<u-form-item label="代理人身份证号码" v-show="formData.hasAgent">
								<u--input placeholder="请输入代理人身份证号码"
									v-model="formData.workerAgent.agentIdCardCode"></u--input>
							</u-form-item>
							<u-form-item label="代理人联系电话" v-show="formData.hasAgent">
								<u--input placeholder="请输入代理人联系电话"
									v-model="formData.workerAgent.agentContactPhone"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-5" v-if="currentView==6">
						<view class="title">职业病种类</view>
						<u-collapse accordion>
							<u-collapse-item v-for="(item,index) in formData.diseaseList">
								<text slot="title" class="u-page__item__title__slot-title">类别{{index+1}}</text>
								<text slot="value" class="u-page__item__title__slot-title"
									style="color: #409EFF;">展开</text>
								<text slot="right-icon" class="u-page__item__title__slot-title">
									<u-icon name="arrow-right-double" color="#409EFF"></u-icon>
								</text>
								<u--form labelWidth="auto">
									<u-form-item label="职业病种类" @click="zybzShow=true">
										<uni-data-picker :localdata="diseasesList2" v-model="item.diseaseCode"
											popup-title="选择职业病种类" @change="handleSectionzyb" placeholder="请选择职业病种类">
										</uni-data-picker>
									</u-form-item>
									<u-form-item label="其他职业病">
										<u--input placeholder="请输入其他职业病" v-model="item.otherDiseaseName"></u--input>
									</u-form-item>
								</u--form>
							</u-collapse-item>
						</u-collapse>
						<view class="addBlList">
							<view @click="handleAddDis">添加种类</view>
						</view>
					</view>
					<view class="form" id="section-6" v-if="currentView==7">
						<view class="title">需要上传的材料</view>
						<u--form labelWidth="auto" labelPosition="top">
							<u-form-item label="身份证正反面">
								<UploadFile :fileList="fileList1" name="1" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/diagnosis/uploadIdCard'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="劳动相关证明">
								<UploadFile :fileList="fileList2" name="2" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/diagnosis/uploadEmploymentRelationProof'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="劳动者职业史和职业病危害接触史">
								<UploadFile :fileList="fileList3" name="3" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/diagnosis/uploadOccupationalHistory'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="劳动者职业健康检查结果">
								<UploadFile :fileList="fileList4" name="4" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/diagnosis/uploadExaminationResult'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="工作场所职业病危害因素检测结果">
								<UploadFile :fileList="fileList5" name="5" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/diagnosis/uploadDetectionResult'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="个人计量监测档案">
								<UploadFile :fileList="fileList6" name="6" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/diagnosis/uploadPersonalDoseRecord'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
						</u--form>
					</view>
				</view>
				<view class="u-tabbar">
					<text class="btn close" @click="handleCancel">取消</text>
					<text class="btn sync" v-if="currentView==4" @click="handleSync">一键同步</text>
					<text class="btn prev" @click="handlePrev" v-if="currentView>1 && currentView<7 ">上一步</text>
					<text class="btn next" @click="handleNext" v-if="currentView!==7">下一步</text>
					<text class="btn next" v-if="currentView==7" @click="handleTemporyStore">暂存</text>
					<text class="btn next" v-if="currentView==7" @click="handleSave">提交申请</text>
				</view>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import {
		mapGetters,
		mapActions,
	} from 'vuex'
	import diagnosisApi from '@/api/diagnosis'
	import UploadFile from '../../components/uploadFile.vue';
	import config from '@/common.js';
	export default {
		components: {
			UploadFile,
		},
		data() {
			return {
				config: config,
				currentView: 1,
				formData: {
          examinationReportNo: '',// 体检报告编号
					applicant: 1,
					workerName: '',
					workerGender: '',
					workerBirthday: '',
					workerContactPhone: '',
					workerAddress: '',
					workerIdCardType: '',
					workerIdCardCode: '',
					workerZipCode: '',
					workerMailAddress: '',
					workerRegisteredResidenceAreaCode: '',
					workerRegisteredResidenceAddress: '',
					workerUsualAreaCode: '',
					workerUsualAddress: '',
					workerPastMedicalHistory: '',
					applicationDate: new Date().toISOString().split('T')[0],
					jobHistoryList: [],
					diseaseList: [],
					workerAgent: {},
					empName: '',
					empCreditCode: '',
					empAreaCode: '',
					empAddress: '',
					empContactPerson: '',
					empContactPhone: '',
					empZipCode: '',
					empIndustryCode: '',
					empEconomicTypeCode: '',
					empEnterpriseScaleCode: '',
					empEstablishmentDate: '',
					empTotalStaffNum: '',
					empProductionWorkerNum: '',
					empExternalStaffNum: '',
					empExposureHazardStaffNum: '',
					workEmpName: '',
					workEmpCreditCode: '',
					workEmpAreaCode: '',
					workEmpAddress: '',
					workEmpContactPerson: '',
					workEmpContactPhone: '',
					workEmpZipCode: '',
					workEmpIndustryCode: '',
					workEmpEconomicTypeCode: '',
					workEmpEnterpriseScaleCode: '',
					workEmpEstablishmentDate: '',
					workEmpTotalStaffNum: '',
					workEmpProductionWorkerNum: '',
					workEmpExternalStaffNum: '',
					workEmpExposureHazardStaffNum: '',
					hasAgent: false,
				},
				IDCardShow: false,
				AreaCodeShow: false,
				ygrShow: false,
				fileList1: [],
				fileList2: [],
				fileList3: [],
				fileList4: [],
				fileList5: [],
				fileList6: [],
				establishmentDateShow: false,
				hazardItem: {},
				hyShow: false,
				jjShow: false,
				qyShow: false,
				hyShow2: false,
				jjShow2: false,
				qyShow2: false,
				userId: '', //获取详情时的userId
				diagnosisId: '4',
			}
		},
		mounted() {
			this.formData.workerName = this.userInfo?.name || ''
			this.formData.workerIdCardCode = this.userInfo?.idNo || ''
			this.formData.workerContactPhone = this.userInfo?.phoneNum || ''
		},
		// 
		onLoad(options) {
			this.formData.institutionId = options.id
			this.userId = options.userId
			if (this.userId) {
				this.getDetail({
					userId: this.userId
				})
			}
      if(options.examinationReportNo){
        this.formData.examinationReportNo = options.examinationReportNo
      }
		},
		async created() {
			await this.getAreaList()
			await this.getDiseasesList()
			await this.getPersonGender()
			await this.getIdcardType()
			await this.getHazard()
		},
		computed: {
			...mapGetters(["areaList", "diseasesList", "personGender", "idcardType", "hazard", "userInfo"]),
			areaList2() {
				return this.processAreaData(this.areaList)
			},
			idcardType2() {
				return this.idcardType.map(item => ({
					text: item.dictLabel,
					value: item.dictCode
				}))
			},
			hazard2() {
				return this.hazard.map(item => ({
					text: item.dictLabel,
					value: item.dictCode
				}))
			},
			diseasesList2() {
				return this.processAreaData(this.diseasesList)
			}
		},
		methods: {
			...mapActions(['getAreaList', 'getDiseasesList', 'getPersonGender', 'getIdcardType', 'getHazard']),
			async getDetail(id) {
				const res = await diagnosisApi.getDiagnosisDetail(id)
				this.formData = {
					...res.data
				}
				this.formData.empTotalStaffNum = this.formData.empTotalStaffNum === 0 ? '0' : this.formData
					.empTotalStaffNum
				this.formData.empExternalStaffNum = this.formData.empExternalStaffNum === 0 ? '0' : this.formData
					.empExternalStaffNum
				this.formData.empProductionWorkerNum = this.formData.empProductionWorkerNum === 0 ? '0' : this.formData
					.empProductionWorkerNum
				this.formData.empExposureHazardStaffNum = this.formData.empExposureHazardStaffNum === 0 ? '0' : this
					.formData.empExposureHazardStaffNum
				this.formData.workEmpTotalStaffNum = this.formData.workEmpTotalStaffNum === 0 ? '0' : this.formData
					.workEmpTotalStaffNum
				this.formData.workEmpProductionWorkerNum = this.formData.workEmpProductionWorkerNum === 0 ? '0' : this
					.formData.workEmpProductionWorkerNum
				this.formData.workEmpExternalStaffNum = this.formData.workEmpExternalStaffNum === 0 ? '0' : this
					.formData.workEmpExternalStaffNum
				this.formData.workEmpExposureHazardStaffNum = this.formData.workEmpExposureHazardStaffNum === 0 ? '0' :
					this.formData.workEmpExposureHazardStaffNum
				if (!this.formData.workerAgent) {
					this.formData.workerAgent = {
						agentName: "",
						agentIdCardCode: "",
						agentContactPhone: "",
						relationship: ""
					}
				}
			},
			processAreaData(data) {
				if (!data) return [];
				return data.map(item => ({
					text: item.dictLabel,
					value: item.dictCode,
					children: item.children ? this.processAreaData(item.children) : null
				}));
			},
			onAddressChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			onUsualAddressChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			onEmpAreaChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			onWorkAreaCodeChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			handlePrev() {
				this.currentView--
			},
			validateFormData1() {
				const requiredFields = [{
						field: 'workerName',
						message: '请填写姓名'
					},
					{
						field: 'workerGender',
						message: '请选择性别'
					},
					{
						field: 'workerBirthday',
						message: '请选择出生日期'
					},
					{
						field: 'workerContactPhone',
						message: '请填写联系电话'
					},
					{
						field: 'workerAddress',
						message: '请填写住址'
					},
					{
						field: 'workerIdCardType',
						message: '请选择证件类别'
					},
					{
						field: 'workerIdCardCode',
						message: '请填写证件号码'
					},
					{
						field: 'workerZipCode',
						message: '请填写邮政编码'
					},
					{
						field: 'workerMailAddress',
						message: '请填写通讯地址'
					},
					{
						field: 'workerPastMedicalHistory',
						message: '请填写既往病史'
					},
					{
						field: 'workerRegisteredResidenceAreaCode',
						message: '请选择户籍所在地'
					},
					{
						field: 'workerRegisteredResidenceAddress',
						message: '请填写户籍详细地址'
					},
					{
						field: 'workerUsualAreaCode',
						message: '请选择经常居住地'
					},
					{
						field: 'workerUsualAddress',
						message: '请填写居住地详细地址'
					}
				];

				const formatChecks = [{
						field: 'workerContactPhone',
						regex: /^\d{11}$/,
						message: '联系电话必须是11位'
					},
					{
						field: 'workerZipCode',
						regex: /^\d{6}$/,
						message: '邮政编码必须是6位'
					},
					{
						field: 'workerIdCardCode',
						regex: /^\d{17}[\dXx]$/,
						message: '证件号码必须是18位'
					}
				];

				// 检查必填字段
				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				return true;
			},
			validateFormData2() {
				if (this.formData.jobHistoryList.length == 0) {
					uni.showToast({
						title: '职业史列表不能为空，请至少添加一条职业史记录',
						icon: 'none'
					});
					return false;
				}

				return true;
			},
			validateFormData3() {
				const requiredFields = [{
						field: 'empName',
						message: '请填写用人单位名称'
					},
					{
						field: 'empCreditCode',
						message: '请填写统一社会信用代码'
					},
					{
						field: 'empAreaCode',
						message: '请选择所在地区'
					},
					{
						field: 'empAddress',
						message: '请填写地址'
					},
					{
						field: 'empContactPerson',
						message: '请填写联系人'
					},
					{
						field: 'empContactPhone',
						message: '请填写联系方式'
					},
					{
						field: 'empZipCode',
						message: '请填写邮编'
					},
					{
						field: 'empIndustryCode',
						message: '请选择行业'
					},
					{
						field: 'empEconomicTypeCode',
						message: '请选择经济类型'
					},
					{
						field: 'empEnterpriseScaleCode',
						message: '请选择企业规模'
					},
					{
						field: 'empEstablishmentDate',
						message: '请选择单位成立时间'
					},
					{
						field: 'empTotalStaffNum',
						message: '请输入职工总人数'
					},
					{
						field: 'empProductionWorkerNum',
						message: '请输入生产工人总数'
					},
					{
						field: 'empExternalStaffNum',
						message: '请输入外委人员数'
					},
					{
						field: 'empExposureHazardStaffNum',
						message: '请输入接触有毒有害作业人数'
					}
				];

				const formatChecks = [{
						field: 'empCreditCode',
						regex: /^[A-Za-z0-9]{18}$/,
						message: '统一社会信用代码必须是18位'
					}, {
						field: 'empContactPhone',
						regex: /^\d{11}$/,
						message: '联系方式必须是11位'
					},
					{
						field: 'empZipCode',
						regex: /^\d{6}$/,
						message: '邮政编码必须是6位'
					}
				];


				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}
				return true;
			},
			validateFormData4() {
				const requiredFields = [{
						field: 'workEmpName',
						message: '请填写用工单位名称'
					},
					{
						field: 'workEmpCreditCode',
						message: '请填写统一社会信用代码'
					},
					{
						field: 'workEmpAreaCode',
						message: '请选择所在地区'
					},
					{
						field: 'workEmpAddress',
						message: '请填写地址'
					},
					{
						field: 'workEmpContactPerson',
						message: '请填写联系人'
					},
					{
						field: 'workEmpContactPhone',
						message: '请填写联系方式'
					},
					{
						field: 'workEmpZipCode',
						message: '请填写邮编'
					},
					{
						field: 'workEmpIndustryCode',
						message: '请选择行业'
					},
					{
						field: 'workEmpEconomicTypeCode',
						message: '请选择经济类型'
					},
					{
						field: 'workEmpEnterpriseScaleCode',
						message: '请选择企业规模'
					},
					{
						field: 'workEmpEstablishmentDate',
						message: '请选择单位成立时间'
					},
					{
						field: 'workEmpTotalStaffNum',
						message: '请输入职工总人数'
					},
					{
						field: 'workEmpProductionWorkerNum',
						message: '请输入生产工人总数'
					},
					{
						field: 'workEmpExternalStaffNum',
						message: '请输入外委人员数'
					},
					{
						field: 'workEmpExposureHazardStaffNum',
						message: '请输入接触有毒有害作业人数'
					}
				];


				const formatChecks = [{
						field: 'workEmpCreditCode',
						regex: /^[A-Za-z0-9]{18}$/,
						message: '统一社会信用代码必须是18位'
					}, {
						field: 'workEmpContactPhone',
						regex: /^\d{11}$/,
						message: '联系方式必须是11位'
					},
					{
						field: 'workEmpZipCode',
						regex: /^\d{6}$/,
						message: '邮政编码必须是6位'
					}

				];

				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}


				return true;
			},
			validateFormData5() {
				const requiredFields = [{
						field: 'agentName',
						message: '请填写代理人姓名'
					},
					{
						field: 'relationship',
						message: '请填写与当事人关系'
					},
					{
						field: 'agentIdCardCode',
						message: '请填写代理人身份证号码'
					},
					{
						field: 'agentContactPhone',
						message: '请填写代理人联系电话'
					}
				];


				const formatChecks = [{
						field: 'agentIdCardCode',
						regex: /^[A-Za-z0-9]{18}$/,
						message: '统一社会信用代码必须是18位'
					}, {
						field: 'agentContactPhone',
						regex: /^\d{11}$/,
						message: '联系方式必须是11位'
					}

				];
				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData.workerAgent[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData.workerAgent[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}
				return true;
			},
			handleNext() {
				let isValid = true;
				switch (this.currentView) {
					case 1:
						isValid = this.validateFormData1();
						break;
					case 2:
						isValid = this.validateFormData2();
						break;
					case 3:
						isValid = this.validateFormData3();
						break;
					case 4:
						isValid = this.validateFormData4();
						break;
					case 5:
						isValid = !this.formData.hasAgent || this.validateFormData5();
						break;
					case 6:
						if (this.formData.diseaseList.length === 0) {
							uni.showToast({
								title: '职业病种类不能为空，请至少添加一条职业病种类',
								icon: 'none'
							});
							return false;
						}
						return this.handleStore()
							.then(() => this.currentView++, this.userId ? this.getZdList() : '')
							.catch(() => {
								uni.showToast({
									title: '暂存数据失败，请稍后再试',
									icon: 'none'
								});
							});
					case 7:
						return;
					default:
						return;
				}

				if (!isValid) return false;

				this.currentView++;
				this.$nextTick(() => {
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 0
					});
				});
			},
			// 暂存
			async handleStore() {
				let params = {
					...this.formData,
					// workerIdCardType: this.idcardType?.find(item => item.dictLabel == this.formData
					// 	.workerIdCardType)?.dictCode,
				};
				params.jobHistoryList?.forEach(item => {
					item.jobHistoryHazardList?.forEach(ele => {
						const hazard = this.hazard?.find(val => val.dictLabel == ele.hazardCode);
						if (hazard) ele.hazardCode = hazard.dictCode;
					});
				});
				const res = params.id ? await diagnosisApi.editDiagnosis(params) : await diagnosisApi.addDiagnosis(
					params);
				if (res.data.success) {
					uni.showToast({
						title: "暂存成功",
						icon: 'success',
						duration: 1200
					})
					this.diagnosisId = res.data.data?.id || this.formData.id;
					// this.formData.id = res.data.data?.id;
					return true;
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return false;
				}
			},
			// 最后一步的暂存
			async handleTemporyStore() {
				if (this.fileList1.length === 0) {
					uni.showToast({
						title: '身份证正反面是必填项，请上传相关文件',
						icon: 'none'
					});
					return false;
				}

				if (this.fileList2.length === 0) {
					uni.showToast({
						title: '劳动相关证明是必填项，请上传相关文件',
						icon: 'none'
					});
					return false;
				}
				let params = {
					...this.formData,
					// workerIdCardType: this.idcardType?.find(item => item.dictLabel == this.formData
					// 	.workerIdCardType)?.dictCode,
				};
				params.jobHistoryList?.forEach(item => {
					item.jobHistoryHazardList?.forEach(ele => {
						const hazard = this.hazard?.find(val => val.dictLabel == ele.hazardCode);
						if (hazard) ele.hazardCode = hazard.dictCode;
					});
				});
				params.id = this.diagnosisId;
				const res = this.diagnosisId ? await diagnosisApi.editDiagnosis(params) : await diagnosisApi
					.addDiagnosis(
						params);
				if (res.data.success) {
					uni.showToast({
						title: "暂存成功",
						icon: 'success',
						duration: 1200
					})
					setTimeout(() => {
						uni.navigateBack();
					}, 1200)
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
				}
			},
			// 提交申请
			handleSave() {
				if (this.fileList1.length === 0) {
					uni.showToast({
						title: '身份证正反面是必填项，请上传相关文件',
						icon: 'none'
					});
					return false;
				}

				if (this.fileList2.length === 0) {
					uni.showToast({
						title: '劳动相关证明是必填项，请上传相关文件',
						icon: 'none'
					});
					return false;
				}
				uni.showModal({
					title: '提示',
					content: '提交申请后数据不能修改，您确定要提交吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								const res = await diagnosisApi.submitDiagnosis({
									id: this.diagnosisId
								})
								if (res.data.success) {
									uni.showToast({
										title: "提交成功",
										icon: 'success',
										duration: 1200
									})
									setTimeout(() => {
										uni.navigateBack();
									}, 1200)
								} else {
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
								}
							} catch (error) {
								console.log(error)
							}
						}
					}
				})

			},
			handleCancel() {
				uni.showModal({
					title: '提示',
					content: '数据还未保存，您确定要取消并返回吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				})
			},
			handleSync() {
				this.formData.workEmpName = this.formData.empName
				this.formData.workEmpCreditCode = this.formData.empCreditCode
				this.formData.workEmpAreaCode = this.formData.empAreaCode
				this.formData.workEmpAddress = this.formData.empAddress
				this.formData.workEmpContactPerson = this.formData.empContactPerson
				this.formData.workEmpContactPhone = this.formData.empContactPhone
				this.formData.workEmpZipCode = this.formData.empZipCode
				this.formData.workEmpIndustryCode = this.formData.empIndustryCode
				this.formData.workEmpEconomicTypeCode = this.formData.empEconomicTypeCode
				this.formData.workEmpEnterpriseScaleCode = this.formData.empEnterpriseScaleCode
				this.formData.workEmpEstablishmentDate = this.formData.empEstablishmentDate
				this.formData.workEmpTotalStaffNum = this.formData.empTotalStaffNum
				this.formData.workEmpProductionWorkerNum = this.formData.empProductionWorkerNum
				this.formData.workEmpExternalStaffNum = this.formData.empExternalStaffNum
				this.formData.workEmpExposureHazardStaffNum = this.formData.empExposureHazardStaffNum
			},
			handleSectionIDCard(e) {
				this.formData.workerIdCardType = e.value[0].text
				this.IDCardShow = false
			},
			handleSectionzyb(e) {
				console.log(e, 'e');
			},
			handleYgrShow(ele, e) {
				ele.hazardCode = e.value[0].text
				this.ygrShow = false
			},
			handleSectionIndustry(e) {
				this.formData.empIndustryCode = e.value[0]
				this.hyShow = false
			},
			handleSectionEconomic(e) {
				this.formData.empEconomicTypeCode = e.value[0]
				this.jjShow = false
			},
			handleSectionEnterprise(e) {
				this.formData.empEnterpriseScaleCode = e.value[0]
				this.qyShow = false
			},

			handleSectionIndustry2(e) {
				this.formData.workEmpIndustryCode = e.value[0]
				this.hyShow2 = false
			},
			handleSectionEconomic2(e) {
				this.formData.workEmpEconomicTypeCode = e.value[0]
				this.jjShow2 = false
			},
			handleSectionEnterprise2(e) {
				this.formData.workEmpEnterpriseScaleCode = e.value[0]
				this.qyShow2 = false
			},
			handleAddBli() {
				this.formData.jobHistoryList.push({
					id: '',
					empName: '',
					job: '',
					post: '',
					operationProcess: '',
					protectiveMeasure: '',
					personalProtection: '',
					jobHistoryHazardList: [{
						hazardCode: '',
						concentration: '',
						contactTime: '',
						startContactDate: '',
						endContactDate: ''
					}]
				})
			},
			handleAddItem(item) {
				item.jobHistoryHazardList.push({
					hazardCode: '',
					concentration: '',
					contactTime: '',
					startContactDate: '',
					endContactDate: ''
				})
			},

			// 删除接触史
			async handleDeleteHazard(job, hz, hzIdx) {
				if (!hz.id) {
					job.jobHistoryHazardList.splice(hzIdx, 1)
					return
				}
				uni.showModal({
					title: '提示',
					content: '您确定要删除该接触史吗？',
					success: async (res) => {
						if (res.confirm) {
							const res = await diagnosisApi.deleteDiagnosisJobHistoryHazard({
								id: hz.id
							})
							if (res.data.success) {
								job.jobHistoryHazardList.splice(hzIdx, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: res.data.msg || "删除失败",
									icon: 'error',
									duration: 1200
								})
							}
						}
					}
				})
			},

			// 保存职业史
			async handleSaveJob(row) {
				let params = {
					id: row.id ? row.id : '',
					diagnosisId: this.formData.id ? this.formData.id : '',
					empName: row.empName,
					job: row.job,
					post: row.post,
					operationProcess: row.operationProcess,
					protectiveMeasure: row.protectiveMeasure,
					personalProtection: row.personalProtection,
					jobHistoryHazardList: row.jobHistoryHazardList
				}
				const res = await diagnosisApi.saveDiagnosisJobHistory(params)
				if (res.data) {
					uni.showToast({
						title: "保存成功",
						icon: 'success',
						duration: 1200
					})
				}
			},

			// 删除职业史
			async handleDeleteJob(item, index) {
				if (!item.id) {
					this.formData.jobHistoryList.splice(index, 1)
					return
				}
				uni.showModal({
					title: '提示',
					content: '您确定要删除该职业史吗？',
					success: async (res) => {
						if (res.confirm) {
							const res = await diagnosisApi.deleteDiagnosisJobHistory({
								id: item.id
							})
							if (res.data.success) {
								this.formData.jobHistoryList.splice(index, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: res.data.msg || "删除失败",
									icon: 'error',
									duration: 1200
								})
							}
						}
					}
				})

			},
			handleAddDis() {
				this.formData.diseaseList.push({
					diseaseCode: '',
					otherDiseaseName: ''
				})
			},
			// 删除图片
			deleteFile(event) {
				console.log('event', event);
				this[`fileList${event.name}`].splice(event.index, 1);
			},
			async getZdList() {
				const res = await diagnosisApi.getProvideFile({
					id: this.userId
				})
				res.data.data.forEach(item => {
					if (item.fileClassify == 'DIAGNOSIS_ID_CARD') {
						this.fileList1 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'EMPLOYMENT_RELATION_PROOF') {
						this.fileList2 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'OCCUPATIONAL_HISTORY') {
						this.fileList3 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'EXAMINATION_RESULT') {
						this.fileList4 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'DETECTION_RESULT') {
						this.fileList5 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
					if (item.fileClassify == 'PERSONAL_DOSE_RECORD') {
						this.fileList6 = item.fileList.map((value) => {
							return {
								id: value.id,
								diagnosisId: value.diagnosisId,
								name: value.fileName,
								url: value.fileUrl,
								fileClassify: value.fileClassify,
								createTime: value.createTime
							}
						})
					}
				})
			},

		},
	}
</script>


<style lang="scss" scoped>
	.institution {
		width: 100%;
		// height: 100vh;
		// padding: 0 30rpx;
		box-sizing: border-box;
	}

	.nav-left {
		display: flex;
		align-items: center;
		width: auto;
		color: #fff;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.section-body {
		padding-top: 30rpx;

		.form {
			width: 100%;
			padding-bottom: 150rpx;

			.title {
				font-family: Source Han Sans;
				font-size: 16px;
				font-weight: bold;
				color: #3D3D3D;
			}

			.addBlList {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 30rpx;

				view {
					width: 102px;
					height: 32px;
					text-align: center;
					line-height: 32px;
					border-radius: 4px;
					background: #4163E1;
					color: #fff;
				}
			}
		}

		#section-2 {
			.title {
				margin-bottom: 32rpx;
			}
		}

		#section-6 {
			.title {
				margin-bottom: 32rpx;
			}

			.u-form {
				.u-form-item {
					padding: 0 24rpx;
					box-sizing: border-box;
					box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.0997), 0px 2px 4px 0px rgba(0, 0, 0, 0.0372);
					margin-bottom: 32rpx;
				}
			}
		}
	}

	.u-tabbar {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 56px;
		background: #FFFFFF;
		box-shadow: 0px 1px 7px 1px rgba(0, 0, 0, 0.2046);
		display: flex;
		align-items: center;
		justify-content: flex-end;


	}

	.info-item {
		width: 100%;
		margin-bottom: 20rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.info-item.full-width {
		width: 100%;
	}

	.section {
		background-color: #ffffff;
		border-radius: 12rpx;
		max-height: 400rpx;


		.career-list {
			height: 400rpx;
			overflow-y: auto;
		}
	}

	.career-item {
		border: 1px solid #eaeaea;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		background-color: #f9fafb;
	}

	::v-deep .u-form-item__body {
		padding: 10rpx 0;
	}

	.btn {
		width: 148rpx;
		height: 64rpx;
		line-height: 64rpx;
		text-align: center;
		border-radius: 4px;
		background: #F4F4F5;
		border: 1px solid #C7C9CC;
		margin-right: 40rpx;

		&.sync {
			background: #ECF5FF;
			border: 1px solid #9FCEFF;
			color: #409EFF;
		}

		&.prev {
			background: #ECF5FF;
			border: 1px solid #9FCEFF;
			color: #409EFF;
		}

		&.next {
			background: #4163E1;
			border: 1px solid #4163E1;
			color: #fff;
		}
	}

	.list-popup {
		width: 650rpx;
		background-color: #ffffff;
		border-radius: 12rpx;

		.popup-header {
			padding: 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #f0f0f0;
		}

		.popup-header .title {
			font-size: 16px;
			font-weight: 500;
			color: #333333;
		}

		.popup-content {
			padding: 30rpx;
		}
	}

	::v-deep .uni-date-x--border[data-v-f2e7c4e8] {
		border: 0px solid #e5e5e5;
	}


	::v-deep .input-value-border[data-v-3ed22fe0] {
		border: none
	}

	::v-deep .uni-select[data-v-6b64008e] {
		border: none;
		border-bottom: none;
	}
</style>