import req from '@/utils/http.js' //导入 封装的请求

export default {
  // 获取当前账号的授权申请列表
  getEHealthRecordAuthList: data => {
    return req({
      url: `manage/eHealthRecord/getEHealthRecordAuthList`,
      method: 'get',
      data
    });
  },

  // 授权申请处理
  handleEHealthRecordAuth: data => {
    return req({
      url: `manage/eHealthRecord/handleEHealthRecordAuth`,
      method: 'post',
      data
    });
  },

  // 获取个人基本信息
  getEHealthRecordBaseInfo: data => {
    return req({
      url: `manage/eHealthRecord/getEHealthRecordBaseInfo`,
      method: 'get',
      data
    });
  },

  // 编辑个人基本信息
  updateEHealthRecordBaseInfo: data => {
    return req({
      url: `manage/eHealthRecord/updateEHealthRecordBaseInfo`,
      method: 'post',
      data
    });
  },
  // 获取监管单位列表
  getSupervisionList(data) {
    return req({
      url: `manage/eHealthRecord/getSupervisionList`,
      method: 'post',
      data
    });
  },

  // 提出申诉
  postComplaint(data) {
    return req({
      url: `manage/eHealthRecord/postComplaint`,
      method: 'post',
      data
    });
  },

  //#region 职业史
  // 获取劳动者是否可以在劳动者端编辑个人职业史
  getLaborIsEdit(data) {
    return req({
      url: `manage/eHealthRecord/getLaborIsEdit`,
      method: 'get',
      data
    });
  },
  // 添加职业史
  addEmploymentHistory(data) {
    return req({
      url: `manage/eHealthRecord/addEmploymentHistory`,
      method: 'post',
      data
    });
  },
  // 编辑职业史
  editEmploymentHistory(data) {
    return req({
      url: `manage/eHealthRecord/editEmploymentHistory`,
      method: 'post',
      data
    });
  },
  // 删除职业史
  deleteEmploymentHistory(data) {
    return req({
      url: `manage/eHealthRecord/deleteEmploymentHistory`,
      method: 'post',
      data
    });
  },
  // 获取职业史
  getEmploymentHistory(data) {
    return req({
      url: `manage/eHealthRecord/getEmploymentHistory`,
      method: 'get',
      data
    });
  },
  //#endregion 职业史

  //#region 地区数据
  // 获取地区数据（统一接口）
  findDistricts(data) {
    return req({
      url: `app/user/findDistricts`,
      method: 'get',
      data
    });
  },

  // 获取省份列表（level=0）
  getProvinces() {
    return this.findDistricts({ level: 0 });
  },

  // 根据省份ID获取城市列表（level=1）
  getCitiesByProvince(provinceCode) {
    return this.findDistricts({ level: 1, parent_code: provinceCode });
  },

  // 根据城市ID获取区县列表（level=2）
  getDistrictsByCity(cityCode) {
    return this.findDistricts({ level: 2, parent_code: cityCode });
  },

  // 根据区县ID获取乡镇列表（level=3）
  getTownsByDistrict(districtCode) {
    return this.findDistricts({ level: 3, parent_code: districtCode });
  },
  //#endregion 地区数据

  // 获取诊断记录列表
  getDiaList: data => {
    return req({
      url: `manage/eHealthRecord/getDiaList`,
      method: 'post',
      data
    });
  },
  // 获取鉴定记录列表
  getIdentificationList: data => {
    return req({
      url: `manage/eHealthRecord/getIdentificationList`,
      method: 'post',
      data
    });
  },

    // 查找危害因素
  findHarmFactors(data) {
    return req({
      url: `manage/eHealthRecord/findHarmFactors`,
      method: 'get',
      data
    });
  },
}

