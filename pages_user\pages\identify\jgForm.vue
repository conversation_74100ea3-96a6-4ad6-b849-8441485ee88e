<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="在线申请" />
		<view slot="gBody" class="grace-body content">
			<view class="institution">
				<view class="section-body">
					<view class="form" id="section-1" v-if="currentView==1">
						<view class="title">职业病鉴定申请表</view>
						<u--form labelWidth="auto" labelPosition="left" ref="modelRef">
							<u-form-item label="鉴定类型">
								<view class="type-select-container">
									<!-- 单选按钮组 -->
									<u-radio-group placement="row" v-model="formData.type"
										:disabled="true"
										class="radio-group">
										<u-radio label="首次鉴定" :name="0"></u-radio>
										<u-radio label="兵团级鉴定" :name="1"></u-radio>
									</u-radio-group>
									<!-- 选择按钮 -->
									<u-button v-if="formData.type !== null" type="primary" size="mini"
										:disabled="formData.id || formData.diagnosisId || formData.firstIdentificationId"
										@click="handleTypeOpen" class="select-btn">
										{{ formData.type == 1?'选择鉴定记录':'选择诊断记录' }}
									</u-button>
								</view>
							</u-form-item>
							<u-form-item label="姓名" prop="workerName">
								<u--input placeholder="请输入姓名" v-model="formData.workerName"></u--input>
							</u-form-item>
							<u-form-item label="性别">
								<u-radio-group placement="row" v-model="formData.workerGender">
									<u-radio label="男" name="1" style="margin-left: 10px;"></u-radio>
									<u-radio label="女" name="2" style="margin-left: 10px;"></u-radio>
									<u-radio label="未知" name="3" style="margin-left: 10px;"></u-radio>
								</u-radio-group>
							</u-form-item>
							<u-form-item label="出生日期" prop="workerBirthday">
								<uni-datetime-picker type="date" v-model="formData.workerBirthday" />
							</u-form-item>
							<u-form-item label="联系电话">
								<u--input placeholder="请输入联系电话" v-model="formData.workerContactPhone"></u--input>
							</u-form-item>
							<u-form-item label="住址">
								<u--input placeholder="请输入住址" v-model="formData.workerAddress"></u--input>
							</u-form-item>
							<u-form-item label="证件类别" @click="IDCardShow=true">
								<!-- <u--input placeholder="请选择证件类别" v-model="formData.workerIdCardType"
						 ></u--input>
						<u-picker :show="IDCardShow" :columns="idcardType2" @cancel="IDCardShow=false"
							@confirm="handleSectionIDCard"></u-picker> -->
								<uni-data-select v-model="formData.workerIdCardType"
									:localdata="idcardType2"></uni-data-select>
							</u-form-item>
							<u-form-item label="证件号码">
								<u--input placeholder="请输入证件号码" v-model="formData.workerIdCardCode"></u--input>
							</u-form-item>
							<u-form-item label="邮政编码">
								<u--input placeholder="请输入邮政编码" v-model="formData.workerZipCode"></u--input>
							</u-form-item>
							<u-form-item label="通讯地址">
								<u--input placeholder="请输入通讯地址" v-model="formData.workerMailAddress"></u--input>
							</u-form-item>
							<u-form-item label="既往病史">
								<u--input placeholder="请输入既往病史" v-model="formData.workerPastMedicalHistory"></u--input>
							</u-form-item>
							<u-form-item label="户籍所在地">
								<uni-data-picker :localdata="areaList2"
									v-model="formData.workerRegisteredResidenceAreaCode" popup-title="选择户籍所在地"
									@change="onAddressChange" placeholder="请选择户籍所在地">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="户籍详细地址">
								<u--input placeholder="请输入详细地址"
									v-model="formData.workerRegisteredResidenceAddress"></u--input>
							</u-form-item>
							<u-form-item label="经常居住地">
								<uni-data-picker :localdata="areaList2" v-model="formData.workerUsualAreaCode"
									popup-title="选择经常居住地" @change="onUsualAddressChange" placeholder="请选择经常居住地">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="居住地详细地址">
								<u--input placeholder="请输入详细地址" v-model="formData.workerUsualAddress"></u--input>
							</u-form-item>
							<u-form-item label="申请日期">
								<uni-datetime-picker type="date" v-model="formData.applicationDate" disabled />
							</u-form-item>
							<u-form-item label="申请理由">
								<u--input placeholder="请输入申请理由" v-model="formData.applicationReason"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-2" v-if="currentView==2">
						<view class="title">劳动者职业史和职业病危害接触史</view>
						<view class="addBlList" style="margin-bottom: 20rpx;">
							<view @click="handleAddBli">添加职业史</view>
						</view>
						<u-collapse accordion>
							<u-collapse-item v-for="(item,index) in formData.jobHistoryList" :key="item.id || index">
								<text slot="title" class="u-page__item__title__slot-title">工作单位{{index+1}}</text>
								<text slot="value" class="u-page__item__title__slot-title" style="color: #409EFF;">展开
								</text>
								<text slot="right-icon" class="u-page__item__title__slot-title">
									<u-icon name="arrow-right-double" color="#409EFF"></u-icon>
								</text>
								<u--form labelWidth="auto">
									<u-form-item label="工作单位">
										<u--input placeholder="请输入工作单位" v-model="item.empName"></u--input>
									</u-form-item>
									<u-form-item label="岗位">
										<u--input placeholder="请输入岗位" v-model="item.post"></u--input>
									</u-form-item>
									<u-form-item label="操作过程">
										<u--input placeholder="请输入操作过程" v-model="item.operationProcess"></u--input>
									</u-form-item>
									<u-form-item label="防护措施">
										<u--input placeholder="请输入防护措施" v-model="item.protectiveMeasure"></u--input>
									</u-form-item>
									<u-form-item label="个人防护">
										<u--input placeholder="请输入个人防护" v-model="item.personalProtection"></u--input>
									</u-form-item>
									<view class="addBlList" style="margin:20rpx 0">
										<view @click="handleAddItem(item)">添加接触史</view>
									</view>
									<u-collapse accordion style="height: 50vh;overflow: scroll;">
										<u-collapse-item v-for="(ele,hzIdx) in item.jobHistoryHazardList"
											:key="ele.id || hzIdx">
											<text slot="title"
												class="u-page__item__title__slot-title">类别{{hzIdx+1}}</text>
											<text slot="value" class="u-page__item__title__slot-title"
												style="color: #409EFF;">展开</text>
											<text slot="right-icon" class="u-page__item__title__slot-title">
												<u-icon name="arrow-right-double" color="#409EFF"></u-icon>
											</text>
											<u--form labelWidth="auto">
												<u-form-item label="危害因素编码" @click="ygrShow=true">
													<!-- <u--input placeholder="请选择危害因素编码" v-model="ele.hazardCode"></u--input>
											<u-picker :show="ygrShow" :columns="hazard2" @cancel="ygrShow=false"
												@confirm="handleYgrShow(item,ele, $event,hzIdx)"></u-picker> -->
													<uni-data-select v-model="ele.hazardCode" :localdata="hazard2"
														@change="handleYgrShow(item,ele,$event,hzIdx)"></uni-data-select>
												</u-form-item>
												<u-form-item label="浓度">
													<u--input placeholder="请输入浓度"
														v-model="ele.concentration"></u--input>
												</u-form-item>
												<u-form-item label="接触时间">
													<u--input placeholder="请输入接触时间"
														v-model="ele.contactTime"></u--input>
												</u-form-item>
												<u-form-item label="接触开始时间">
													<uni-datetime-picker type="date" v-model="ele.startContactDate" />
												</u-form-item>
												<u-form-item label="接触结束时间">
													<uni-datetime-picker type="date" v-model="ele.endContactDate" />
												</u-form-item>
												<u-form-item>
													<u-button type="error" size="mini"
														@click="handleDeleteHazard(item, ele, hzIdx)">删除接触史</u-button>
												</u-form-item>
											</u--form>
										</u-collapse-item>
									</u-collapse>
									<u-form-item>
										<u-button v-show="formData.id" type="primary" size="mini"
											@click="handleSaveJob(item)">保存职业史/接触史</u-button>
										<u-button style="margin-left: 5px;" type="error" size="mini"
											@click="handleDeleteJob(item, index)">删除职业史</u-button>
									</u-form-item>
								</u--form>
							</u-collapse-item>
						</u-collapse>

					</view>
					<view class="form" id="section-3" v-if="currentView==3">
						<view class="title">用人单位信息</view>
						<u--form labelWidth="auto">
							<u-form-item label="用人单位名称">
								<u--input placeholder="请选择用人单位名称" v-model="formData.empName"></u--input>
							</u-form-item>
							<u-form-item label="统一社会信用代码">
								<u--input placeholder="请输入统一社会信用代码" v-model.trim="formData.empCreditCode"></u--input>
							</u-form-item>
							<u-form-item label="所在地区" @click="AreaCodeShow=true">
								<uni-data-picker :localdata="areaList2" v-model="formData.empAreaCode"
									popup-title="选择所在地区" @change="onEmpAreaChange" placeholder="请选择所在地区">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="地址">
								<u--input placeholder="请输入地址" v-model="formData.empAddress"></u--input>
							</u-form-item>
							<u-form-item label="联系人">
								<u--input placeholder="请输入联系人" v-model="formData.empContactPerson"></u--input>
							</u-form-item>
							<u-form-item label="联系方式">
								<u--input placeholder="请输入联系方式" v-model="formData.empContactPhone"></u--input>
							</u-form-item>
							<u-form-item label="邮编">
								<u--input placeholder="请输入邮编" v-model="formData.empZipCode"></u--input>
							</u-form-item>
							<u-form-item label="行业" @click="hyShow = true">
								<u--input placeholder="请选择行业" v-model="formData.empIndustryCode"></u--input>
								<u-picker :show="hyShow" :columns="[['炼铁','钢压延加工','硅冶炼','其他常用有色金属冶炼']]"
									@cancel="hyShow=false" @confirm="handleSectionIndustry"></u-picker>
							</u-form-item>
							<u-form-item label="经济类型" @click="jjShow = true">
								<u--input placeholder="请选择经济类型" v-model="formData.empEconomicTypeCode"></u--input>
								<u-picker :show="jjShow"
									:columns="[['内资','国有全资','集体全资','股份合作','有限责任(公司)','其他有限责任(公司)']]"
									@cancel="jjShow=false" @confirm="handleSectionEconomic"></u-picker>
							</u-form-item>
							<u-form-item label="企业规模" @click="qyShow = true">
								<u--input placeholder="请选择企业规模" v-model="formData.empEnterpriseScaleCode"></u--input>
								<u-picker :show="qyShow" :columns="[['大','中','小','微型']]" @cancel="qyShow=false"
									@confirm="handleSectionEnterprise"></u-picker>
							</u-form-item>
							<u-form-item label="单位成立时间">
								<uni-datetime-picker type="date" v-model="formData.empEstablishmentDate" />
							</u-form-item>
							<u-form-item label="职工总人数">
								<u--input placeholder="请输入职工总人数" v-model="formData.empTotalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="生产工人总数">
								<u--input placeholder="请输入生产工人总数" v-model="formData.empProductionWorkerNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="外委人员数">
								<u--input placeholder="请输入外委人员数" v-model="formData.empExternalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="接触有毒有害作业人数">
								<u--input placeholder="请输入接触有毒有害作业人数" v-model="formData.empExposureHazardStaffNum"
									type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-4" v-if="currentView==4">
						<view class="title">用工单位信息</view>
						<u--form labelWidth="auto">
							<u-form-item label="用工单位名称">
								<u--input placeholder="请输入用工单位名称" v-model="formData.workEmpName"></u--input>
							</u-form-item>
							<u-form-item label="统一社会信用代码">
								<u--input placeholder="请输入统一社会信用代码"
									v-model.trim="formData.workEmpCreditCode"></u--input>
							</u-form-item>
							<u-form-item label="所在地区" @click="AreaCodeShow=true">
								<uni-data-picker :localdata="areaList2" v-model="formData.workEmpAreaCode"
									popup-title="选择所在地区" @change="onWorkAreaCodeChange" placeholder="请选择所在地区">
								</uni-data-picker>
							</u-form-item>
							<u-form-item label="地址">
								<u--input placeholder="请输入地址" v-model="formData.workEmpAddress"></u--input>
							</u-form-item>
							<u-form-item label="联系人">
								<u--input placeholder="请输入联系人" v-model="formData.workEmpContactPerson"></u--input>
							</u-form-item>
							<u-form-item label="联系方式">
								<u--input placeholder="请输入联系方式" v-model="formData.workEmpContactPhone"></u--input>
							</u-form-item>
							<u-form-item label="邮编">
								<u--input placeholder="请输入邮编" v-model="formData.workEmpZipCode"></u--input>
							</u-form-item>
							<u-form-item label="行业" @click="hyShow2 = true">
								<u--input placeholder="请选择行业" v-model="formData.workEmpIndustryCode"></u--input>
								<u-picker :show="hyShow2" :columns="[['炼铁','钢压延加工','硅冶炼','其他常用有色金属冶炼']]"
									@cancel="hyShow2=false" @confirm="handleSectionIndustry2"></u-picker>
							</u-form-item>
							<u-form-item label="经济类型" @click="jjShow2 = true">
								<u--input placeholder="请选择经济类型" v-model="formData.workEmpEconomicTypeCode"></u--input>
								<u-picker :show="jjShow2"
									:columns="[['内资','国有全资','集体全资','股份合作','有限责任(公司)','其他有限责任(公司)']]"
									@cancel="jjShow2=false" @confirm="handleSectionEconomic2"></u-picker>
							</u-form-item>
							<u-form-item label="企业规模" @click="qyShow2 = true">
								<u--input placeholder="请选择企业规模"
									v-model="formData.workEmpEnterpriseScaleCode"></u--input>
								<u-picker :show="qyShow2" :columns="[['大','中','小','微型']]" @cancel="qyShow2=false"
									@confirm="handleSectionEnterprise2"></u-picker>
							</u-form-item>
							<u-form-item label="单位成立时间">
								<uni-datetime-picker type="date" v-model="formData.workEmpEstablishmentDate" />
							</u-form-item>
							<u-form-item label="职工总人数">
								<u--input placeholder="请输入职工总人数" v-model="formData.workEmpTotalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="生产工人总数">
								<u--input placeholder="请输入生产工人总数" v-model="formData.workEmpProductionWorkerNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="外委人员数">
								<u--input placeholder="请输入外委人员数" v-model="formData.workEmpExternalStaffNum"
									type="number"></u--input>
							</u-form-item>
							<u-form-item label="接触有毒有害作业人数">
								<u--input placeholder="请输入接触有毒有害作业人数" v-model="formData.workEmpExposureHazardStaffNum"
									type="number"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-5" v-if="currentView==5">
						<view class="title">委托代理人信息</view>
						<u--form labelWidth="auto">
							<u-form-item label="是否有委托代理人">
								<u-radio-group v-model="formData.hasAgent" placement="row">
									<u-radio label="是" :name="true" />
									<u-radio label="否" :name="false" />
								</u-radio-group>
							</u-form-item>
							<u-form-item label="代理人姓名" v-show="formData.hasAgent">
								<u--input placeholder="请输入代理人姓名" v-model="formData.workerAgent.agentName"></u--input>
							</u-form-item>
							<u-form-item label="与当事人关系" v-show="formData.hasAgent">
								<u--input placeholder="请输入与当事人关系"
									v-model="formData.workerAgent.relationship"></u--input>
							</u-form-item>
							<u-form-item label="代理人身份证号码" v-show="formData.hasAgent">
								<u--input placeholder="请输入代理人身份证号码"
									v-model="formData.workerAgent.agentIdCardCode"></u--input>
							</u-form-item>
							<u-form-item label="代理人联系电话" v-show="formData.hasAgent">
								<u--input placeholder="请输入代理人联系电话"
									v-model="formData.workerAgent.agentContactPhone"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-6" v-if="currentView==6">
						<view class="title">请在提交的资料后打 "✓"</view>
						<view class="application_table">
							<view class="form-content">
								<view class="form-item">
									<view class="item-label">（一）职业病诊断证明书;</view>
									<view class="checkbox-container">
										<u-checkbox v-model="formData.applicationFileRecord.hasDiagnosisCertificate"
											:checked="formData.applicationFileRecord.hasDiagnosisCertificate"
											@change="formData.applicationFileRecord.hasDiagnosisCertificate = !formData.applicationFileRecord.hasDiagnosisCertificate,certificateList=[]" />
									</view>
								</view>
								<view class="form-item">
									<view class="item-label">（二）首次职业病诊断鉴定书;</view>
									<view class="checkbox-container">
										<u-checkbox
											v-model="formData.applicationFileRecord.hasFirstIdentificationReport"
											:checked="formData.applicationFileRecord.hasFirstIdentificationReport"
											@change="formData.applicationFileRecord.hasFirstIdentificationReport = !formData.applicationFileRecord.hasFirstIdentificationReport,identificationReportList=[]" />
									</view>
								</view>
								<view class="form-item">
									<view class="item-label">（三）其他有关资料</view>
								</view>
								<view class="sub-items">
									<view class="form-item">
										<view class="item-label">1.
											劳动者职业史和职业病危害接触史（包括在岗时间、工种、岗位、接触的职业病危害因素名称等）;</view>
										<view class="checkbox-container">
											<u-checkbox :v-model="formData.applicationFileRecord.hasOtherJobHistory"
												:checked="formData.applicationFileRecord.hasOtherJobHistory"
												@change="formData.applicationFileRecord.hasOtherJobHistory = !formData.applicationFileRecord.hasOtherJobHistory,otherJobHistoryList=[]" />
										</view>
									</view>
									<view class="form-item">
										<view class="item-label">2. 劳动者职业健康检查结果;</view>
										<view class="checkbox-container">
											<u-checkbox
												v-model="formData.applicationFileRecord.hasOtherExaminationReport"
												:checked="formData.applicationFileRecord.hasOtherExaminationReport"
												@change="formData.applicationFileRecord.hasOtherExaminationReport = !formData.applicationFileRecord.hasOtherExaminationReport,examinationReportList=[]" />
										</view>
									</view>
									<view class="form-item">
										<view class="item-label">3. 工作场所职业病危害因素检测结果;</view>
										<view class="checkbox-container">
											<u-checkbox v-model="formData.applicationFileRecord.hasOtherDetectionReport"
												:checked="formData.applicationFileRecord.hasOtherDetectionReport"
												@change="formData.applicationFileRecord.hasOtherDetectionReport = !formData.applicationFileRecord.hasOtherDetectionReport,detectionReportList=[]" />
										</view>
									</view>
									<view class="form-item">
										<view class="item-label">4. 个人剂量监测档案（限于接触职业性放射性危害的劳动者）;</view>
										<view class="checkbox-container">
											<u-checkbox
												v-model="formData.applicationFileRecord.hasOtherPersonalDoseRecord"
												:checked="formData.applicationFileRecord.hasOtherPersonalDoseRecord"
												@change="formData.applicationFileRecord.hasOtherPersonalDoseRecord = !formData.applicationFileRecord.hasOtherPersonalDoseRecord ,doseRecordtList=[]" />
										</view>
									</view>
									<view class="form-item">
										<view class="item-label">5. 劳动者身份证复印件;</view>
										<view class="checkbox-container">
											<u-checkbox v-model="formData.applicationFileRecord.hasOtherPersonalIdCard"
												:checked="formData.applicationFileRecord.hasOtherPersonalIdCard"
												@change="formData.applicationFileRecord.hasOtherPersonalIdCard = !formData.applicationFileRecord.hasOtherPersonalIdCard ,personalIdCardList=[]" />
										</view>
									</view>
									<view class="form-item">
										<view class="item-label">6. 授权委托书及代理人身份证复印件;</view>
										<view class="checkbox-container">
											<u-checkbox v-model="formData.applicationFileRecord.hasOtherDepute"
												:checked="formData.applicationFileRecord.hasOtherDepute"
												@change="formData.applicationFileRecord.hasOtherDepute = !formData.applicationFileRecord.hasOtherDepute ,deputeList=[]" />
										</view>
									</view>
									<view class="form-item">
										<view class="item-label">7. 与鉴定有关的其他资料.</view>
										<view class="checkbox-container">
											<u-checkbox v-model="formData.applicationFileRecord.hasOther"
												:checked="formData.applicationFileRecord.hasOther"
												@change="formData.applicationFileRecord.hasOther = !formData.applicationFileRecord.hasOther ,otherList=[]" />
										</view>
									</view>
								</view>
							</view>
							<view class="form-note">
								上述第（三）项资料，当事人如以职业病诊断或首次职业病鉴定时提供的资料为准，可以不再提供请在备注中说明。
							</view>
							<view class="form-remark">
								<view class="remark-label">备注:</view>
								<u--input v-model="formData.applicationFileRecord.remark" type="textarea" :rows="2"
									placeholder="请在此处填写备注信息" />
							</view>
						</view>
						<u--form labelWidth="auto" labelPosition="top">
							<u-form-item label="上传职业病诊断证明书"
								v-show="formData.applicationFileRecord.hasDiagnosisCertificate">
								<UploadFile :fileList="certificateList" name="1" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadDiagnosisCertificate'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传首次职业病诊断鉴定书"
								v-show="formData.applicationFileRecord.hasFirstIdentificationReport">
								<UploadFile :fileList="identificationReportList" name="2" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadFirstIdentificationReport'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传劳动者职业史和职业病危害接触史"
								v-show="formData.applicationFileRecord.hasOtherJobHistory">
								<UploadFile :fileList="otherJobHistoryList" name="3" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadJobHistory'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传劳动者职业健康检查结果"
								v-show="formData.applicationFileRecord.hasOtherExaminationReport">
								<UploadFile :fileList="examinationReportList" name="4" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadExaminationReport'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传工作场所职业病危害因素检测结果"
								v-show="formData.applicationFileRecord.hasOtherDetectionReport">
								<UploadFile :fileList="detectionReportList" name="5" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadDetectionReport'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传个人剂量监测档案"
								v-show="formData.applicationFileRecord.hasOtherPersonalDoseRecord">
								<UploadFile :fileList="doseRecordtList" name="6" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadPersonalDoseRecord'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传劳动者身份证复印件"
								v-show="formData.applicationFileRecord.hasOtherPersonalIdCard">
								<UploadFile :fileList="personalIdCardList" name="7" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadIdCard'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传授权委托书及代理人身份证复印件"
								v-show="formData.applicationFileRecord.hasOtherDepute">
								<UploadFile :fileList="deputeList" name="8" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadDepute'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
							<u-form-item label="上传与鉴定有关的其他资料" v-show="formData.applicationFileRecord.hasOther">
								<UploadFile :fileList="otherList" name="9" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadOtherFile'"
									:diagnosisId="diagnosisId" @afterRead="afterReadIdCard" @delete="deleteFile">
								</UploadFile>
							</u-form-item>
						</u--form>
					</view>
					<view class="form" id="section-6" v-if="currentView==7">
						<u-form labelWidth="auto" labelPosition="top">
							<u-form-item label="《职业病鉴定申请书》">
								<u-button type="primary" size="mini" @click="downloadReport">
									下载
								</u-button>
							</u-form-item>
							<u-form-item label="上传职业病鉴定申请书">
								<UploadFile :fileList="applyList" name="10" :maxCount="2"
									:uploadUrl="config.apiServer + 'app/identify/uploadApplication'"
									:diagnosisId="diagnosisId || userId" @afterRead="afterReadIdCard"
									@delete="deleteFile">
								</UploadFile>
							</u-form-item>
						</u-form>
					</view>
				</view>
				<view class="u-tabbar">
					<text class="btn close" @click="handleCancel">取消</text>
					<text class="btn sync" v-if="currentView==4" @click="handleSync">一键同步</text>
					<text class="btn prev" @click="handlePrev" v-if="currentView>1 && currentView < 7">上一步</text>
					<text class="btn next" @click="handleNext" v-if="currentView!=7">下一步</text>
					<!-- <text class="btn next" v-if="currentView==7" @click="handleStore">暂存</text> -->
					<text class="btn next" v-if="currentView==7" @click="handleSave">提交申请</text>
				</view>
				<!-- 再次鉴定弹窗 -->
				<uni-popup ref="appealPopup" type="center">
					<view class="appeal-popup">
						<!-- 添加关闭按钮 -->
						<view class="popup-close" @click="handleTypeClose">
							<uni-icons type="closeempty" size="24" color="#999"></uni-icons>
						</view>
						<view class="popup-title">{{addType == 1?'鉴定记录列表':'诊断记录列表'}}</view>
						<view class="popup-content">
							<u-empty v-if="appealList.length == 0" mode="data" icon="/static/empty.png" text="暂无鉴定记录">
							</u-empty>
							<u-list height="800rpx" v-else>
								<u-list-item v-for="(item, index) in appealList" :key="index">
									<view class="diagnosis-item">
										<view class="item-content">
											<view class="info-row">
												<text class="label">劳动者姓名：</text>
												<text class="value">{{item.workerName}}</text>
											</view>
											<view class="info-row">
												<text class="label">用人单位名称：</text>
												<text class="value">{{item.empName}}</text>
											</view>
											<view class="info-row">
												<text class="label">用人单位统一社会信用代码：</text>
												<text class="value">{{item.empCreditCode}}</text>
											</view>
											<view class="info-row">
												<text class="label">用工单位名称：</text>
												<text class="value">{{item.workEmpName}}</text>
											</view>
											<view class="info-row">
												<text class="label">用工单位统一社会信用代码：</text>
												<text class="value">{{item.workEmpCreditCode}}</text>
											</view>
										</view>
										<view class="item-footer">
											<u-button type="primary" size="mini" @click="handleChoose(item)">
												选择
											</u-button>
										</view>
									</view>
								</u-list-item>
							</u-list>
						</view>
					</view>
				</uni-popup>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import identifyApi from '@/api/identify.js'
	import UploadFile from '../../../components/uploadFile.vue';
	import config from '@/common.js';
	export default {
		components: {
			UploadFile,
		},
		data() {
			return {
				config: config,
				currentView: 1,
				formData: {
					diagnosisId: '', //首次鉴定
					firstIdentificationId: '', //再次鉴定
					applicant: 1,
					workerName: '',
					workerGender: '',
					workerBirthday: '',
					workerContactPhone: '',
					workerAddress: '',
					workerIdCardType: '',
					workerIdCardCode: '',
					workerZipCode: '',
					workerMailAddress: '',
					workerRegisteredResidenceAreaCode: '',
					workerRegisteredResidenceAddress: '',
					workerUsualAreaCode: '',
					workerUsualAddress: '',
					workerPastMedicalHistory: '',
					applicationDate: new Date().toISOString().split('T')[0],
					applicationReason: '',
					type: null,
					jobHistoryList: [],
					workerAgent: {
						agentName: '',
						agentIdCardCode: '',
						agentContactPhone:'',
						relationship: '',
					},
					empName: '',
					empCreditCode: '',
					empAreaCode: '',
					empAddress: '',
					empContactPerson: '',
					empContactPhone: '',
					empZipCode: '',
					empIndustryCode: '',
					empEconomicTypeCode: '',
					empEnterpriseScaleCode: '',
					empEstablishmentDate: '',
					empTotalStaffNum: '',
					empProductionWorkerNum: '',
					empExternalStaffNum: '',
					empExposureHazardStaffNum: '',
					workEmpName: '',
					workEmpCreditCode: '',
					workEmpAreaCode: '',
					workEmpAddress: '',
					workEmpContactPerson: '',
					workEmpContactPhone: '',
					workEmpZipCode: '',
					workEmpIndustryCode: '',
					workEmpEconomicTypeCode: '',
					workEmpEnterpriseScaleCode: '',
					workEmpEstablishmentDate: '',
					workEmpTotalStaffNum: '',
					workEmpProductionWorkerNum: '',
					workEmpExternalStaffNum: '',
					workEmpExposureHazardStaffNum: '',
					hasAgent: false,
					fileList: [],
					applicationFileRecord: {
						hasDiagnosisCertificate: false,
						hasFirstIdentificationReport: false,
						hasOtherJobHistory: false,
						hasOtherExaminationReport: false,
						hasOtherDetectionReport: false,
						hasOtherPersonalDoseRecord: false,
						hasOtherPersonalIdCard: false,
						hasOtherDepute: false,
						hasOther: false,
						remark: ""
					},
				},
				IDCardShow: false,
				AreaCodeShow: false,
				ygrShow: false,
				certificateList: [],
				identificationReportList: [],
				otherJobHistoryList: [],
				examinationReportList: [],
				detectionReportList: [],
				doseRecordtList: [],
				personalIdCardList: [],
				deputeList: [],
				otherList: [],
				establishmentDateShow: false,
				hazardItem: {},
				hyShow: false,
				jjShow: false,
				qyShow: false,
				hyShow2: false,
				jjShow2: false,
				qyShow2: false,
				userId: '', //获取详情时的userId
				diagnosisId: '', //暂存后上传职业病鉴定申请书需要的id
				applyList: [],
				appealList: [],
        addType:'',//  0：首次鉴定，1：再次鉴定
        institutionId:'',// 首次鉴定申请的id
			}
		},
		mounted() {
			this.formData.workerName = this.userInfo?.name || ''
			this.formData.workerIdCardCode = this.userInfo?.idNo || ''
			this.formData.workerContactPhone = this.userInfo?.phoneNum || ''
		},
		// 
		onLoad(options) {
      if(options.type == 2){
        this.formData.type = 1
        this.addType = 1
      }else{
        this.formData.type = 0
        this.addType = 0
       }
			this.formData.institutionId = options.institutionId
      this.institutionId = options.institutionId
			this.userId = options.userId
			if (this.userId) {
				this.getDetail({
					userId: this.userId
				})
			}
			this.formData.diagnosisId = options.diagnosisId
			this.formData.firstIdentificationId = options.firstIdentificationId
			// 如果诊断记录选择首次鉴定
			if (this.formData.institutionId && this.formData.diagnosisId) {
				this.getDetailByDiagnosisId({
					diagnosisId: this.formData.diagnosisId
				})
			}
			// 如果鉴定记录选择兵团级鉴定
			if (this.formData.institutionId && this.formData.firstIdentificationId) {
        this.getDetail({
					userId: this.formData.firstIdentificationId
				})
			}
		},
		async created() {
			await this.getAreaList()
			await this.getDiseasesList()
			await this.getPersonGender()
			await this.getIdcardType()
			await this.getHazard()

		},
		computed: {
			...mapGetters(["areaList", "diseasesList", "personGender", "idcardType", "hazard", "userInfo"]),
			areaList2() {
				return this.processAreaData(this.areaList)
			},
			idcardType2() {
				return this.idcardType && this.idcardType.map(item => ({
					text: item.dictLabel,
					value: item.dictCode
				}))
			},
			hazard2() {
				return this.hazard && this.hazard.map(item => ({
					text: item.dictLabel,
					value: item.dictCode
				}))
			},
			diseasesList2() {
				return this.processAreaData(this.diseasesList)
			}
		},
		methods: {
			...mapActions(['getAreaList', 'getDiseasesList', 'getPersonGender', 'getIdcardType', 'getHazard']),
			// 获取鉴定详情
			async getDetail(userId) {
				const res = await identifyApi.getIdentificationDetail(userId)
				this.formData = {
					...res.data
				}

				if (this.formData.empTotalStaffNum === 0) {
					this.formData.empTotalStaffNum = '0'
				}
				if (this.formData.empExternalStaffNum === 0) {
					this.formData.empExternalStaffNum = '0'
				}
				if (this.formData.empProductionWorkerNum === 0) {
					this.formData.empProductionWorkerNum = '0'
				}
				if (this.formData.empExposureHazardStaffNum === 0) {
					this.formData.empExposureHazardStaffNum = '0'
				}
				if (this.formData.workEmpTotalStaffNum === 0) {
					this.formData.workEmpTotalStaffNum = '0'
				}
				if (this.formData.workEmpProductionWorkerNum === 0) {
					this.formData.workEmpProductionWorkerNum = '0'
				}
				if (this.formData.workEmpExternalStaffNum === 0) {
					this.formData.workEmpExternalStaffNum = '0'
				}
				if (this.formData.workEmpExposureHazardStaffNum === 0) {
					this.formData.workEmpExposureHazardStaffNum = '0'
				}

        
        if (!this.userId) {
          this.formData.institutionId =  this.institutionId
          this.formData.id = ''
          this.formData.firstIdentificationId = res.data.id
          this.formData.type = this.addType
          // 是否覆盖
          if (this.formData.applicationDate) {
            this.formData.applicationDate = new Date().toISOString().split('T')[0]
          }
          if (this.formData.hasAgent) {
            this.formData.hasAgent = false
          }
          if (this.formData.applicationReason) {
            this.formData.applicationReason = ''
          }
        }

				if (!this.formData.applicationFileRecord) {
					this.formData.applicationFileRecord = {
						hasDiagnosisCertificate: false,
						hasFirstIdentificationReport: false,
						hasOtherJobHistory: false,
						hasOtherExaminationReport: false,
						hasOtherDetectionReport: false,
						hasOtherPersonalDoseRecord: false,
						hasOtherPersonalIdCard: false,
						hasOtherDepute: false,
						hasOther: false,
						remark: ''
					}
				}

				this.certificateList = res.data.fileList.diagnosisCertificate ?
					res.data.fileList.diagnosisCertificate : []
				this.identificationReportList = res.data.fileList.firstIdentificationReport ?
					res.data.fileList.firstIdentificationReport : []
				this.otherJobHistoryList = res.data.fileList.jobHistory ? res.data.fileList.jobHistory : []
				this.examinationReportList = res.data.fileList.examinationReport ?
					res.data.fileList.examinationReport : []
				this.detectionReportList = res.data.fileList.detectionReport ?
					res.data.fileList.detectionReport : []
				this.doseRecordtList = res.data.fileList.personalDoseRecord ?
					res.data.fileList.personalDoseRecord : []
				this.personalIdCardList = res.data.fileList.idCard ? res.data.fileList.idCard : []
				this.deputeList = res.data.fileList.depute ? res.data.fileList.depute : []
				this.otherList = res.data.fileList.other ? res.data.fileList.other : []
				if ((!this.formData.workerAgent) || this.formData.workerAgent == null) {
					this.formData.workerAgent = {
						agentName: '',
						agentIdCardCode: '',
						agentContactPhone:'',
						relationship: '',
					}
				}
				this.getIdeApplicationWord(userId)
			},
			// 获取鉴定申请书
			async getIdeApplicationWord(id) {
				const res = await identifyApi.getIdeApplication(id)
				this.applyList = res.data.length ? res.data.map(item => {
					return {
						id: item.id,
						url: item.fileUrl,
						name: item.fileName,
					}
				}) : []
				this.getIdeApplicationWord(id)
			},
			// 获取鉴定申请书
			async getIdeApplicationWord(id) {
				const res = await identifyApi.getIdeApplication(id)
				this.applyList = res.data.length ? res.data.map(item => {
					return {
						id: item.id,
						url: item.fileUrl,
						name: item.fileName,
					}
				}) : []
			},
			processAreaData(data) {
				if (!data) return [];
				return data.map(item => ({
					text: item.dictLabel,
					value: item.dictCode,
					children: item.children ? this.processAreaData(item.children) : null
				}));
			},
			onAddressChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			onUsualAddressChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			onEmpAreaChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			onWorkAreaCodeChange(e) {
				if (e.detail && e.detail.value) {
					const selectedValue = e.detail.value;
					console.log(selectedValue);
				}
			},
			handlePrev() {
				this.currentView--
			},
			validateFormData1() {
				const requiredFields = [{
						field: 'workerName',
						message: '请填写姓名'
					},
					{
						field: 'workerGender',
						message: '请选择性别'
					},
					{
						field: 'workerBirthday',
						message: '请选择出生日期'
					},
					{
						field: 'workerContactPhone',
						message: '请填写联系电话'
					},
					{
						field: 'workerAddress',
						message: '请填写住址'
					},
					{
						field: 'workerIdCardType',
						message: '请选择证件类别'
					},
					{
						field: 'workerIdCardCode',
						message: '请填写证件号码'
					},
					{
						field: 'workerZipCode',
						message: '请填写邮政编码'
					},
					{
						field: 'workerMailAddress',
						message: '请填写通讯地址'
					},
					{
						field: 'workerPastMedicalHistory',
						message: '请填写既往病史'
					},
					{
						field: 'workerRegisteredResidenceAreaCode',
						message: '请选择户籍所在地'
					},
					{
						field: 'workerRegisteredResidenceAddress',
						message: '请填写户籍详细地址'
					},
					{
						field: 'workerUsualAreaCode',
						message: '请选择经常居住地'
					},
					{
						field: 'workerUsualAddress',
						message: '请填写居住地详细地址'
					},
					{
						field: 'applicationReason',
						message: '请填写申请理由'
					}
				];

				const formatChecks = [{
						field: 'workerContactPhone',
						regex: /^\d{11}$/,
						message: '联系电话必须是11位'
					},
					{
						field: 'workerZipCode',
						regex: /^\d{6}$/,
						message: '邮政编码必须是6位'
					},
					{
						field: 'workerIdCardCode',
						regex: /^\d{17}[\dXx]$/,
						message: '证件号码必须是18位'
					}
				];

				// 检查必填字段
				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				return true;
			},
			validateFormData2() {
				if (this.formData.jobHistoryList.length == 0) {
					uni.showToast({
						title: '职业史列表不能为空，请至少添加一条职业史记录',
						icon: 'none'
					});
					return false;
				}

				return true;
			},
			validateFormData3() {
				const requiredFields = [{
						field: 'empName',
						message: '请填写用人单位名称'
					},
					{
						field: 'empCreditCode',
						message: '请填写统一社会信用代码'
					},
					{
						field: 'empAreaCode',
						message: '请选择所在地区'
					},
					{
						field: 'empAddress',
						message: '请填写地址'
					},
					{
						field: 'empContactPerson',
						message: '请填写联系人'
					},
					{
						field: 'empContactPhone',
						message: '请填写联系方式'
					},
					{
						field: 'empZipCode',
						message: '请填写邮编'
					},
					{
						field: 'empIndustryCode',
						message: '请选择行业'
					},
					{
						field: 'empEconomicTypeCode',
						message: '请选择经济类型'
					},
					{
						field: 'empEnterpriseScaleCode',
						message: '请选择企业规模'
					},
					{
						field: 'empEstablishmentDate',
						message: '请选择单位成立时间'
					},
					{
						field: 'empTotalStaffNum',
						message: '请输入职工总人数'
					},
					{
						field: 'empProductionWorkerNum',
						message: '请输入生产工人总数'
					},
					{
						field: 'empExternalStaffNum',
						message: '请输入外委人员数'
					},
					{
						field: 'empExposureHazardStaffNum',
						message: '请输入接触有毒有害作业人数'
					}
				];

				const formatChecks = [{
						field: 'empCreditCode',
						regex: /^[A-Za-z0-9]{18}$/,
						message: '统一社会信用代码必须是18位'
					}, {
						field: 'empContactPhone',
						regex: /^\d{11}$/,
						message: '联系方式必须是11位'
					},
					{
						field: 'empZipCode',
						regex: /^\d{6}$/,
						message: '邮政编码必须是6位'
					}
				];


				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}
				return true;
			},
			validateFormData4() {
				const requiredFields = [{
						field: 'workEmpName',
						message: '请填写用工单位名称'
					},
					{
						field: 'workEmpCreditCode',
						message: '请填写统一社会信用代码'
					},
					{
						field: 'workEmpAreaCode',
						message: '请选择所在地区'
					},
					{
						field: 'workEmpAddress',
						message: '请填写地址'
					},
					{
						field: 'workEmpContactPerson',
						message: '请填写联系人'
					},
					{
						field: 'workEmpContactPhone',
						message: '请填写联系方式'
					},
					{
						field: 'workEmpZipCode',
						message: '请填写邮编'
					},
					{
						field: 'workEmpIndustryCode',
						message: '请选择行业'
					},
					{
						field: 'workEmpEconomicTypeCode',
						message: '请选择经济类型'
					},
					{
						field: 'workEmpEnterpriseScaleCode',
						message: '请选择企业规模'
					},
					{
						field: 'workEmpEstablishmentDate',
						message: '请选择单位成立时间'
					},
					{
						field: 'workEmpTotalStaffNum',
						message: '请输入职工总人数'
					},
					{
						field: 'workEmpProductionWorkerNum',
						message: '请输入生产工人总数'
					},
					{
						field: 'workEmpExternalStaffNum',
						message: '请输入外委人员数'
					},
					{
						field: 'workEmpExposureHazardStaffNum',
						message: '请输入接触有毒有害作业人数'
					}
				];


				const formatChecks = [{
						field: 'workEmpCreditCode',
						regex: /^[A-Za-z0-9]{18}$/,
						message: '统一社会信用代码必须是18位'
					}, {
						field: 'workEmpContactPhone',
						regex: /^\d{11}$/,
						message: '联系方式必须是11位'
					},
					{
						field: 'workEmpZipCode',
						regex: /^\d{6}$/,
						message: '邮政编码必须是6位'
					}

				];

				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}


				return true;
			},
			validateFormData5() {
				const requiredFields = [{
						field: 'agentName',
						message: '请填写代理人姓名'
					},
					{
						field: 'relationship',
						message: '请填写与当事人关系'
					},
					{
						field: 'agentIdCardCode',
						message: '请填写代理人身份证号码'
					},
					{
						field: 'agentContactPhone',
						message: '请填写代理人联系电话'
					}
				];


				const formatChecks = [{
						field: 'agentIdCardCode',
						regex: /^\d{17}[\dXx]$/,
						message: '统一社会信用代码必须是18位'
					}, {
						field: 'agentContactPhone',
						regex: /^\d{11}$/,
						message: '联系方式必须是11位'
					}

				];
				for (const {
						field,
						message
					}
					of requiredFields) {
					if (!this.formData.workerAgent[field]) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}

				// 检查格式
				for (const {
						field,
						regex,
						message
					}
					of formatChecks) {
					if (!regex.test(this.formData.workerAgent[field])) {
						uni.showToast({
							title: message,
							icon: 'none'
						});
						return false;
					}
				}
				return true;
			},
			handleNext() {
				let isValid = true;
				switch (this.currentView) {
					case 1:
						isValid = this.validateFormData1();
						break;
					case 2:
						isValid = this.validateFormData2();
						break;
					case 3:
						isValid = this.validateFormData3();
						break;
					case 4:
						isValid = this.validateFormData4();
						break;
					case 5:
						isValid = !this.formData.hasAgent || this.validateFormData5();
						break;
					case 6:
						if (this.identificationReportList.length === 0 && this.formData.type === 1) {
							uni.showToast({
								title: '首次职业病诊断鉴定书是必填项，请上传相关文件',
								icon: 'none'
							});
							return false;
						}
						if (this.identificationReportList.length === 0 && this.formData.type === 1) {
							uni.showToast({
								title: '首次职业病诊断鉴定书是必填项，请上传相关文件',
								icon: 'none'
							});
							return false;
						}
						return this.handleStore()
							.then(() => {
								this.currentView++
							})
							.catch(err => {
								uni.showToast({
									title: '暂存数据失败，请稍后再试',
									icon: 'none'
								});
							});
					case 7:
						return;
					default:
						return;
				}

				if (!isValid) return false;

				this.currentView++;
				this.$nextTick(() => {
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 0
					});
				});
			},
			handleSync() {
				this.formData.workEmpName = this.formData.empName
				this.formData.workEmpCreditCode = this.formData.empCreditCode
				this.formData.workEmpAreaCode = this.formData.empAreaCode
				this.formData.workEmpAddress = this.formData.empAddress
				this.formData.workEmpContactPerson = this.formData.empContactPerson
				this.formData.workEmpContactPhone = this.formData.empContactPhone
				this.formData.workEmpZipCode = this.formData.empZipCode
				this.formData.workEmpIndustryCode = this.formData.empIndustryCode
				this.formData.workEmpEconomicTypeCode = this.formData.empEconomicTypeCode
				this.formData.workEmpEnterpriseScaleCode = this.formData.empEnterpriseScaleCode
				this.formData.workEmpEstablishmentDate = this.formData.empEstablishmentDate
				this.formData.workEmpTotalStaffNum = this.formData.empTotalStaffNum
				this.formData.workEmpProductionWorkerNum = this.formData.empProductionWorkerNum
				this.formData.workEmpExternalStaffNum = this.formData.empExternalStaffNum
				this.formData.workEmpExposureHazardStaffNum = this.formData.empExposureHazardStaffNum
			},
			async handleStore() {
				this.formData.fileList = []
				this.formData.fileList = [
					...this.certificateList,
					...this.identificationReportList,
					...this.otherJobHistoryList,
					...this.examinationReportList,
					...this.detectionReportList,
					...this.doseRecordtList,
					...this.personalIdCardList,
					...this.deputeList,
					...this.otherList
				]

				let params = {
					...this.formData,
					// workerIdCardType: this.idcardType?.find(item => item.dictLabel === this.formData
					// 	.workerIdCardType)?.dictCode,
				};
				params.jobHistoryList?.forEach(item => {
					item.jobHistoryHazardList?.forEach(ele => {
						const hazard = this.hazard?.find(val => val.dictLabel === ele.hazardCode);
						if (hazard) ele.hazardCode = hazard.dictCode;
					});
				});
				try {
					// 提交表单
					const res = params.id ? await identifyApi.updateIdentification(params) : await identifyApi
						.addIdentification(
							params)
					if (res.data.success) {
						this.diagnosisId = res.data.data?.id || this.formData.id;
						uni.showToast({
							title: "暂存成功",
							icon: 'success',
							duration: 1200
						})
						return true
					} else {
						uni.showToast({
							title: res.data.msg || "暂存失败",
							icon: 'error',
							duration: 1200
						})
						return false
						return false
					}
				} catch (error) {
					uni.showToast({
						title: error.message || "暂存失败",
						icon: 'none',
						duration: 1200
					})
					return false
					return false
				}
			},
			handleSave() {
				if (this.applyList.length === 0) {
					uni.showToast({
						title: '职业病鉴定申请书是必填项，请上传相关文件',
						icon: 'none'
					});
					return false;
				}
				uni.showModal({
					title: '提示',
					content: '提交申请后数据不能修改,您确定要提交吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								// 提交表单
								const res = await identifyApi.submitIdentify({
									id: this.formData.id || this.diagnosisId
								})
								if (res.data.success) {
									uni.showToast({
										title: "提交成功",
										icon: 'success',
										duration: 1200
									})
									setTimeout(() => {
										uni.navigateBack()
									}, 1200)
								} else {
									uni.showToast({
										title: res.data.msg || "提交失败",
										icon: 'none'
									})
								}
							} catch (error) {
								uni.showToast({
									title: error.message || "提交失败",
									icon: 'none'
								})
							}
						}
					}
				})
			},
			handleCancel() {
				uni.showModal({
					title: '提示',
					content: '数据还未保存，您确定要取消并返回吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack()
						}
					}
				})
			},
			handleSectionIDCard(e) {
				this.formData.workerIdCardType = e.value[0].text
				this.IDCardShow = false
			},
			handleSectionzyb(e) {
				console.log(e, 'e');
			},
			handleYgrShow(row, ele, e, index) {
				// ele.hazardCode = e.value[0].text
				// this.ygrShow = false
				if (row.jobHistoryHazardList.length > 1) {
					const hasDuplicate = row.jobHistoryHazardList.some(
						(item, i) => i !== index && item.hazardCode === ele.hazardCode
					)
					if (hasDuplicate) {
						uni.showToast({
							title: '危害因素编码重复',
							icon: 'none'
						});
						row.jobHistoryHazardList.splice(index, 1)
					}
				}
			},

			// 删除接触史
			async handleDeleteHazard(job, hz, hzIdx) {
				if (!hz.id) {
					job.jobHistoryHazardList.splice(hzIdx, 1)
					return
				}
				uni.showModal({
					title: '提示',
					content: '您确定要删除该接触史吗？',
					success: async (res) => {
						if (res.confirm) {
							const res = await identifyApi.deleteIdentifyJobHistoryHazard({
								id: hz.id
							})
							if (res.data.success) {
								job.jobHistoryHazardList.splice(hzIdx, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: res.data.msg || "删除失败",
									icon: 'error',
									duration: 1200
								})
							}
						}
					}
				})
			},

			// 保存职业史
			async handleSaveJob(row) {
				let params = {
					id: row.id ? row.id : '',
					identificationId: this.formData.id ? this.formData.id : '',
					empName: row.empName,
					job: row.job,
					post: row.post,
					operationProcess: row.operationProcess,
					protectiveMeasure: row.protectiveMeasure,
					personalProtection: row.personalProtection,
					jobHistoryHazardList: row.jobHistoryHazardList
				}
				const res = await identifyApi.saveIdentifyJobHistory(params)
				if (res.data) {
					uni.showToast({
						title: "保存成功",
						icon: 'success',
						duration: 1200
					})
				}
			},

			// 删除职业史
			async handleDeleteJob(item, index) {
				if (!item.id) {
					this.formData.jobHistoryList.splice(index, 1)
					return
				}
				uni.showModal({
					title: '提示',
					content: '您确定要删除该职业史吗？',
					success: async (res) => {
						if (res.confirm) {
							const res = await identifyApi.deleteIdentifyJobHistory({
								id: item.id
							})
							if (res.data.success) {
								this.formData.jobHistoryList.splice(index, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: res.data.msg || "删除失败",
									icon: 'error',
									duration: 1200
								})
							}
						}
					}
				})

			},
			handleSectionIndustry(e) {
				this.formData.empIndustryCode = e.value[0]
				this.hyShow = false
			},
			handleSectionEconomic(e) {
				this.formData.empEconomicTypeCode = e.value[0]
				this.jjShow = false
			},
			handleSectionEnterprise(e) {
				this.formData.empEnterpriseScaleCode = e.value[0]
				this.qyShow = false
			},

			handleSectionIndustry2(e) {
				this.formData.workEmpIndustryCode = e.value[0]
				this.hyShow2 = false
			},
			handleSectionEconomic2(e) {
				this.formData.workEmpEconomicTypeCode = e.value[0]
				this.jjShow2 = false
			},
			handleSectionEnterprise2(e) {
				this.formData.workEmpEnterpriseScaleCode = e.value[0]
				this.qyShow2 = false
			},
			handleAddBli() {
				this.formData.jobHistoryList.push({
					id: '',
					empName: '',
					job: '',
					post: '',
					operationProcess: '',
					protectiveMeasure: '',
					personalProtection: '',
					jobHistoryHazardList: [{
						hazardCode: '',
						concentration: '',
						contactTime: '',
						startContactDate: '',
						endContactDate: ''
					}]
				})
			},
			handleAddItem(item) {
				item.jobHistoryHazardList.push({
					hazardCode: '',
					concentration: '',
					contactTime: '',
					startContactDate: '',
					endContactDate: ''
				})
			},
			handleAddDis() {
				this.formData.diseaseList.push({
					diseaseCode: '',
					otherDiseaseName: ''
				})
			},
			// 删除图片
			deleteFile(event) {
				console.log('event', event);
				this[`fileList${event.name}`].splice(event.index, 1);
			},
			// 下载职业病鉴定申请书
			async downloadReport() {
				try {
					const res = await uni.downloadFile({
						url: `${config.apiServer}app/identify/downloadApplication?identificationId=${this.diagnosisId}`
					});
					if (res && res[1] && res[1].statusCode === 200) {
						let filename = '职业病鉴定申请书.docx';
						const link = document.createElement('a');
						link.href = res[1].tempFilePath;
						link.download = filename;
						link.click();
					} else {
						uni.showToast({
							title: res[1].errMsg,
							icon: 'none'
						});
					}
				} catch (error) {
					uni.showToast({
						title: error.message,
						icon: 'none'
					});
				}
			},
			// 鉴定类型切换的时候获取鉴定列表
			async handleTypeOpen() {
				this.$refs.appealPopup.open();
        this.addType = this.formData.type
        console.log(this.addType,'this.addType------->');
        
				this.getJDList()
			},
			// 获取可再鉴定鉴定列表/可鉴定诊断列表
			async getJDList() {
				let params = {
					pageNum: 1,
					pageSize: 1000,
					idCardCode: this.userInfo?.idNo || '',
					institutionId: this.formData.institutionId || ''
				}
				const res = this.formData.type == 1 ? await identifyApi.getCanAgainDiagnosis(params) :
					await identifyApi.getCanIdentificationList(params)
				if (res.data.success) {
					this.appealList = res.data.data?.list || []
				}
			},
			// 获取可鉴定诊断详情信息
			async getDetailByDiagnosisId(diagnosisId) {
				const res = await identifyApi.getCanIdentificationDetail(diagnosisId)
				if (res.data.success) {
					this.formData = {
						...res.data.data
					} || {}

					if (this.formData.empTotalStaffNum === 0) {
						this.formData.empTotalStaffNum = '0'
					}
					if (this.formData.empExternalStaffNum === 0) {
						this.formData.empExternalStaffNum = '0'
					}
					if (this.formData.empProductionWorkerNum === 0) {
						this.formData.empProductionWorkerNum = '0'
					}
					if (this.formData.empExposureHazardStaffNum === 0) {
						this.formData.empExposureHazardStaffNum = '0'
					}
					if (this.formData.workEmpTotalStaffNum === 0) {
						this.formData.workEmpTotalStaffNum = '0'
					}
					if (this.formData.workEmpProductionWorkerNum === 0) {
						this.formData.workEmpProductionWorkerNum = '0'
					}
					if (this.formData.workEmpExternalStaffNum === 0) {
						this.formData.workEmpExternalStaffNum = '0'
					}
					if (this.formData.workEmpExposureHazardStaffNum === 0) {
						this.formData.workEmpExposureHazardStaffNum = '0'
					}

          if (!this.userId) {
            this.formData.institutionId =  this.institutionId
            this.formData.id = ''
            this.formData.diagnosisId = res.data.data.id
            this.formData.type = this.addType
            // 是否覆盖
            if (this.formData.applicationDate) {
              this.formData.applicationDate = new Date().toISOString().split('T')[0]
            }
            if (this.formData.hasAgent) {
              this.formData.hasAgent = false
            }
            if (this.formData.applicationReason) {
              this.formData.applicationReason = ''
            }
          }

					if (!this.formData.applicationFileRecord) {
						this.formData.applicationFileRecord = {
							hasDiagnosisCertificate: false,
							hasFirstIdentificationReport: false,
							hasOtherJobHistory: false,
							hasOtherExaminationReport: false,
							hasOtherDetectionReport: false,
							hasOtherPersonalDoseRecord: false,
							hasOtherPersonalIdCard: false,
							hasOtherDepute: false,
							hasOther: false,
							remark: ''
						}
					}

					this.certificateList = res.data.fileList.diagnosisCertificate ?
						res.data.fileList.diagnosisCertificate : []
					this.identificationReportList = res.data.fileList.firstIdentificationReport ?
						res.data.fileList.firstIdentificationReport : []
					this.otherJobHistoryList = res.data.fileList.jobHistory ? res.data.fileList.jobHistory : []
					this.examinationReportList = res.data.fileList.examinationReport ?
						res.data.fileList.examinationReport : []
					this.detectionReportList = res.data.fileList.detectionReport ?
						res.data.fileList.detectionReport : []
					this.doseRecordtList = res.data.fileList.personalDoseRecord ?
						res.data.fileList.personalDoseRecord : []
					this.personalIdCardList = res.data.fileList.idCard ? res.data.fileList.idCard : []
					this.deputeList = res.data.fileList.depute ? res.data.fileList.depute : []
					this.otherList = res.data.fileList.other ? res.data.fileList.other : []
					if ((!this.formData.workerAgent) || this.formData.workerAgent == null) {
						this.formData.workerAgent = {
							agentName: '',
							agentIdCardCode: '',
							agentContactPhone: '',
							relationship: '',
						}
					}
					this.getIdeApplicationWord(diagnosisId)
				}
			},
			handleChoose(item) {
        if (item.id) {
          this.addType == 1 ? this.getDetail({userId:item.id}):this.getDetailByDiagnosisId({diagnosisId: item.id})
        }
        this.handleTypeClose()
			},
			handleTypeClose() {
				this.$refs.appealPopup.close();
				this.appealList = []
			},
		},
	}
</script>


<style lang="scss" scoped>
	.institution {
		width: 100%;
		// height: 100vh;
		// padding: 0 30rpx;
		box-sizing: border-box;
	}

	.nav-left {
		display: flex;
		align-items: center;
		width: auto;
		color: #fff;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.section-body {
		padding-top: 30rpx;

		.form {
			width: 100%;
			padding-bottom: 150rpx;

			.title {
				font-family: Source Han Sans;
				font-size: 16px;
				font-weight: bold;
				color: #3D3D3D;
			}

			.addBlList {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 30rpx;

				view {
					width: 102px;
					height: 32px;
					text-align: center;
					line-height: 32px;
					border-radius: 4px;
					background: #4163E1;
					color: #fff;
				}
			}
		}

		#section-2 {
			.title {
				margin-bottom: 32rpx;
			}
		}

		#section-6 {
			.title {
				margin-bottom: 32rpx;
			}

			.u-form {
				.u-form-item {
					padding: 0 24rpx;
					box-sizing: border-box;
					box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.0997), 0px 2px 4px 0px rgba(0, 0, 0, 0.0372);
					margin-bottom: 32rpx;
				}
			}
		}
	}

	.u-tabbar {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 56px;
		background: #FFFFFF;
		box-shadow: 0px 1px 7px 1px rgba(0, 0, 0, 0.2046);
		display: flex;
		align-items: center;
		justify-content: flex-end;


	}

	.info-item {
		width: 100%;
		margin-bottom: 20rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.info-item.full-width {
		width: 100%;
	}

	.section {
		background-color: #ffffff;
		border-radius: 12rpx;
		max-height: 400rpx;


		.career-list {
			height: 400rpx;
			overflow-y: auto;
		}
	}

	.career-item {
		border: 1px solid #eaeaea;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		background-color: #f9fafb;
	}

	::v-deep .u-form-item__body {
		padding: 10rpx 0;
	}

	.btn {
		width: 148rpx;
		height: 64rpx;
		line-height: 64rpx;
		text-align: center;
		border-radius: 4px;
		background: #F4F4F5;
		border: 1px solid #C7C9CC;
		margin-right: 40rpx;

		&.sync {
			background: #ECF5FF;
			border: 1px solid #9FCEFF;
			color: #409EFF;
		}

		&.prev {
			background: #ECF5FF;
			border: 1px solid #9FCEFF;
			color: #409EFF;
		}

		&.next {
			background: #4163E1;
			border: 1px solid #4163E1;
			color: #fff;
		}
	}

	::v-deep .uni-date-x--border[data-v-f2e7c4e8] {
		border: 0px solid #e5e5e5;
	}


	::v-deep .input-value-border[data-v-3ed22fe0] {
		border: none
	}

	.form-content {
		margin-bottom: 20px;
	}

	.form-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 8rpx 0;
		border-bottom: 2rpx dashed #ebeef5;
	}

	.form-item:hover {
		background-color: #f5f7fa;
	}

	.item-label {
		flex: 1;
		font-size: 28rpx;
		line-height: 1.5;
	}

	.checkbox-container {
		width: 30px;
		height: 30px;
	}

	.form-note {
		margin: 20px 0;
		padding: 10rpx;
		background-color: #f4f4f5;
		// border-left: 4px solid #409eff;
		color: #606266;
		font-size: 14px;
		line-height: 1.6;
	}

	.form-remark {
		margin: 20px 0;
	}

	.remark-label {
		margin-bottom: 10px;
		font-size: 14px;
	}

	::v-deep .uni-select[data-v-6b64008e] {
		border: none;
		border-bottom: none;
	}

	.appeal-popup {
		width: 650rpx;
		max-height: 120vh;
		/* 最大高度限制 */
		background: #fff;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
	}

	.popup-title {
		padding: 24rpx;
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		border-bottom: 2rpx solid #eee;
	}

	.popup-content {
		flex: 1;
		/* 填充剩余空间 */
		padding: 20rpx 24rpx;
		overflow-y: auto;
		/* 添加滚动 */
	}

	/* 列表项样式优化 */
	.diagnosis-item {
		padding: 24rpx;
		margin: 16rpx 0;
		background: #f8f8f8;
		border-radius: 12rpx;
	}

	.info-row {
		margin: 12rpx 0;
		// display: flex;
	}

	.label {
		color: #666;
		// width: 380rpx;
		// flex-shrink: 0;
	}

	.value {
		color: #333;
		// flex: 1;
	}

	.item-footer {
		margin-top: 24rpx;
		text-align: right;
	}

	/* 新增关闭按钮样式 */
	.popup-close {
		position: absolute;
		top: 10rpx;
		right: 20rpx;
		padding: 16rpx;
		z-index: 10;
	}

	// 鉴定类型选择样式调整
	.type-select-container {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.radio-group {
		flex: 1;
		display: flex;
	}

	.select-btn {
		width: 180rpx;
	}
</style>