<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="上报记录" />
		<view slot="gBody" class="grace-body section-body">
			<view class="diagnosis-result">
				<!-- <uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					上报记录
				</view>
			</block>
		</uni-nav-bar> -->
				<view class="search-wrap">
					<u--input v-model.trim="postData.name" placeholder="请输入上报机构名称" clearable @input="handleInputChange">
						<template slot="suffix">
							<u-button @click="handleSearch" type="primary" size="mini">搜索</u-button>
						</template>
					</u--input>
				</view>

				<view class="diagnosis-list">
					<u-empty v-if="diagnosisList.length == 0" mode="data" icon="/static/empty.png" text="暂无记录">
					</u-empty>
					<u-list>
						<u-list-item v-for="(item, index) in diagnosisList" :key="index">
							<view class="diagnosis-item">
								<view class="item-content">
									<view class="info-row">
										<text class="label">检查机构：</text>
										<text class="value">{{item.physicalExamOrg.name}}</text>
									</view>
									<view class="info-row">
										<text class="label">检查时间：</text>
										<text class="value">{{handletoDate(item.registerTime) || ''}}</text>
									</view>
									<view class="info-row">
										<text class="label">危害因素：</text>
										<text
											class="value">{{ item.checkHazardFactors.length && item.checkHazardFactors.map(item => item.name).join('、') || '' }}</text>
									</view>
								</view>
								<view class="item-footer" v-if="item.diagnosisStatus == 1">
									<u-button  type="primary" size="mini" @click="handleApply(item)">
										诊断申请
									</u-button>
								</view>
							</view>
						</u-list-item>
					</u-list>
				</view>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import diagnosisApi from '@/api/diagnosis'
	import {
		mapGetters
	} from 'vuex'
	import moment from 'moment'
	export default {
		data() {
			return {
				diagnosisList: [],
				postData: {
					name: '',
					pageNum: 1,
					pageSize: 1000
				},
				total: 0
			}
		},
		created() {},
		mounted() {
			this.fetchDiagnosisList()
		},
		computed: {
			...mapGetters(["userInfo"]),
		},
		methods: {
			handletoDate(value) {
				if (!value) return "";
				return moment(value).format("YYYY-MM-DD HH:mm:ss");
			},
			back() {
				uni.navigateBack()
			},
			handleSearch() {
				this.fetchDiagnosisList()
			},
			handleInputChange(value) {
				if (value == '') {
					this.fetchDiagnosisList();
				}
			},
			async fetchDiagnosisList() {
				const res = await diagnosisApi.getSuspectList(this.postData);
				this.diagnosisList = res.data
				// this.postData.pageNum = res.data.pageNum
				// this.postData.pageSize = res.data.pageSize
				// this.total = res.data.total
			},
			handleApply(item) {
				uni.navigateTo({
					// url: `/pages/institution/institution?id=${item.id}&checkNo=${item.checkNo}`
					url: `/pages/institution/institution?checkNo=${item.checkNo}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.diagnosis-result {
		// padding: 0 30rpx;

		.nav-left {
			display: flex;
			align-items: center;
			color: #fff;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}

		.diagnosis-list {
			margin-top: 20rpx;

			.diagnosis-item {
				background: #fff;
				border-radius: 12rpx;
				padding: 20rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

				.item-content {
					.info-row {
						display: flex;
						margin-bottom: 10rpx;

						.label {
							color: #666;
							font-size: 28rpx;
							width: 198rpx;
						}

						.value {
							color: #333;
							font-size: 28rpx;
							flex: 1;
						}
					}
				}

				.item-footer {
					margin-top: 20rpx;
					display: flex;
					justify-content: flex-end;
					gap: 20rpx;
				}
			}
		}
	}

	.search-wrap {
		margin-top: 10px;
		height: 70rpx;
		border-radius: 30rpx;
		background: #FFFFFF;
		border: 1px solid #ccc;
	}
</style>