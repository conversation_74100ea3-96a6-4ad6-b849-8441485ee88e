import diagnosisApi from '@/api/diagnosis.js'
import identifyApi from '@/api/identify.js'

const state = {
	// 数据字典
	areaList: [], //地区
	diseasesList: [], //职业病分类目录
	personGender: [], //性别
	idcardType: [], //身份证件类别
	hazard: [], //危害因素
	diagnosisStatus: [], //诊断的受理状态
	identifyStatus: [], //鉴定的受理状态
	diagnosisStatus: [], //诊断的受理状态
	identifyStatus: [], //鉴定的受理状态
};

const mutations = {
	setAreaList(state, areaList) {
		state.areaList = areaList;
	},
	setDiseasesList(state, diseasesList) {
		state.diseasesList = diseasesList;
	},
	setPersonGender(state, personGender) {
		state.personGender = personGender;
	},
	setIdcardType(state, idcardType) {
		state.idcardType = idcardType;
	},
	setHazard(state, hazard) {
		state.hazard = hazard;
	},
	setDiagnosisStatus(state, diagnosisStatus) {
		state.diagnosisStatus = diagnosisStatus;
	},
  setIdentifyStatus(state, identifyStatus) {
		state.identifyStatus = identifyStatus;
	},
  setIdentifyStatus(state, identifyStatus) {
		state.identifyStatus = identifyStatus;
	},
}

const actions = {
	// 获取地区
	getAreaList: async function({
		commit,
		state
	}) {
		diagnosisApi.getDictType({
			dictType: 'area'
		}).then(res => {
			commit('setAreaList', res.data);
		})
	},
	// 获取职业病分类目录
	getDiseasesList: async function({
		commit,
		state
	}) {
		diagnosisApi.getDictType({
			dictType: 'occupational_disease'
		}).then(res => {
			if (res.status == 200) commit('setDiseasesList', res.data);
		})
	},
	// 获取性别
	getPersonGender: async function({
		commit,
		state
	}) {
		diagnosisApi.getDictType({
			dictType: 'person_gender'
		}).then(res => {
			if (res.status == 200) commit('setPersonGender', res.data);
		})
	},
	// 获取身份证件类别
	getIdcardType: async function({
		commit,
		state
	}) {
		diagnosisApi.getDictType({
			dictType: 'idcard_type'
		}).then(res => {
			if (res.status == 200) commit('setIdcardType', res.data);
		})
	},
	// 获取危害因素
	getHazard: async function({
		commit,
		state
	}) {
		diagnosisApi.getDictType({
			dictType: 'hazard'
		}).then(res => {
			if (res.status == 200) commit('setHazard', res.data);
		})
	},
	// 获取诊断的受理状态
	getDiagnosisStatus: async function({
		commit,
		state
	}) {
		diagnosisApi.getDictType({
			dictType: 'diagnosis_process_status'
		}).then(res => {
			const typeMap = {
				'-1': 'error',
				0: 'warning',
				1: 'primary',
        4: 'warning',
				99: 'success'
			}
			const list = res.data.map((item) => ({
				value: item.dictCode,
				text: item.dictLabel,
				type: typeMap[item.dictCode] || 'info'
			}))
			list.unshift({
				value: '',
				text: '全部状态'
			})
			if (res.status == 200) commit('setDiagnosisStatus', list);
		})
	},
  	// 获取鉴定的受理状态
	getIdentifyStatus: async function({
		commit,
		state
	}) {
		identifyApi.getDictType({
			dictType: 'identification_process_status'
		}).then(res => {
			const typeMap = {
				'-1': 'error',
				0: 'warning',
				4: 'warning',
        5: 'primary',
				99: 'success'
			}
			const list = res.data.map((item) => ({
				value: item.dictCode,
				text: item.dictLabel,
				type: typeMap[item.dictCode] || 'info'
			}))
			list.unshift({
				value: '',
				text: '全部状态'
			})
			if (res.status == 200) commit('setIdentifyStatus', list);
		})
	},
};

export default {
	state,
	mutations,
	actions,
};