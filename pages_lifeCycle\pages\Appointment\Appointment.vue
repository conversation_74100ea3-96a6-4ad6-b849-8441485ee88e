<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF">
		<my-header slot="gHeader" title="选择机构" />
		<view class="grace-body container" slot="gBody">
			<view class="search">
				<view class="searchInfo" @click="gotoAppointmentRecord">
					<uni-icons type="compose" size="18" color="dodgerblue"></uni-icons>
					<text style="margin-left: 4rpx">预约记录</text>
				</view>
			</view>
			<view class="mechanism">
				<view class="mechanismList" v-for="item in institutions" :key="item.id">
					<view class="left">
						<view class="text">
							<view class="label"> 机构名称: </view>
							<view class="content"> {{ item.stationName }} </view>
						</view>
						<view class="text">
							<view class="label"> 所在地区: </view>
							<view class="content"> {{ item.address }} </view>
						</view>
						<view class="text">
							<view class="label"> 联系人: </view>
							<view class="content"> {{ item.contactPerson }} </view>
						</view>
						<view class="text">
							<view class="label"> 联系电话: </view>
							<view class="content"> {{ item.contactPhone }} </view>
						</view>
					</view>
					<view class="right">
						<view class="link" @click="openPopup(item)"> 立即预约>> </view>
					</view>
				</view>
			</view>

			<uni-popup ref="popup" @change="change" mask-background-color="#0000000">
				<view class="popup-content">
					<view class="title"> 立即预约 </view>
					<uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
						<uni-forms-item label="机构名称" required name="stationName" :label-width="80">
							<view class="mechanismName">
								{{ valiFormData.stationName }}
							</view>
						</uni-forms-item>
						<uni-forms-item label="诊疗医师" required name="doctor_id" :label-width="80">
							<uni-data-select v-model="valiFormData.doctor_id" :localdata="personList"
								style="background-color: #fff"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="职业病病人分类" required name="disease_category" :label-width="80">
							<uni-data-select v-model="valiFormData.disease_category" :localdata="categories"
								style="background-color: #fff"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="预约服务类别" name="service_type" required :label-width="80">
							<uni-easyinput type="text" v-model="valiFormData.service_type" placeholder="请输入预约服务类别"
								style="background-color: #fff" />
						</uni-forms-item>
						<uni-forms-item label="就诊时间" name="appt_date" required :label-width="80">
							<uni-datetime-picker type="datetime" v-model="valiFormData.appt_date" />
						</uni-forms-item>
						<view class="btns">
							<button type="default" @click="close">取消</button>
							<button type="primary" @click="submit('valiForm')">
								立即预约
							</button>
						</view>
					</uni-forms>
				</view>
			</uni-popup>
		</view>
	</gracePage>
</template>

<script>
	import stationApi from "@/api/rehabStation.js";
	import uniIcons from "@/components/uni-icons/uni-icons.vue";
	export default {
		data() {
			return {
				pageParams: {
					pageNum: 1,
					pageSize: 9999,
				},
				institutions: [],
				personList: [],
				valiFormData: {
					stationName: "",
					doctor_id: "",
					appt_date: "",
				},
				rules: {
					physician: {
						rules: [{
							required: true,
							errorMessage: "诊疗医师不能为空",
						}, ],
					},
					appt_date: {
						rules: [{
							required: true,
							errorMessage: "就诊时间不能为空",
						}, ],
					},
				},
				categories: [],
			};
		},
		components: {
			uniIcons,
		},
		created() {
			this.getInstitutions();
			this.getDiseaseClassifyList()
		},
		methods: {
			// 获取职业病分类
			async getDiseaseClassifyList() {
				const res = await stationApi.getDiseaseClassify();
				this.categories = res.data.map(item => {
					return {
						id: item.id,
						value: item.code,
						text: item.name
					}
				})
			},
			submit(valiForm) {
				this.$refs.valiForm
					.validate()
					.then(async (res) => {
						const params = {
							appt_date: this.valiFormData.appt_date,
							disease_category: this.valiFormData.disease_category,
							doctor_id: this.valiFormData.doctor_id,
							service_type: this.valiFormData.service_type,
							inst_id: this.valiFormData.siteId,
						};

						const result = await stationApi.createAppointment(params);
						if (result.status === 200) {
							this.$refs.popup.close();
							uni.showToast({
								title: "预约成功",
								icon: "success",
							});
							this.valiFormData = {};
						} else {
							uni.showToast({
								title: result.message || "预约失败",
								icon: "none",
							});
						}
					})
					.catch((err) => {
						console.log("表单错误信息：", err);
					});
			},
			// 获取康复站列表
			async getInstitutions() {
				try {
					const params = {
						...this.pageParams,
					};
					const res = await stationApi.station(params);
					console.log(res);
					if (res && res.data) {
						this.institutions = res.data.list.map((item) => ({
							value: item.id,
							text: item.stationName,
							...item,
						}));
					}
				} catch (error) {
					console.error("获取康复站列表失败:", error);
					uni.showToast({
						title: "获取康复站列表失败",
						icon: "none",
					});
				}
			},
			async getPersonnel() {
				try {
					const params = {
						siteId: this.valiFormData.siteId,
						personnelCategoryCode: "15",
						...this.pageParams,
					};
					const res = await stationApi.personnel(params);
					console.log(res);
					if (res && res.data) {
						this.personList = res.data.list.map((item) => ({
							value: item.id,
							text: item.name,
						}));
					}
				} catch (error) {
					console.error("获取人员列表失败:", error);
					uni.showToast({
						title: "获取人员列表失败",
						icon: "none",
					});
				}
			},
			close() {
				this.$refs.popup.close();
			},
			change(e) {
				console.log(e);
			},
			openPopup(item) {
				this.valiFormData.stationName = item.stationName;
				this.valiFormData.siteId = item.id;
				this.getPersonnel();
				this.$refs.popup.open("center");
			},
			gotoAppointmentRecord() {
				uni.navigateTo({
					url: "/pages_lifeCycle/pages/Appointment/AppointmentRecord",
				});
			},
		},
	};
</script>

<style scoped lang="less">
	@media screen and (min-width: 960px) {
		/deep/ .uni-date-single--x {
			background-color: #fff;
			position: absolute;
			top: -20rem !important;
			z-index: 999;
			border: 1px solid #ebeef5;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
			border-radius: 4px;
		}

		/deep/ .uni-popper__arrow {
			display: none;
		}
	}

	.grace-body {
		min-height: calc(100vh - 120rpx);
		padding-top: 30rpx;
		background-color: #f6f6f6;
	}

	.grace-body .search {
		display: flex;
		justify-content: center;
		align-items: flex-end;
		flex-direction: column;
		color: dodgerblue;
		margin-bottom: 30rpx;
	}

	.grace-body .search .searchInfo {
		display: flex;
		align-items: center;
	}

	.mechanismList {
		background-color: #fff;
		border-radius: 8rpx;
		padding: 30rpx 30rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.mechanismList .left {
		flex: 1;
		margin-right: 20rpx;
		overflow: hidden;
		/* 隐藏超出部分 */
		white-space: nowrap;
		/* 禁止换行 */
		text-overflow: ellipsis;
		/* 使用省略号表示超出部分 */
	}

	.mechanismList .right {
		color: dodgerblue;
		font-size: 28rpx;
	}

	.mechanismList .left .text {
		display: flex;
		margin-bottom: 10rpx;
		align-items: flex-end;
	}

	.mechanismList .left .text .label {
		font-size: 28rpx;
		margin-right: 10rpx;
		width: 120rpx;
	}

	.mechanismList .left .text .content {
		font-size: 28rpx;
		overflow: hidden;
		/* 隐藏超出部分 */
		white-space: nowrap;
		/* 禁止换行 */
		text-overflow: ellipsis;
		/* 使用省略号表示超出部分 */
	}

	.popup-content {
		width: 80vw;
		height: 65vh;
		background-color: #fff;
		border-radius: 10rpx;
		border: 1px solid #eee;
	}

	.popup-content .title {
		text-align: center;
		font-size: 32rpx;
		margin-bottom: 30rpx;
		font-weight: bold;
		padding: 30rpx 0;
		border-bottom: 1px solid #eee;
	}

	.uni-forms {
		padding: 30rpx;
	}

	.btns {
		position: absolute;
		bottom: 2%;
		width: 100%;
		left: 0;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.btns button {
		width: 40%;
		margin: 0;
		font-size: 28rpx;
	}
</style>