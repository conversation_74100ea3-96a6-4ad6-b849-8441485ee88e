<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="个人电子健康档案" />
		<view slot="gBody" class="grace-body">
			<view class="card" v-for="(item, i) in list" :key="i">
				<img :src="item.img" mode="scaleToFill" class="card-img" />
				<view class="card-content">
					<view class="card-content-text">
						<view class="card-content-text-title">{{ item.title }}</view>
						<view class="card-content-text-desc">{{ item.desc }}</view>
					</view>
					<view style="display: flex;align-items: center;">
						<button class="card-content-button" @click="toUrl(item.url)">查看</button>
					</view>
				</view>
			</view>
		</view>
	</gracePage>

</template>
<script>

import svg1 from '@/static/svg/个人基本信息.svg'
import svg2 from '@/static/svg/职业健康体检记录.svg'
import svg3 from '@/static/svg/诊断医疗记录.svg'
import svg4 from '@/static/svg/重点人群健康管理记录.svg'
import svg5 from '@/static/svg/工伤保险记录.svg'
import svg6 from '@/static/svg/医疗卫生服务记录.svg'

export default {
	data() {
		return {
			list: [
				{
					title: '个人基本信息',
					desc: '劳动者可以查看个人基本信息及更改',
					img: svg1,
					url: 'basicInfo'
				},
				{
					title: '职业健康体检记录',
					desc: '劳动者可查看职业健康体检记录及结果如有异常可申报',
					img: svg2,
					url: 'exam'
				},
				{
					title: '诊断医疗记录',
					desc: '劳动者可查看职业健康诊断记录及结果如有异常可申报',
					img: svg3,
					url: 'diagnose'
				},
				{
					title: '重点人群健康管理记录',
					desc: '劳动者可以查看重点人群健康管理记录',
					img: svg4,
					url: 'healthManage'
				},
				{
					title: '工伤保险记录',
					desc: '劳动者可查看工伤保险记录',
					img: svg5,
					url: 'injury'
				},
				{
					title: '医疗卫生服务记录',
					desc: '劳动者可查看医疗卫生服务记录',
					img: svg6,
					url: 'servic'
				},
				{
					title: '档案申诉情况',
					desc: '劳动者可查看档案申诉情况',
					img: svg3,
					url: 'complaint'
				},
				{
					title: '档案授权管理',
					desc: '劳动者能够看到所有申请授权的记录并进行处理',
					img: svg4,
					url: 'auth'
				},
				{
					title: '健康档案报告',
					desc: '通过统计、趋势分析对个人健康档案进行综合呈现',
					img: svg2,
					url: 'report'
				},
				{
					title: '职业健康检查预警',
					desc: '劳动者能够看到所有职业健康检查提醒预警',
					img: svg2,
					url: 'warning'
				},
			]
		}
	},
	methods: {
		toUrl(url) {
			
			uni.navigateTo({
				url
			});
		},
	},
	components: {
	}
}
</script>

<style lang="scss" scoped>
.grace-body {
	height: 100%;
	background-color: #f6f6f6 !important;
	display: flex;
	flex-direction: column;
	align-items: center;

	gap: 20rpx;
	padding-top: 30rpx;

	.card {
		width: 690rpx;
		height: 160rpx;
		border-radius: 8rpx;
		background-color: #fff;
		box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.0372);

		display: flex;

		padding: 24rpx;
		box-sizing: border-box;

		&:last-child {
			margin-bottom: 24rpx;
		}


		.card-img {
			width: 112rpx;
			height: 112rpx;
		}

		.card-content {
			width: 100%;
			margin-left: 24rpx;
			display: flex;
			justify-content: space-between;
			gap: 56rpx;


			.card-content-text {

				.card-content-text-title {
					opacity: 0.9;

					font-family: Source Han Sans;
					font-size: 32rpx;
					font-weight: bold;
					line-height: 30rpx;
					letter-spacing: 0px;

					font-variation-settings: "opsz" auto;
					color: #000000;

				}

				.card-content-text-desc {
					opacity: 0.9;

					margin-top: 12rpx;
					font-family: Source Han Sans;
					font-size: 24rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: 0px;

					font-variation-settings: "opsz" auto;
					color: #666666;
				}
			}

			.card-content-button {
				width: 120rpx;
				height: 64rpx;
				/* 圆 */
				border-radius: 256000px;
				opacity: 1;

				background: #F0F9EB;
				box-sizing: border-box;
				border: 1px solid #B3E09C;

				color: #67C23A;
				font-size: 28rpx;
				font-weight: bold;
				line-height: 44rpx;
				letter-spacing: 0px;
				font-variation-settings: "wght" 500;

				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0;
			}		}
	}
}
</style>