"use strict";
import req from '@/utils/http.js' //导入 封装的请求

// 新增/编辑工伤认定申请信息
export function addOrEditWorkInjuryRecognition(data) {
	return req({
		url: 'manage/adminorg/workInjuryRecognition',
		data,
		method: 'post',
	});
}

// 获取工伤列表
export const getWorkInjuryRecognitionList = data => {
	return req({
		url: 'manage/adminorg/workInjuryRecognition',
		data,
		method: 'get',
	});
};

// 获取工伤认定申请详情
export const getWorkInjuryRecognitionDetail = data => {
	return req({
		url: 'manage/adminorg/workInjuryRecognitionDetail?_id=' + data._id,
		method: 'get',
	});
};

// 删除工伤申请
export const deleteWorkInjuryRecognition = data => {
	return req({
		url: 'manage/adminorg/workInjuryRecognition',
		method: 'delete',
		data,
	});
};

// 下载文件模版
export const downloadTemplateFile = data => {
	return req({
		url: 'manage/adminorg/recognitionTemp',
		data,
		method: 'get',
	});
};

// 上传文件
export const uploadFile = data => {
	return req({
		url: 'app/file',
		data,
		method: 'post',
		header: {
			'Content-Type': 'multipart/form-data',
		}
	});
};

// 删除文件
export const deleteFile = data => {
	return req({
		url: 'app/file?filePath=' + data.filePath,
		method: 'delete',
	});
};