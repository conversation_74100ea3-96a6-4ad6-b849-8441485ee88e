<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="体检信息" />
		<view slot="gBody" class="grace-body content">
			<view class="content_main">
				<!-- 体检部分 -->
				<view class="content_maina">
					<view class="content_text">体检</view>
					<block v-for="(item, index) in healthCheckData" :key="index">
						<view class="content_list">
							<view class="right-content">
								<view class="top_img">
									<image class="content_img" :src="item.image" alt="" />
								</view>
								<view class="top">
									<view class="top_title">{{ item.title }}</view>
									<view class="top_list">{{ item.description }}</view>
								</view>
							</view>
							<view class="top_btn">
								<text class="btn" @click="goToPage(item)">{{ item.buttonText }}</text>
							</view>
						</view>
					</block>
				</view>

				<!-- 职业病诊断申请部分 -->
				<view class="content_maina">
					<view class="content_text">职业病诊断管理</view>
					<block v-for="(item, index) in diseaseDiagnosisData" :key="index">
						<view class="content_list">
							<view class="right-content">
								<view class="top_img">
									<image class="content_img" :src="item.image" alt="" />
								</view>
								<view class="top">
									<view class="top_title">{{ item.title }}</view>
									<view class="top_list">{{ item.description }}</view>
								</view>
							</view>
							<view class="top_btn">
								<text class="btn" @click="goToPage(item)">{{ item.buttonText }}</text>
							</view>
						</view>
					</block>
				</view>
			</view>
		</view>

	</gracePage>


</template>

<script>
	export default {
		data() {
			return {
				// 体检相关的模拟数据
				healthCheckData: [{
						image: "../../static/physical.png",
						title: "体检预约",
						description: "劳动者根据所在单位设定体检计划预约体检机构进行体检",
						buttonText: "预约",
						targetPage: "/pages/institution/tjBooking"
					},
					{
						image: "../../static/notebook.png",
						title: "预约记录",
						description: "劳动者可查看预约体检机构预约结果",
						buttonText: "查看",
						targetPage: "/pages/institution/tjRecord"
					},
					{
						image: "../../static/line.png",
						title: "体检结果",
						description: "劳动者可查看体检检测结果及体检报告",
						buttonText: "查看",
						targetPage: "/pages/institution/tjResult"
					}
				],

				// 职业病诊断申请相关的模拟数据
				diseaseDiagnosisData: [{
						image: "../../static/apartment.png",
						title: "诊断机构",
						description: "劳动者可选择辖区内的诊断机构进行职业病诊断申请",
						buttonText: "申请",
						targetPage: "/pages/institution/institution"
					},
					{
						image: "../../static/memo.png",
						title: "申请记录",
						description: "劳动者可查看历次职业病诊断申请记录",
						buttonText: "查看",
						targetPage: "/pages/institution/zdResult"
					},
					{
						image: "../../static/notebook.png",
						title: "疑似职业病上报查询",
						description: "劳动者可查看疑似职业病上报记录",
						buttonText: "查看",
						targetPage: "/pages/institution/ysReport"
					}
				]
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},

			goToPage(item) {
				uni.navigateTo({
					url: `${item.targetPage}`
				})
			},


			// goToBooking(){
			// 	uni.navigateTo({
			// 		url: "/pages/institution/tjBooking"
			// 	})
			// }
		},
		onLoad() {}
	}
</script>
<style lang="scss">
	.content {
		box-sizing: border-box;
		width: 100%;
		height: 100vh;
		background-color: #f6f6f6;
		display: flex;
		flex-direction: column;
		align-items: center;

		.content_main {
			width: 690rpx;
			height: 100%;

			// margin:  auto;
			.content_maina {
				.content_text {
					margin-top: 15rpx;
					color: #3D3D3D;
					font-weight: bold;
				}

				.content_list {
					width: 100%;
					box-sizing: border-box;
					height: 160rpx;
					background: #fff;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 15rpx;
					padding: 0 24rpx;

					.right-content {
						display: flex;
						align-items: center;

						.content_img {
							width: 112rpx;
							height: 112rpx;
						}

						.top {
							margin-left: 24rpx;

							.top_title {
								font-weight: bolder;
								font-size: 32rpx;
								margin-bottom: 12rpx;
								color: #000000;
							}

							.top_list {
								color: #666666;
								font-size: 24rpx;
							}
						}
					}

					.top_btn {
						display: flex;
						margin-left: 12px;

						.btn {
							width: 50px;
							height: 28px;
							line-height: 28px;
							text-align: center;
							font-size: 14px;
							background: #F0F9EB;
							border-radius: 20px;
							flex-shrink: 0;
							border: 2rpx solid #B3E09C;
							color: #67C23A;
						}
					}
				}

				&:nth-child(2) {
					margin-top: 15px;
				}
			}
		}
	}
</style>