(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-identify-jgDetail"],{"01fc":function(i,e,t){"use strict";t.r(e);var n=t("640e"),o=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return n[i]}))}(a);e["default"]=o.a},"108a":function(i,e,t){i.exports=t.p+"static/img/leftArrow.e84103a9.svg"},"165f":function(i,e,t){"use strict";t.r(e);var n=t("88fc"),o=t("8299");for(var a in o)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return o[i]}))}(a);t("c453");var l=t("828b"),c=Object(l["a"])(o["default"],n["b"],n["c"],!1,null,"59765974",null,!1,n["a"],void 0);e["default"]=c.exports},"1b01":function(i,e,t){"use strict";t.d(e,"b",(function(){return n})),t.d(e,"c",(function(){return o})),t.d(e,"a",(function(){}));var n=function(){var i=this.$createElement,e=this._self._c||i;return e("v-uni-view",{staticClass:"uni-status-bar",style:{height:this.statusBarHeight}},[this._t("default")],2)},o=[]},"23e6":function(i,e,t){"use strict";t("6a54");var n=t("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t("64aa");var o=n(t("8e98")),a={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:o.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=a},"28af":function(i,e,t){"use strict";t.r(e);var n=t("8f2b"),o=t("76e3");for(var a in o)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return o[i]}))}(a);t("bb00");var l=t("828b"),c=Object(l["a"])(o["default"],n["b"],n["c"],!1,null,"5c0264f4",null,!1,n["a"],void 0);e["default"]=c.exports},"2bde":function(i,e,t){var n=t("9f32");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[i.i,n,""]]),n.locals&&(i.exports=n.locals);var o=t("967d").default;o("6ccff0e0",n,!0,{sourceMap:!1,shadowMode:!1})},"2d642":function(i,e,t){"use strict";t.r(e);var n=t("6ae1"),o=t("bbbf");for(var a in o)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return o[i]}))}(a);t("f046");var l=t("828b"),c=Object(l["a"])(o["default"],n["b"],n["c"],!1,null,"14fd6023",null,!1,n["a"],void 0);e["default"]=c.exports},3129:function(i,e,t){var n=t("63f7");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[i.i,n,""]]),n.locals&&(i.exports=n.locals);var o=t("967d").default;o("0a470295",n,!0,{sourceMap:!1,shadowMode:!1})},"470b":function(i,e,t){var n=t("c86c");e=n(!1),e.push([i.i,'uni-view[data-v-59765974], uni-scroll-view[data-v-59765974], uni-swiper-item[data-v-59765974]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}@font-face{font-family:uicon-iconfont;src:url(https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf) format("truetype")}.u-icon[data-v-59765974]{display:flex;align-items:center}.u-icon--left[data-v-59765974]{flex-direction:row-reverse;align-items:center}.u-icon--right[data-v-59765974]{flex-direction:row;align-items:center}.u-icon--top[data-v-59765974]{flex-direction:column-reverse;justify-content:center}.u-icon--bottom[data-v-59765974]{flex-direction:column;justify-content:center}.u-icon__icon[data-v-59765974]{font-family:uicon-iconfont;position:relative;\ndisplay:flex;\nflex-direction:row;align-items:center}.u-icon__icon--primary[data-v-59765974]{color:#3c9cff}.u-icon__icon--success[data-v-59765974]{color:#5ac725}.u-icon__icon--error[data-v-59765974]{color:#f56c6c}.u-icon__icon--warning[data-v-59765974]{color:#f9ae3d}.u-icon__icon--info[data-v-59765974]{color:#909399}.u-icon__img[data-v-59765974]{height:auto;will-change:transform}.u-icon__label[data-v-59765974]{line-height:1}',""]),i.exports=e},"4e8d":function(i,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""}},"63f7":function(i,e,t){var n=t("c86c"),o=t("2ec5"),a=t("e549");e=n(!1);var l=o(a);e.push([i.i,"@font-face{font-family:uniicons;src:url("+l+') format("truetype")}.uni-icons[data-v-5c0264f4]{font-family:uniicons;text-decoration:none;text-align:center}',""]),i.exports=e},"640e":function(i,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"UniStatusBar",data:function(){return{statusBarHeight:uni.getSystemInfoSync().statusBarHeight+"px"}}};e.default=n},"6ae1":function(i,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return n}));var n={uniNavBar:t("d23e").default,uIcon:t("165f").default},o=function(){var i=this,e=i.$createElement,n=i._self._c||e;return n("v-uni-view",{staticClass:"institution"},[n("uni-nav-bar",{attrs:{leftWidth:"180rpx",fixed:!0,"background-color":"#007AFF",border:"{false}"},on:{clickLeft:function(e){arguments[0]=e=i.$handleEvent(e),i.back.apply(void 0,arguments)}}},[n("template",{attrs:{slot:"left"},slot:"left"},[n("v-uni-view",{staticClass:"nav-left"},[n("v-uni-image",{attrs:{src:t("108a"),mode:""}}),i._v("机构详情")],1)],1)],2),n("v-uni-view",{staticClass:"section-body"},[n("v-uni-view",{staticClass:"jg-detail"},[n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"label"},[i._v("机构名称：")]),n("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.institutionName))])],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"label"},[i._v("联系人：")]),n("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.contactPerson))])],1),n("v-uni-view",{staticClass:"row phone"},[n("v-uni-view",{staticClass:"label"},[i._v("联系电话：")]),n("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.contactPhone))]),n("u-icon",{attrs:{name:"phone-fill",color:"#409EFF",size:"20"}})],1),n("v-uni-view",{staticClass:"row address"},[n("v-uni-view",{staticClass:"label"},[i._v("地址：")]),n("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.address)+"4号")]),n("u-icon",{attrs:{name:"map-fill",color:"#409EFF",size:"20"}})],1)],1)],1)],1)},a=[]},"6c9e":function(i,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t("64aa");var n={props:{name:{type:String,default:uni.$u.props.icon.name},color:{type:String,default:uni.$u.props.icon.color},size:{type:[String,Number],default:uni.$u.props.icon.size},bold:{type:Boolean,default:uni.$u.props.icon.bold},index:{type:[String,Number],default:uni.$u.props.icon.index},hoverClass:{type:String,default:uni.$u.props.icon.hoverClass},customPrefix:{type:String,default:uni.$u.props.icon.customPrefix},label:{type:[String,Number],default:uni.$u.props.icon.label},labelPos:{type:String,default:uni.$u.props.icon.labelPos},labelSize:{type:[String,Number],default:uni.$u.props.icon.labelSize},labelColor:{type:String,default:uni.$u.props.icon.labelColor},space:{type:[String,Number],default:uni.$u.props.icon.space},imgMode:{type:String,default:uni.$u.props.icon.imgMode},width:{type:[String,Number],default:uni.$u.props.icon.width},height:{type:[String,Number],default:uni.$u.props.icon.height},top:{type:[String,Number],default:uni.$u.props.icon.top},stop:{type:Boolean,default:uni.$u.props.icon.stop}}};e.default=n},"6f0c":function(i,e,t){"use strict";t("6a54");var n=t("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t("64aa");var o=n(t("dbe1")),a=function(i){return"number"===typeof i?i+"px":i},l={name:"UniNavBar",components:{statusBar:o.default},emits:["clickLeft","clickRight","clickTitle"],props:{dark:{type:Boolean,default:!1},title:{type:String,default:""},leftText:{type:String,default:""},rightText:{type:String,default:""},leftIcon:{type:String,default:""},rightIcon:{type:String,default:""},fixed:{type:[Boolean,String],default:!1},color:{type:String,default:""},backgroundColor:{type:String,default:""},statusBar:{type:[Boolean,String],default:!1},shadow:{type:[Boolean,String],default:!1},border:{type:[Boolean,String],default:!0},height:{type:[Number,String],default:44},leftWidth:{type:[Number,String],default:60},rightWidth:{type:[Number,String],default:60},stat:{type:[Boolean,String],default:""}},computed:{themeBgColor:function(){return this.dark?this.backgroundColor?this.backgroundColor:this.dark?"#333":"#FFF":this.backgroundColor||"#FFF"},themeColor:function(){return this.dark?this.color?this.color:this.dark?"#fff":"#333":this.color||"#333"},navbarHeight:function(){return a(this.height)},leftIconWidth:function(){return a(this.leftWidth)},rightIconWidth:function(){return a(this.rightWidth)}},mounted:function(){uni.report&&this.stat&&""!==this.title&&uni.report("title",this.title)},methods:{onClickLeft:function(){this.$emit("clickLeft")},onClickRight:function(){this.$emit("clickRight")},onClickTitle:function(){this.$emit("clickTitle")}}};e.default=l},"71bf":function(i,e,t){"use strict";var n=t("aa3b"),o=t.n(n);o.a},"73d0":function(i,e,t){var n=t("acc7");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[i.i,n,""]]),n.locals&&(i.exports=n.locals);var o=t("967d").default;o("1d32745b",n,!0,{sourceMap:!1,shadowMode:!1})},"76e3":function(i,e,t){"use strict";t.r(e);var n=t("23e6"),o=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return n[i]}))}(a);e["default"]=o.a},8299:function(i,e,t){"use strict";t.r(e);var n=t("e6d3"),o=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return n[i]}))}(a);e["default"]=o.a},"88fc":function(i,e,t){"use strict";t.d(e,"b",(function(){return n})),t.d(e,"c",(function(){return o})),t.d(e,"a",(function(){}));var n=function(){var i=this,e=i.$createElement,t=i._self._c||e;return t("v-uni-view",{staticClass:"u-icon",class:["u-icon--"+i.labelPos],on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i.clickHandler.apply(void 0,arguments)}}},[i.isImg?t("v-uni-image",{staticClass:"u-icon__img",style:[i.imgStyle,i.$u.addStyle(i.customStyle)],attrs:{src:i.name,mode:i.imgMode}}):t("v-uni-text",{staticClass:"u-icon__icon",class:i.uClasses,style:[i.iconStyle,i.$u.addStyle(i.customStyle)],attrs:{"hover-class":i.hoverClass}},[i._v(i._s(i.icon))]),""!==i.label?t("v-uni-text",{staticClass:"u-icon__label",style:{color:i.labelColor,fontSize:i.$u.addUnit(i.labelSize),marginLeft:"right"==i.labelPos?i.$u.addUnit(i.space):0,marginTop:"bottom"==i.labelPos?i.$u.addUnit(i.space):0,marginRight:"left"==i.labelPos?i.$u.addUnit(i.space):0,marginBottom:"top"==i.labelPos?i.$u.addUnit(i.space):0}},[i._v(i._s(i.label))]):i._e()],1)},o=[]},"8e98":function(i,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},"8f2b":function(i,e,t){"use strict";t.d(e,"b",(function(){return n})),t.d(e,"c",(function(){return o})),t.d(e,"a",(function(){}));var n=function(){var i=this,e=i.$createElement,t=i._self._c||e;return t("v-uni-text",{staticClass:"uni-icons",class:[i.customIcons,i.customIcons?i.type:""],style:{color:i.color,"font-size":i.size+"px"},on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i._onClick.apply(void 0,arguments)}}},[i._v(i._s(i.icons[i.type]))])},o=[]},"96ec":function(i,e,t){var n=t("c86c");e=n(!1),e.push([i.i,".uni-status-bar[data-v-285e3a40]{height:20px}",""]),i.exports=e},"9f32":function(i,e,t){var n=t("c86c");e=n(!1),e.push([i.i,".uni-nav-bar-text[data-v-36458f7c]{font-size:14px}.uni-nav-bar-right-text[data-v-36458f7c]{font-size:12px}.uni-navbar__content[data-v-36458f7c]{position:relative;background-color:initial}.uni-navbar-btn-text[data-v-36458f7c]{display:flex;flex-direction:column;justify-content:flex-start;align-items:center;line-height:12px}.uni-navbar__header[data-v-36458f7c]{display:flex;padding:0 10px;flex-direction:row;height:44px;font-size:12px}.uni-navbar__header-btns[data-v-36458f7c]{overflow:hidden;display:flex;flex-wrap:nowrap;flex-direction:row;width:%?120?%;justify-content:center;align-items:center;cursor:pointer}.uni-navbar__header-btns-left[data-v-36458f7c]{display:flex;width:%?120?%;justify-content:flex-start;align-items:center}.uni-navbar__header-btns-right[data-v-36458f7c]{display:flex;flex-direction:row;justify-content:flex-end;align-items:center}.uni-navbar__header-container[data-v-36458f7c]{display:flex;flex:1;padding:0 10px;overflow:hidden}.uni-navbar__header-container-inner[data-v-36458f7c]{display:flex;flex:1;flex-direction:row;align-items:center;justify-content:center;font-size:12px;overflow:hidden}.uni-navbar__placeholder-view[data-v-36458f7c]{height:44px}.uni-navbar--fixed[data-v-36458f7c]{position:fixed;z-index:99;left:var(--window-left);right:var(--window-right)}.uni-navbar--shadow[data-v-36458f7c]{box-shadow:0 1px 6px #ccc}.uni-navbar--border[data-v-36458f7c]{border-bottom-width:%?1?%;border-bottom-style:solid;border-bottom-color:#eee}.uni-ellipsis-1[data-v-36458f7c]{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}",""]),i.exports=e},a7a6:function(i,e,t){var n=t("470b");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[i.i,n,""]]),n.locals&&(i.exports=n.locals);var o=t("967d").default;o("758c0dd4",n,!0,{sourceMap:!1,shadowMode:!1})},aa3b:function(i,e,t){var n=t("96ec");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[i.i,n,""]]),n.locals&&(i.exports=n.locals);var o=t("967d").default;o("0e22cd8c",n,!0,{sourceMap:!1,shadowMode:!1})},acc7:function(i,e,t){var n=t("c86c");e=n(!1),e.push([i.i,".institution[data-v-14fd6023]{width:100%;min-height:100vh;background-color:#f6f6f6;padding:0 %?30?%;box-sizing:border-box}.nav-left[data-v-14fd6023]{display:flex;align-items:center;width:auto;color:#fff;white-space:nowrap}.nav-left uni-image[data-v-14fd6023]{width:%?40?%;height:%?40?%}.section-body[data-v-14fd6023]{padding-top:%?30?%}.section-body .jg-detail .row[data-v-14fd6023]{width:100%;border-bottom:1px solid hsla(0,0%,62%,.32941176470588235);margin-bottom:%?30?%;padding-bottom:%?20?%;position:relative;display:flex;flex-direction:column}.section-body .jg-detail .row .label[data-v-14fd6023]{font-family:Source Han Sans;font-size:16px;color:#333;padding-bottom:%?14?%}.section-body .jg-detail .row .desc[data-v-14fd6023]{font-family:Source Han Sans;font-size:16px;font-weight:350;color:#666}.section-body .jg-detail .row .u-icon[data-v-14fd6023]{position:absolute;right:0;bottom:%?14?%}",""]),i.exports=e},b0e5:function(i,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return n}));var n={uniIcons:t("28af").default},o=function(){var i=this,e=i.$createElement,t=i._self._c||e;return t("v-uni-view",{staticClass:"uni-navbar",class:{"uni-dark":i.dark,"uni-nvue-fixed":i.fixed}},[t("v-uni-view",{staticClass:"uni-navbar__content",class:{"uni-navbar--fixed":i.fixed,"uni-navbar--shadow":i.shadow,"uni-navbar--border":i.border},style:{"background-color":i.themeBgColor,"border-bottom-color":i.themeColor}},[i.statusBar?t("status-bar"):i._e(),t("v-uni-view",{staticClass:"uni-navbar__header",style:{color:i.themeColor,backgroundColor:i.themeBgColor,height:i.navbarHeight}},[t("v-uni-view",{staticClass:"uni-navbar__header-btns uni-navbar__header-btns-left",style:{width:i.leftIconWidth},on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i.onClickLeft.apply(void 0,arguments)}}},[i._t("left",[i.leftIcon.length>0?t("v-uni-view",{staticClass:"uni-navbar__content_view"},[t("uni-icons",{attrs:{color:i.themeColor,type:i.leftIcon,size:"20"}})],1):i._e(),i.leftText.length?t("v-uni-view",{staticClass:"uni-navbar-btn-text",class:{"uni-navbar-btn-icon-left":!i.leftIcon.length>0}},[t("v-uni-text",{style:{color:i.themeColor,fontSize:"12px"}},[i._v(i._s(i.leftText))])],1):i._e()])],2),t("v-uni-view",{staticClass:"uni-navbar__header-container ",on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i.onClickTitle.apply(void 0,arguments)}}},[i._t("default",[i.title.length>0?t("v-uni-view",{staticClass:"uni-navbar__header-container-inner"},[t("v-uni-text",{staticClass:"uni-nav-bar-text uni-ellipsis-1",style:{color:i.themeColor}},[i._v(i._s(i.title))])],1):i._e()])],2),t("v-uni-view",{staticClass:"uni-navbar__header-btns uni-navbar__header-btns-right",style:{width:i.rightIconWidth},on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i.onClickRight.apply(void 0,arguments)}}},[i._t("right",[i.rightIcon.length?t("v-uni-view",[t("uni-icons",{attrs:{color:i.themeColor,type:i.rightIcon,size:"22"}})],1):i._e(),i.rightText.length&&!i.rightIcon.length?t("v-uni-view",{staticClass:"uni-navbar-btn-text"},[t("v-uni-text",{staticClass:"uni-nav-bar-right-text",style:{color:i.themeColor}},[i._v(i._s(i.rightText))])],1):i._e()])],2)],1)],1),i.fixed?t("v-uni-view",{staticClass:"uni-navbar__placeholder"},[i.statusBar?t("status-bar"):i._e(),t("v-uni-view",{staticClass:"uni-navbar__placeholder-view",style:{height:i.navbarHeight}})],1):i._e()],1)},a=[]},bb00:function(i,e,t){"use strict";var n=t("3129"),o=t.n(n);o.a},bbbf:function(i,e,t){"use strict";t.r(e);var n=t("d3f0"),o=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return n[i]}))}(a);e["default"]=o.a},c453:function(i,e,t){"use strict";var n=t("a7a6"),o=t.n(n);o.a},d23e:function(i,e,t){"use strict";t.r(e);var n=t("b0e5"),o=t("f864");for(var a in o)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return o[i]}))}(a);t("ef52");var l=t("828b"),c=Object(l["a"])(o["default"],n["b"],n["c"],!1,null,"36458f7c",null,!1,n["a"],void 0);e["default"]=c.exports},d3f0:function(i,e,t){"use strict";t("6a54");var n=t("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(t("2634")),a=n(t("2fdc")),l=n(t("c96d")),c={data:function(){return{id:"",institutionDetail:{}}},created:function(i){this.getInsDetail()},onLoad:function(i){this.id=i},methods:{getInsDetail:function(){var i=this;return(0,a.default)((0,o.default)().mark((function e(){var t;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l.default.getInstitutionDetail(i.id);case 2:t=e.sent,i.institutionDetail=t.data;case 4:case"end":return e.stop()}}),e)})))()},back:function(){uni.navigateBack()}}};e.default=c},dbe1:function(i,e,t){"use strict";t.r(e);var n=t("1b01"),o=t("01fc");for(var a in o)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return o[i]}))}(a);t("71bf");var l=t("828b"),c=Object(l["a"])(o["default"],n["b"],n["c"],!1,null,"285e3a40",null,!1,n["a"],void 0);e["default"]=c.exports},e549:function(i,e,t){i.exports=t.p+"assets/uni.75745d34.ttf"},e6d3:function(i,e,t){"use strict";t("6a54");var n=t("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t("aa9c"),t("4626"),t("5ac7"),t("5ef2");var o=n(t("4e8d")),a=n(t("6c9e")),l={name:"u-icon",data:function(){return{}},mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],computed:{uClasses:function(){var i=[];return i.push(this.customPrefix+"-"+this.name),this.color&&uni.$u.config.type.includes(this.color)&&i.push("u-icon__icon--"+this.color),i},iconStyle:function(){var i={};return i={fontSize:uni.$u.addUnit(this.size),lineHeight:uni.$u.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:uni.$u.addUnit(this.top)},this.color&&!uni.$u.config.type.includes(this.color)&&(i.color=this.color),i},isImg:function(){return-1!==this.name.indexOf("/")},imgStyle:function(){var i={};return i.width=this.width?uni.$u.addUnit(this.width):uni.$u.addUnit(this.size),i.height=this.height?uni.$u.addUnit(this.height):uni.$u.addUnit(this.size),i},icon:function(){return o.default["uicon-"+this.name]||this.name}},methods:{clickHandler:function(i){this.$emit("click",this.index),this.stop&&this.preventEvent(i)}}};e.default=l},ef52:function(i,e,t){"use strict";var n=t("2bde"),o=t.n(n);o.a},f046:function(i,e,t){"use strict";var n=t("73d0"),o=t.n(n);o.a},f864:function(i,e,t){"use strict";t.r(e);var n=t("6f0c"),o=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){t.d(e,i,(function(){return n[i]}))}(a);e["default"]=o.a}}]);