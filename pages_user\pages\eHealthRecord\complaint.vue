<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="申诉管理" />

    <view slot="gBody" class="grace-body container">

      <view class="health-check-item" v-for="(item, index) in list" :key="index">
        <!-- <view class="item-header">
          <view class="org-name">{{ item.name }}</view>
          <view class="date">{{ item.time }}</view>
        </view> -->

        <view class="item-content">
          <!-- <view class="org-name">
            <uni-icons custom-prefix="iconfont" type="hospital" size="16" color="#666"></uni-icons>
            <text>{{ item.orgName }}</text>
          </view> -->

          <view class="check-info">
            <view class="info-item">
              <text class="label">申诉单位类型：</text>
              <text class="value">{{ item.name }}</text>
            </view>
            <view class="info-item">
              <text class="label">申诉单位名称：</text>
              <text class="value">{{ getType(item.type) }}</text>
            </view>
            <view class="info-item">
              <text class="label">申诉内容：</text>
              <text class="value">{{ item.content }}</text>
            </view>
            <view class="info-item">
              <text class="label">状态：</text>
              <text class="value type-tag" :class="getConclusionClass(item.status)">{{ getStatus(item.status) }}</text>
            </view>
            <view v-if="item.status == 0" class="info-item" style="display:flex;justify-content:end;">
              <button size="mini" type="warn" class="grace-button circleBtn" @click="agree(index)">
                <text style="font-size:28rpx;width:1em;" class="grace-grids-icon grace-icons icon-remove"></text>
              </button>
            </view>

          </view>
        </view>

      </view>


      <!-- <view class="grace-table grace-margin-top">
				<view class="grace-theader grace-bg-blue">
					<text class="grace-td grace-bold header">申诉单位类型</text>
					<text class="grace-td grace-bold header">申诉单位名称</text>
					<text class="grace-td grace-bold header">申诉内容</text>
					<text class="grace-td grace-bold header">状态</text>
					<text class="grace-td grace-bold header">操作</text>
				</view>
				<view class="grace-tbody" v-for="(item, index) in list" :key="index">
					<text class="grace-td" style="font-size:10px">{{item.name}}</text>
					<text class="grace-td" style="font-size:10px">{{getType(item.type)}}</text>
					<text class="grace-td">{{item.content}}</text>
					<text class="grace-td">{{getStatus(item.status)}}</text>
					<view class="grace-td" style="display:flex;justify-content:center;gap:.75em">

            <button v-if="item.status==0" size="mini" type="warn" class="grace-button circleBtn" @click="agree(index)">
              <text style="font-size:28rpx;width:1em;" class="grace-grids-icon grace-icons icon-remove" ></text>
            </button>
            
          </view>
				</view>
			</view> -->
    </view>
  </gracePage>
</template>
<script>
export default {
  data() {
    return {
      list: [
        {
          name: '第七师胡杨河市疾病预防控制中心',
          content: '籍贯信息有误',
          date: '2025-02-23',
          type: 2,
          status: 0
        },
        {
          name: '北京智联科技有限公司',
          content: '第一段工作史的起始时间是2019年',
          date: '2024-11-22',
          type: 1,
          status: 1
        }
        // ['新疆生产建设兵团第十四师昆玉市疾病预防控制中心', '2024-08-31', 2]
      ]
    }
  },
  methods: {

    getConclusionClass(conclusion) {
      const classMap = {
        '1': 'conclusion-normal',
        '0': 'conclusion-warning',
        '2': 'conclusion-danger',
        '': 'conclusion-alert'
      };
      return classMap[conclusion] || '';
    },

    getType(type) {
      if (type == 1) {
        return '用人单位'
      } else if (type == 2) {
        return '管理单位'
      } else {
        return '未知'
      }
    },

    getStatus(status) {
      if (status == 0) {
        return '待审核'
      } else if (status == 1) {
        return '已通过'
      } else {
        return '已驳回'
      }
    },

    agree(index) {
      uni.showToast({
        title: '操作成功',
        icon: 'success'
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.header {
  height: 60rpx;

}

.grace-td {
  height: 120rpx;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 超过两行以...显示 */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 限制显示文本的行数为3行 */

}

.circleBtn {
  height: 48rpx;
  width: 48rpx;
  font-size: 16rpx;
  border-radius: 99px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0
}

.health-check-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  padding-top: 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);

  &:first-child {
    margin-top: 20rpx;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #f0f0f0;

    .date {
      font-size: 14px;
      color: #666666;
    }

    .org-name {
      display: flex;
      align-items: center;
      /* margin-bottom: 20rpx; */
      font-size: 16px;
      color: #333333;

      text {
        margin-left: 10rpx;
      }
    }
  }

  .item-content {
    padding: 20rpx 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .check-info {
    margin-top: 20rpx;

    .info-item {
      margin-bottom: 16rpx;
      font-size: 14px;
      color: #666666;

      .label {
        color: #999999;
      }
    }
  }




  .item-footer {
    margin-top: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

}

.conclusion-normal {
  color: #52c41a;
}

.conclusion-warning {
  color: #fa8c16;
}

.conclusion-danger {
  color: #f5222d;
}

.conclusion-alert {
  color: #722ed1;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}
</style>