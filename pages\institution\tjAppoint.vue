<template>
	<view class="institution">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					体检预约
					<!-- 体检未结束且未登记 -->
					<!-- 结束日期大于今日且registerStatus等于0或不存在 -->
				</view>
			</block>
		</uni-nav-bar>

		<view class="record-body">
			<view class="record-section">
				<view class="card-section">
					<view class="card" v-for="item in recordList" :key="item._id">
						<view class="title">
							<text></text>
							{{ item.physicalExamOrgName }}
						</view>
						<view class="name">
							<text class="label">体检时间</text>
							<text class="des">{{ item.reservationDate }}</text>
						</view>
						<view class="name">
							<text class="label">体检类型</text>
							<text class="des" v-if="item.examType === 0">离岗</text>
							<text class="des" v-if="item.examType === 1">岗前</text>
							<text class="des" v-if="item.examType === 2">在岗</text>
						</view>
						<view class="name">
							<text class="label">预约状态</text>
							<text class="des" v-if="item.reservationStatu === 0">未预约</text>
							<text class="des" v-if="item.reservationStatu === 1">已预约(待处理)</text>
							<text class="des" v-if="item.reservationStatu === 2">已通过</text>
							<text class="des" v-if="item.reservationStatu === 3">已拒绝</text>
						</view>
						<!-- <view class="name">
							<text class="label">登记状态</text>
							<text class="des" v-if="!item.registerStatus">未登记</text>
							<text class="des" v-if="item.registerStatus === 0">未登记</text>
							<text class="des" v-if="item.registerStatus === 1">已登记</text>
							<text class="des" v-if="item.registerStatus === 2">已总结</text>
						</view> -->
						<view class="name" v-if="item.refuseReason">
							<text class="label">拒绝原因</text>
							<text class="des">{{ item.refuseReason }}</text>
						</view>
						<view class="operaction">
							<!-- 未预约状态 -->
							<view class="edit-btn" v-if="item.reservationStatu === 0" @click="handleEdit(item)">预约
							</view>
							<!-- 已拒绝状态 -->
							<view class="edit-btn" v-if="item.reservationStatu === 3" @click="handleEdit(item)">重新预约
							</view>
							<!-- 已预约待审核状态 -->
							<view class="edit-btn" v-if="item.reservationStatu === 1" @click="handleEdit(item)">修改预约</view>
							<view class="edit-btn" v-if="item.reservationStatu === 1" @click="cancel(item)">撤回</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-popup :show="showEdit" mode="bottom" :closeable="true" @close="showEdit = false">
			<view style="width: 100%;height: 50vh;padding: 0 10px;box-sizing: border-box;">
				<u--form labelPosition="left" labelWidth='100' ref="uForm" :model="form" style="padding-top: 50px;">
					<u-form-item label="体检日期:" prop="reservationDate" borderBottom @click="showCalendar = true">
						<u--input disabledColor="#ffffff" placeholder="请选择体检日期" v-model="form.reservationDate"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</u--form>
				<u-button type="primary" text="确定" @click="handleUpdateSave"
					:disabled="!form.reservationDate || form.reservationDate === '尚未预约'"></u-button>
			</view>
		</u-popup>
		<u-calendar :show="showCalendar" mode="single" :minDate="minDate" :maxDate="maxDate" @confirm="confirm"
			@close="showCalendar = false"></u-calendar>
	</view>
</template>

<script>
import {
	mapGetters
} from 'vuex'
import healthApi from '../../api/health.js'
import userApi from '@/api/user.js'
import moment from "moment";
export default {
	data() {
		return {
			recordList: [],
			showEdit: false,
			showCalendar: false,
			form: {
				id: "",
				reservationDate: '',
				examType: -1
			},
			minDate: '',
			maxDate: ''
		}
	},
	onLoad() {
		this.getList()
	},
	computed: {
		...mapGetters(['userInfo', 'hasLogin']),
	},
	methods: {
		back() {
			uni.navigateBack()
		},
		async getList() {
			const res = await userApi.getHcAppointmentList({})
			// 去除结束日期小于当前今日
			const list = res.data.filter(item => {
				const examEndDate = moment(item.examEndDate)
				return examEndDate >= moment()
			})
			this.recordList = list.map(item => {
				item.reservationDate = item.reservationDate ? moment(item.reservationDate).format("YYYY-MM-DD") : '尚未预约'
				return item
			})
			// 去除registerStatus存在且大于0的记录
			this.recordList = this.recordList.filter(item => !item.registerStatus || item.registerStatus === 0)
		},
		handleEdit(item) {
			this.showEdit = true
			this.form.id = item._id
			this.form.examType = item.examType
			this.form.reservationDate = item.reservationDate
			this.minDate = moment(item.examStartDate).format("YYYY-MM-DD")
			this.maxDate = moment(item.examEndDate).format("YYYY-MM-DD")
		},
		cancel(item) {
			uni.showModal({
				title: '提示',
				content: '您确定要撤回预约吗？',
				success: async (res) => {
					if (res.confirm) {
						const res = await healthApi.cancelReservation({
							id: item._id
						})
						if (res.status === 200) {
							uni.showToast({
								title: "撤回成功",
								duration: 3000
							})
							this.getList()
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: "none",
								duration: 3000
							})
						}
					}
				}
			})
		},

		confirm(e) {
			this.form.reservationDate = e.toString()
			this.showCalendar = false
		},
		handleUpdateSave() {
			console.log(this.form.reservationDate)
			if (!this.form.reservationDate || this.form.reservationDate === '尚未预约') {
				uni.showToast({
					title: "请选择体检日期",
					icon: "none",
					duration: 3000
				})
				return
			}
			const params = {
				id: this.form.id,
				reservationDate: new Date(this.form.reservationDate),
			}
			healthApi.updateAppointment(params).then(res => {
				this.getList()
				uni.showToast({
					title: "修改成功",
					duration: 3000
				})
				this.showEdit = false
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.institution {
	width: 100%;
	background-color: #f6f6f6;
	padding: 0 15px;
	box-sizing: border-box;
}

.nav-left {
	display: flex;
	align-items: center;
	width: auto;
	color: #fff;

	image {
		width: 40rpx;
		height: 40rpx;
	}
}

.record-body {
	.record-section {
		margin-top: 15px;

		.card-section {
			.card {
				width: 100%;
				border-radius: 5px;
				background: #FFFFFF;
				margin-top: 12px;
				padding: 14px;
				box-sizing: border-box;
				margin-bottom: 14px;

				.title {
					font-family: PingFangSC;
					font-size: 14px;
					color: #555555;
					display: flex;
					align-items: center;
					margin-left: -14px;
					margin-bottom: 15px;

					text {
						display: inline-block;
						width: 6px;
						height: 20px;
						border-radius: 3px 3px 0px 3px;
						background: #FE3E3E;
						margin-right: 12px;
					}
				}

				.name {
					font-size: 14px;
					margin-bottom: 6px;

					.label {
						margin-right: 24px;
						color: #000;
					}

					.des {
						color: #555555;
					}
				}

				.operaction {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					margin-top: 24px;

					view {
						text-align: center;
						line-height: 33px;
						width: 81px;
						height: 33px;
						border-radius: 3px;
					}

					.cancel-btn {
						color: #3E73FE;
						border: 1px solid #3E73FE;
						text-align: center;
						line-height: 33px;
					}

					.edit-btn {
						background: #3E73FE;
						border: 1px solid #3E73FE;
						color: #fff;
						margin-left: 12px;
					}
				}
			}
		}
	}
}
</style>