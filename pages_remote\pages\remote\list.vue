<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <view slot="gHeader">
      <view class="grace-header-body">
        <view class="grace-header-content">
          <view style="width: 100%;display: flex;justify-content: center;color: azure;"> 
              <text>远程会诊</text>  
          </view>
        </view>
      </view>
    </view>


    <view slot="gBody" style="padding: 0 10px;background-color: #F8F8F8; height: 100vh;">
      <graceDialog :isTitle="false" closeBtnColor='#999' ref="fullNoticeDialog"" @closeDialog="closeDialog2">
        <view class="content2" slot="content">
         
              <view style="display: flex; width: 250px; margin-left: 20px; justify-content: space-around;align-items: center;margin-top: 50px;">
                <text style="margin-right: 20px;">选择会诊专家</text>
              
                <uni-data-select v-model="selectValue" :localdata="experts"
                @change="change"></uni-data-select>
              </view>

              <view style="display: flex; width: 250px; margin-left: 20px; justify-content: space-between;align-items: center;margin-top: 20px;">
                <text style="margin-right: 20px;">选择会诊医生</text>
      
                  <uni-data-select v-model="selectDoctor" :localdata="doctorList"
                  @change="change2"></uni-data-select>
            
              </view>

              <view style="display: flex; justify-content: space-around;align-items: center;margin-top: 20px;">
                <text>选择会诊日期</text>
                <view style="width: 140px;">
                  <uni-datetime-picker type="date" v-model="remoteDate" />
                </view>
              </view>

              <view style="display: flex; justify-content: space-around;align-items: center;margin-top: 20px;">
                <text>填写中毒物质名称</text>
                <view style="width: 140px;">
                  <u-input placeholder="请输入" v-model="poisonName"></u-input>
                </view>
              </view>

              <view style="display: flex; justify-content: space-around;align-items: center;margin-top: 20px;">
                <text>填写中毒症状</text>
                <view style="width: 140px;">
                  <u-input placeholder="请输入" v-model="symptoms"></u-input>
                </view>
              </view>

              <view style="display: flex; justify-content: space-around;align-items: center;margin-top: 20px;">
                <text>中毒时间</text>
                <view style="width: 140px;">
                  <uni-datetime-picker type="date" v-model="poisonTime" />
                </view>
              </view>
           
        </view>
        <view slot="btns" style="padding: 20px 20px;" class="grace-space-between">
					<button style="background-color: #008AFF; border: none; color: #fff; width: 80%;height: 40px;" @tap="confirmReserve">确认</button>
				</view>
		  </graceDialog>
      <view style="margin-top: 20px;">
          <button @click="reserve" style="background-color: #008AFF;color: #fff;"><text style="padding-right: 10px;font-size: 18px;">+</text>预约远程会诊</button>
      </view>
      <view style="margin-top: 10px;">
         <text style="font-weight: bold;">
           已预约会诊
         </text>
       </view>
       <view v-for="(item, index) in info" :key="index" style="background-color: #fff; padding: 10px; border-radius: 5px; margin-top: 10px;box-shadow: 0 0 5px #ccc;">
            <view>
               <text>会诊日期：</text>
               <text>{{ item.startTime }}</text>
            </view>

            <view style="margin-top: 5px;">
               <text>会诊专家：</text>
               <text>{{ item.expertName }}</text>
            </view>

            <view style="margin-top: 5px;">
               <text>会诊医生：</text>
               <text>{{ item.doctorName }}</text>
            </view>

            <view style="margin-top: 5px;">
               <text>中毒物质名称：</text>
               <text>{{ item.poisonName }}</text>
            </view>

            <view style="margin-top: 5px;">
               <text>中毒时间：</text>
               <text>{{ item.poisonTime}}</text>
            </view>

             <view style="margin-top: 10px;">
               <button style="display: flex; align-items: center;justify-content: center;" @click="goMeeting(item._id)">
                <text style="color: #008AFF;font-size: 15px;">进入远程会诊</text>
                <text style="color: #008AFF;" class="grace-text-small grace-blue grace-icons icon-arrow-right icon-left-margin"></text>
               </button> 
            </view>
        </view>
       <view style="margin-top: 10px;">
         <text style="font-weight: bold;">
           近期会诊记录
         </text>
       </view>

       <!-- 记录列表 -->
        <view v-if="history.length === 0" style="display: flex; justify-content: center;padding-bottom: 30px;">
           <text style="color:#ccc">暂无数据</text>
        </view>
        <view v-else style="padding-bottom: 30px;">
          <view v-for="(item, index) in history" :key="index" style="background-color: #fff; padding: 10px; border-radius: 5px; margin-top: 10px;box-shadow: 0 0 5px #ccc;">
            <view>
               <text>会诊日期：</text>
               <text>{{ item.startTime }}</text>
            </view>

            <view>
               <text>会诊专家：</text>
               <text>{{ item.expertName }}</text>
            </view>

            <view>
               <text>会诊医生：</text>
               <text>{{ item.doctorName }}</text>
            </view>

            <view>
               <text>会诊结论：</text>
               <text>{{ item.suggestion }}</text>
            </view>

            <view>
               <text>会诊结果：</text>
               <text style="color: #008AFF">下载</text>
            </view>
        </view>
        </view>
    </view>

   
  </gracePage>

</template>

<script>
import graceDialog from "@/graceUI/components/graceDialog.vue";
import consultationApi from '@/api/consultation'
export default {
  data() {
    return {
      userinfo:"",
      poisonName:"",
      poisonTime:"",
      symptoms:"",
      remoteDate:"",
      selectValue: '',
      selectName:"",
      selectDoctor:"",
      selectDoctorName:"",
      experts:[],
      doctorList:[],
      info:[],
      
      history: [
       
      ]
    }
  },
  methods: {
    change(val) {
      this.selectName = this.experts.filter( item => item.id === this.selectValue)[0].name
    },
    change2(val) {
      this.selectDoctorName = this.doctorList.filter( item => item._id === this.selectDoctor)[0].name
    },
    goMeeting(_id) {
      uni.navigateTo({
					url: `/pages_remote/pages/remote/meeting?id=${_id}`
			})
    },
    async confirmReserve() {
      const res = await consultationApi.reserveRemote({
        patientName: this.userinfo.name,
        patientId: this.userinfo._id,
        patientUnit: this.userinfo.company,
        phoneNumber: this.userinfo.phoneNum,
        age: this.userinfo.age,
        // patientMarriage: "未知",
        idNumber: this.userinfo.idNo,
        status: 0,
        poisonName: this.poisonName, // 中毒物质名称
        symptoms: this.symptoms, // 中毒症状
        poisonTime: this.poisonTime, // 中毒时间
        doctorId: this.selectDoctor,
        doctorName: this.selectDoctorName,
        expertId: String(this.selectValue),
        expertName: this.selectName,
        startTime: this.remoteDate,
        gender: Number(this.userinfo.gender)
      })
      this.$refs.fullNoticeDialog.hide();

      uni.showToast({
        title: '已预约会诊',
        icon: 'success',
        mask: true
      })

      this.poisonName = ''
      this.symptoms = ''
      this.poisonTime = ''
      this.remoteDate = ''
    },
    reserve() {
      this.$refs.fullNoticeDialog.open()
    },
    closeDialog2: function() {
				this.$refs.fullNoticeDialog.hide();
			},
  },
  components: {
			graceDialog,
	},
  async mounted() {
      const res = await consultationApi.getExperts();
      this.experts = res.data.exportList
      this.experts.forEach( item => {
        item.value = item.id
        item.text = item.name
      })
  
      this.doctorList = res.data.doctorList
      this.doctorList.forEach( item => {
        item.value = item._id
        item.text = item.name
      })

    const user = await consultationApi.loginVerification()
    this.userinfo = user.data

    const list = await consultationApi.reserveList()
    this.history = list.data.filter( item => item.status === 2)
    this.info = list.data.filter( item => item.status === 0 || item.status === 1)
  },
}
</script>

<style scoped lang="scss"></style>