<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="用药详情" />
    <view class="grace-body container" slot="gBody">
      <!-- <view class="navlist">
        <view
          :class="activeIndex === index ? 'active navitem' : 'navitem'"
          v-for="(item, index) in navlist"
          :key="index"
          @click="changeNav(index)"
        >
          {{ item.title }}
        </view>
      </view> -->
      <view class="medicationInfo" v-show="activeIndex === 0">
        <view class="medicationInfoItem">
          <view class="label">
            <view class="label_title">报告信息</view>
            <view class="label_btn" @click="download">下载</view>
          </view>
          <view class="content">
            <view class="item">
              <view class="itemlabel">报告时间:</view>
              <view class="itemcontent">{{ currentRecord.reportTime }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">药品种类:</view>
              <view class="itemcontent">{{ currentRecord.medicineType }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">药品名称:</view>
              <view class="itemcontent">{{ currentRecord.medicineName }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">药品价格:</view>
              <view class="itemcontent">{{ currentRecord.medicinePrice }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">用药方案:</view>
              <view class="itemcontent">{{ currentRecord.medicinePlan }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">药品起效时间:</view>
              <view class="itemcontent">{{ currentRecord.onsetTime }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">用药禁忌:</view>
              <view class="itemcontent">{{
                currentRecord.contraindications
              }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">用药注意事项:</view>
              <view class="itemcontent">{{ currentRecord.precautions }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">药物治疗的疾病:</view>
              <view class="itemcontent">{{ currentRecord.treatedDisease }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">贮存药物的正确方式:</view>
              <view class="itemcontent">{{ currentRecord.storagePractices }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">服药的事宜时间:</view>
              <view class="itemcontent">{{ currentRecord.medicationTime }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">用药适当的疗程:</view>
              <view class="itemcontent">{{ currentRecord.treatmentCourse }}</view>
            </view>
            <view class="item">
              <view class="itemlabel">潜在的不良反应:</view>
              <view class="itemcontent">{{ currentRecord.sideEffects }}</view>
            </view>
          </view>
        </view>
        <view class="medicationInfoItem">
          <view class="label">
            <view class="label_title">附件列表</view>
          </view>
          <view class="moreWay">
            <view class="item" v-for="(file, index) in fileList" :key="file.id" @click="downloadFile(file)">
              <view class="file-item">
                <view class="file-name">{{ file.fileName }}</view>
                <view class="file-download">点击下载</view>
              </view>
            </view>
            <view class="empty-tip" v-if="fileList.length === 0">暂无附件</view>
          </view>
        </view>
        <!-- <view class="medicationInfoItem">
          <view class="label">
            <view class="label_title">用药提醒</view>
          </view>
          <view class="remindWay">
            <view class="item">
              <view class="itemlabel">用药提醒</view>
              <switch @change="switch1Change" style="transform: scale(0.7)" />
            </view>
          </view>
        </view> -->
      </view>

      <view class="medicationRecord" v-show="activeIndex === 1">
        <view
          class="medicationRecordItem"
          v-for="(record, index) in medicationRecords"
          :key="index"
        >
          <view class="item">
            <view class="itemlabel">药品名称:</view>
            <view class="itemcontent">{{ record.medicineName }}</view>
          </view>
          <view class="item">
            <view class="itemlabel">用药时间:</view>
            <view class="itemcontent">{{ record.medicationTime }}</view>
          </view>
          <view class="item">
            <view class="itemlabel">药品金额:</view>
            <view class="itemcontent">{{ record.medicinePrice }}</view>
          </view>
        </view>
        <view class="btn">
          <button type="primary" @click="openPopup">添加记录</button>
        </view>
      </view>

      <uni-popup ref="popup" @change="change" mask-background-color="#0000000">
        <view class="popup-content">
          <view class="title"> 添加记录 </view>
          <uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
            <uni-forms-item
              label="药品名称"
              required
              name="DrugName"
              :label-width="80"
            >
              <uni-easyinput
                v-model="valiFormData.DrugName"
                placeholder="请输入药品名称"
              />
            </uni-forms-item>

            <uni-forms-item
              label="用药时间"
              name="datetimesingle"
              required
              :label-width="80"
            >
              <uni-datetime-picker
                type="datetime"
                v-model="valiFormData.datetimesingle"
              />
            </uni-forms-item>
            <uni-forms-item
              label="药品金额"
              required
              name="DrugPrice"
              :label-width="80"
            >
              <uni-easyinput
                v-model="valiFormData.DrugPrice"
                placeholder="请输入药品金额"
              />
            </uni-forms-item>
            <view class="btns">
              <button type="default" @click="close">取消</button>
              <button type="primary" @click="submit('valiForm')">确定</button>
            </view>
          </uni-forms>
        </view>
      </uni-popup>
      <!-- 隐藏的 Canvas -->
      <canvas
        canvas-id="downloadCanvas"
        style="position: fixed; left: -9999px; width: 300px; height: 600px"
      ></canvas>
    </view>
  </gracePage>
</template>
	
	<script>
import stationApi from '@/api/rehabStation.js'
import config from "@/common.js";
export default {
  data() {
    return {
      activeIndex: 0,
      navlist: [
        {
          title: "报告详情",
        },
        // {
        //   title: "用药记录",
        // },
      ],
      valiFormData: {
        DrugName: "",
        DrugPrice: "",
        datetimesingle: "",
      },
      rules: {
        DrugName: {
          rules: [
            {
              required: true,
              errorMessage: "药品名称不能为空",
            },
          ],
        },
        DrugPrice: {
          rules: [
            {
              required: true,
              errorMessage: "药品金额不能为空",
            },
          ],
        },
        datetimesingle: {
          rules: [
            {
              required: true,
              errorMessage: "用药时间不能为空",
            },
          ],
        },
      },
      currentRecord: {
        medicineName: '',
        reportTime: '',
        medicineType: '',
        medicinePrice: '',
        medicinePlan: '',
        onsetTime: '',
        contraindications: '',
        precautions: '',
        treatedDisease: '',
        storagePractices: '',
        medicationTime: '',
        treatmentCourse: '',
        sideEffects: '',
        url: ''
      },
      medicationRecords: [],
      fileList: [],
    };
  },
  async onLoad(options) {
    const id = parseInt(options.id);
    try {
      // 通过接口获取用药指导详情
      const data = await stationApi.medicationGuidanceDetail({ id });
      if (data.status === 200) {
        this.currentRecord = data.data;
        this.fileList = data.data.fileList || [];
      } else {
        uni.showToast({ 
          title: '获取用药指导详情失败', 
          icon: 'none' 
        });
      }
    } catch (error) {
      console.error('获取用药指导详情失败:', error);
      uni.showToast({ 
        title: '获取用药指导详情失败', 
        icon: 'none' 
      });
    }
  },
  methods: {
    download() {
      if(this.currentRecord && this.currentRecord.url){
        const domain = config.apiServer.substr(
								0,
								config.apiServer.length - 1
							);
        const url = domain + this.currentRecord.url
        console.log(url, 'url') 
        window.open(url, '_blank');
      } else {
        uni.showToast({
          title: '暂无数据',
          icon: 'none'
        });
      }
    },
    changeNav(index) {
      this.activeIndex = index;
    },
    submit(valiForm) {
      this.$refs.valiForm
        .validate()
        .then((res) => {
          // 添加新记录
          this.medicationRecords.push({
            medicineName: this.valiFormData.DrugName,
            time: this.valiFormData.datetimesingle,
            price: this.valiFormData.DrugPrice,
          });
          // 关闭弹窗并清空表单
          this.$refs.popup.close();
          this.valiFormData = {
            DrugName: "",
            DrugPrice: "",
            datetimesingle: "",
          };
          uni.showToast({ title: "添加成功", icon: "success" });
        })
        .catch((err) => {
          console.log("表单错误信息：", err);
        });
    },
    close() {
      this.$refs.popup.close();
    },
    change(e) {
      console.log(e);
    },
    openPopup() {
      this.$refs.popup.open("center");
    },
    downloadFile(file) {
      // 根据文件类型决定操作方式
      const extension = file.fileName.split('.').pop().toLowerCase();
      
      // 如果是图片类型，直接预览
      if (extension === 'jpg' || extension === 'png' || extension === 'jpeg') {
        uni.previewImage({
          current: 0,
          urls: [file.fileUrl],
          success: function(data) {
            console.log('预览成功');
          },
          fail: function(err) {
            console.log('预览失败: ' + err.errMsg);
            uni.showToast({
              title: '预览失败',
              icon: 'none'
            });
          }
        });
        return;
      }

      // #ifdef H5
      // H5平台：使用window.open在新窗口下载
      window.open(file.fileUrl, '_blank');
      // #endif

      // #ifdef APP-PLUS || MP
      // APP和小程序平台：使用downloadFile
      uni.showLoading({
        title: '文件下载中...'
      });
      
      uni.downloadFile({
        url: file.fileUrl,
        success: (res) => {
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.showToast({
              title: "下载成功",
              icon: "success"
            });
            // 使用openDocument打开文件
            uni.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: function() {
                console.log('打开文档成功');
              },
              fail: function(err) {
                console.error('打开文档失败', err);
                uni.showToast({
                  title: '打开文档失败',
                  icon: 'none'
                });
              }
            });
          } else {
            uni.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          console.error('下载失败', err);
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      });
      // #endif
    },
  },
};
</script>
	
	<style scoped lang="less">
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
  .navlist {
    display: flex;
    width: 70%;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    .navitem {
      width: 50%;
      text-align: center;
      background-color: #ececec;
      padding: 20rpx 0;
      border-radius: 6rpx;
    }
    .active {
      background-color: #fff;
      color: #2ba4d9;
      border-bottom: 7rpx solid #2ba4d9;
    }
  }
  .medicationInfo {
    margin-top: 40rpx;
    .medicationInfoItem {
      margin-bottom: 20rpx;
      .label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .label_btn {
          color: #2ba4d9;
        }
      }
      .content {
        margin-top: 20rpx;
        background-color: #fff;
        padding: 20rpx 40rpx;
        border-radius: 10rpx;
        .item {
          margin-bottom: 10rpx;
          display: flex;
          align-items: flex-end;
          .itemlabel {
            margin-right: 10rpx;
            vertical-align: bottom;
          }
          .itemcontent {
            vertical-align: bottom;
          }
        }
      }
      .moreWay {
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 10rpx;
        .item {
          margin-bottom: 10rpx;
          padding: 20rpx 40rpx;
          border-bottom: 1px solid #f4eeee;
          &:last-child {
            border-bottom: none;
          }
        }
      }
      .remindWay {
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 10rpx;
        .item {
          padding: 10rpx 10rpx 10rpx 40rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }
  .medicationRecord {
    margin-top: 40rpx;
    .medicationRecordItem {
      margin-bottom: 20rpx;
      background-color: #fff;
      padding: 20rpx 40rpx;
      border-radius: 10rpx;
      .item {
        margin-bottom: 10rpx;
        display: flex;
        align-items: center;
        .itemlabel {
          margin-right: 10rpx;
        }
      }
    }
    
  }
  @media screen and (max-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 100%;
    left: 0;
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
@media screen and (min-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 24rem;
    left: 50%;
    transform: translateX(-50%);
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
  .popup-content {
    width: 80vw;
    height: 50vh;
    background-color: #fff;
    border-radius: 10rpx;
    border: 1px solid #eee;
  }

  .popup-content .title {
    text-align: center;
    font-size: 32rpx;
    margin-bottom: 30rpx;
    font-weight: bold;
    padding: 30rpx 0;
    border-bottom: 1px solid #eee;
  }

  .uni-forms {
    padding: 30rpx;
  }

  .btns {
    position: absolute;
    bottom: 5%;
    width: 100%;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  .btns button {
    width: 40%;
    margin: 0;
    font-size: 28rpx;
  }
  
  // 文件列表样式
  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 28rpx;
  }
  .file-download {
    color: #008AFF;
    flex-shrink: 0;
    font-size: 26rpx;
  }
  .empty-tip {
    padding: 20rpx 40rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }
}
</style>