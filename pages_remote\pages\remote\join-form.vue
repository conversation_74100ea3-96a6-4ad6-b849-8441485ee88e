<template>
     <view>
        <view>
            <view class="title">APP ID</view>
            <u-input type="text" placeholder="Enter the appid" v-model="appId"></u-input>
            <view class="footer">You find your APP ID in the <a href="https://console.agora.io/projects">Agora Console</a></view>
        </view>
        <view>
            <view class="title">Token(optional)</view>
            <u-input placeholder="Enter the app token" v-model="token"></u-input>
            <view class="footer">To create a temporary token, <a href="https://console.agora.io/projects">edit your project</a> in Agora Console.</view>
        </view>
        <view>
            <view class="title">Channel Name</view>
            <u-input type="text" placeholder="Enter the channel name" v-model="channel"></u-input>
            <view class="footer">You create a channel when you create a temporary token. You guessed it, in <a href="https://console.agora.io/projects"><PERSON><PERSON><PERSON> Console</a></view>
        </view>
        <view>
            <view class="title">User ID(optional)</view>
             <u-input type="number" placeholder="Enter the user ID (number)" v-model="uid"></u-input>
        </view>
    </view>
</template>


<script>
export default {
    data() {
        return {
            appId: '',
            token: '',
            channel: '',
            uid: '',
        };
    },

    methods: {
        getValue() {
            return {
                appId: this.appId,
                token: this.token,
                channel: this.channel,
                uid: this.uid ? Number(this.uid) : null,
            };
        },
        setValue(data) {
            if (data.appId) {
                this.appId = data.appId;
            }
            if (data.token) {
                this.token = data.token;
            }
            if (data.channel) {
                this.channel = data.channel;
            }
            if (data.uid) {
                this.uid = data.uid;
            }
        },
    },
}
</script>

<style>
</style>