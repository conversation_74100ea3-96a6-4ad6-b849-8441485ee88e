<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="鉴定机构" />
		<view slot="gBody" class="grace-body section-body">
			<view class="map-body">
				<view class="search-wrap">
					<u--input v-model.trim="postData.institutionName" placeholder="请输入机构名称" clearable
						@input="handleInputChange">
						<template slot="suffix">
							<u-button @click="handleSearch" type="primary" size="mini">搜索</u-button>
						</template>
					</u--input>
				</view>
			</view>
			<view class="some-element">
				<view class="element" v-if="dataList.length">
					<view class="ele" v-for="item in dataList" :key="item.id">
						<view class="name row">
							<view class="tit">
								<text class="element-title">{{item.institutionName}}</text>
							</view>
							<view class="more" @click="goToPathDetail(item)">查看详情
								<image src="@/static/right-jt.png" mode=""></image>
							</view>
						</view>
						<view class="phone row">
							<image src="@/static/phone.png" mode=""></image>
							<text>{{item.contactPhone}}</text>
							<text>{{item.contactPerson }}</text>
						</view>
						<view class="address row">
							<image src="@/static/physical.png" mode=""></image>
							<text>{{item.address}}</text>
						</view>
						<view class="operaction">
							<view class="btn" @click="goToPathForm(item)">立即申请</view>
						</view>
					</view>
				</view>
				<u-loadmore v-else status="nomore" nomoreText="暂无数据" lineColor="#1CD29B"></u-loadmore>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import identifyApi from '@/api/identify.js'
	export default {
		data() {
			return {
				dataList: [],
				postData: {
					institutionName: '',
          type: '', //1首次鉴定,2兵团级鉴定
					pageNum: 1,
					pageSize: 100
				},
				total: 0,
				diagnosisId: '', //首次鉴定
				firstIdentificationId: '' //兵团级鉴定
			}
		},
		onLoad(options) {
			this.diagnosisId = options.diagnosisId || ''
			this.firstIdentificationId = options.firstIdentificationId || ''
		},
		created() {
			this.getList()
		},
		methods: {
			handleSearch() {
				this.getList()
			},
			async getList() {
        if(this.diagnosisId) this.postData.type = 1
        if(this.firstIdentificationId ) this.postData.type = 2
				const res = await identifyApi.getInstitutionList(this.postData)
				this.dataList = res.data.list
				this.postData.pageNum = res.data.pageNum
				this.postData.pageSize = res.data.pageSize
				this.total = res.data.total
			},
			handleInputChange(value) {
				if (value == '') {
					this.getList();
				}
			},
			goToPathDetail(item) {
				uni.navigateTo({
					url: `/pages_user/pages/identify/jgDetail?id=${item.id}`,
				})
			},
			goToPathForm(item) {
				let params = `institutionId=${item.id}&type=${item.type}`
				if (this.diagnosisId) {
					params += `&diagnosisId=${this.diagnosisId}`
				}
				if (this.firstIdentificationId) {
					params += `&firstIdentificationId=${this.firstIdentificationId}`
				}
				uni.navigateTo({
					url: `/pages_user/pages/identify/jgForm?${params}`
				})
			}
		},
	}
</script>


<style lang="scss" scoped>
	.section-body {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #f5f5f5;

		.map-body {
			margin-top: 20rpx;

			.search-wrap {
				height: 70rpx;
				border-radius: 30rpx;
				background: #FFFFFF;
			}
		}

		.some-element {
			margin-top: 20rpx;

			.element {
				border-radius: 5rpx;
				background: #FFFFFF;
				padding: 30rpx;
				box-sizing: border-box;

				.ele {
					width: 100%;
					padding: 30rpx 24rpx;
					box-sizing: border-box;
					border-radius: 8rpx;
					background: rgba(62, 115, 254, 0.06);
					margin-bottom: 24rpx;

					.row {
						margin-bottom: 22rpx;
						display: flex;
						align-items: center;
						width: 100%;

						image {
							width: 20rpx;
							height: 20rpx;
							margin-right: 16rpx;
						}

						text {
							font-family: Source Han Sans;
							font-size: 28rpx;
							color: #555555;
							margin-right: 32rpx;
						}
					}

					.name {
						width: 100%;
						position: relative;

						.element-title {
							width: 320rpx;
							display: inline-block;
							font-size: 16px;
							font-weight: bold;
							color: #3E73FE;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.tit {
							// width: 320rpx;
							// display: inline-block;
							// font-size: 16px;
							// font-weight: bold;
							// color: #3E73FE;
							// overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.more {
							font-size: 12px;
							font-weight: 300;
							color: #3E73FE;
							position: absolute;
							right: 0;
							display: flex;
							align-items: center;

							image {
								width: 32rpx;
								height: 24rpx;
							}
						}
					}

					.operaction {
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.btn {
							width: 184rpx;
							height: 64rpx;
							text-align: center;
							line-height: 64rpx;
							border-radius: 8rpx;
							background: #3E73FE;
							color: #fff;
						}
					}
				}
			}
		}
	}
</style>