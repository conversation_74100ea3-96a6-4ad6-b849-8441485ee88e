<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="服务记录" />
    <view class="grace-body container" slot="gBody">
      <view class="search">
        <view class="searchInfo" @click="openSearchPopup">
          <uni-icons type="search" size="20" color="dodgerblue"></uni-icons>
          <text>查询</text>
        </view>
      </view>
      <view class="search_content">
        <checkbox-group @change="handleCheckboxChange">
          <label v-for="item in recordsList" :key="item.id">
            <view class="records">
              <view class="text">
                <view class="label"> 服务时间: </view>
                <view class="content"> {{ item.serviceTime }} </view>
              </view>
              <view class="text">
                <view class="label"> 服务医师: </view>
                <view class="content"> {{ item.doctorName }} </view>
              </view>
              <view class="text">
                <view
                  :class="
                    item.serviceType === '线下'
                      ? 'content'
                      : 'content noaddress'
                  "
                >
                  指导方式: {{ item.serviceType }}
                </view>
              </view>
              <view class="text">
                <view class="label"> 服务地点: </view>
                <view>
                  {{ item.serviceAddress }}
                </view>
              </view>
              <view class="text">
                <view class="label"> 服务内容: </view>
                <view class="content"> {{ item.serviceItem }} </view>
              </view>
              <view class="check" v-show="isDownload">
                <checkbox
                  :value="String(item.id)"
                  color="#008aff"
                  style="transform: scale(0.7)"
                />
              </view>
            </view>
          </label>
        </checkbox-group>

        <view class="btn">
          <button type="primary" @click="download">下载</button>
        </view>
      </view>
      <canvas
        canvas-id="downloadCanvas"
        style="position: fixed; left: -9999px"
      ></canvas>

      <uni-popup
        ref="popup"
        background-color="#fff"
        @change="change"
        style="z-index: 90"
        mask-background-color="#0000000"
      >
        <view class="popup-content">
          <view class="forms">
            <view class="forms_item">
              <view class="label"> 服务时间 </view>
              <uni-datetime-picker
                v-model="formDate.range"
                type="daterange"
                rangeSeparator="至"
              />
            </view>

            <view class="forms_item">
              <view class="label"> 服务医师 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.doctorName"
                placeholder="请输入服务医师"
              ></uni-easyinput>
            </view>
            <view class="forms_btn">
              <button type="default" plain="true" @click="reset">重置</button>
              <button type="primary" @click="search">查询</button>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </gracePage>
</template>

<script>
import stationApi from "@/api/rehabStation.js";
import config from "@/common.js";
export default {
  data() {
    return {
      formDate: {
        range: "",
        doctorName: "",
      },
      isDownload: false,
      selectedIds: [],
      // 分页参数
      pageParams: {
        pageNum: 1,
        pageSize: 9999,
      },
      recordsList: [],
    };
  },
  created() {
    this.getRecoveryInfoList();
  },
  methods: {
    async getRecoveryInfoList() {
      try {
        // 构建查询参数
        const params = {
          ...this.pageParams,
          doctorName: this.formDate.doctorName || undefined,
          serviceTimeStart: this.formDate.range?.[0] || undefined,
          serviceTimeEnd: this.formDate.range?.[1] || undefined,
        };

        const data = await stationApi.recoveryInfo(params);
        if (data.status === 200) {
          this.recordsList = data.data.list;
        }
      } catch (error) {
        console.error("获取康复记录列表失败:", error);
      }
    },
    handleCheckboxChange(e) {
      console.log(e);
      this.selectedIds = e.detail.value;
    },
    async download() {
      // 构建查询参数
      const params = {
        ...this.pageParams,
          doctorName: this.formDate.doctorName || undefined,
          serviceTimeStart: this.formDate.range?.[0] || undefined,
          serviceTimeEnd: this.formDate.range?.[1] || undefined,
      };
      const {data} = await stationApi.recoveryInfoUpload(params);
      if (data.url) {
        const domain = config.apiServer.substr(0, config.apiServer.length - 1);
        const url = domain + data.url
        window.open(url, "_blank");
      } else {
        uni.showToast({
          title: "暂无数据",
          icon: "none",
        });
      }
    },
    search() {
      this.pageParams.pageNum = 1; // 重置到第一页
      this.getRecoveryInfoList();
      this.$refs.popup.close();
    },
    reset() {
      this.formDate = {
        range: "",
        recreationDoctor: "",
      };
      this.pageParams = {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime",
      };
      this.getRecoveryInfoList();
    },
    change(e) {
      console.log(e);
    },
    openSearchPopup() {
      this.$refs.popup.open("right");
    },
  },
};
</script>

<style scoped lang="less">
.btn {
  bottom: 3%;
  position: fixed;
  width: 100%;
  left: 0;

  button {
    width: 90%;
    margin: 0 auto;
    font-size: 28rpx;
    background-color: #008aff;
  }

  .btns {
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      margin: 0 30rpx;
      width: 30%;

      &:first-child {
        background-color: #fff;
        border: 1px solid #999;
      }
    }
  }
}
@media screen and (max-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 100%;
    left: 0;
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
@media screen and (min-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 24rem;
    left: 50%;
    transform: translateX(-50%);
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
}

.grace-body .search {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: column;
  color: #169bd5;
  margin-bottom: 30rpx;
}

.grace-body .search .searchInfo {
  display: flex;
  align-items: center;
}

.records {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  position: relative;

  .check {
    position: absolute;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
  }
}

.records .text {
  display: flex;
  margin-bottom: 10rpx;
  align-items: flex-end;
}

.records .text .label {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.records .text .content {
  font-size: 28rpx;
  color: #000;
}

.records .text .noaddress {
  color: #999;
}

.popup-content {
  position: relative;
  width: 70vw;
  height: 88vh;
  padding: 40rpx;
  padding-top: 120rpx;
}

.forms_item {
  margin-bottom: 20rpx;
}

.forms_item .label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.forms_item input {
  border: 1px solid #f6f6f6;
  border-radius: 5rpx;
}

.forms_btn {
  position: absolute;
  bottom: 5%;
  width: 100%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.forms_btn button {
  margin: 0;
  font-size: 28rpx;
  padding: 0 80rpx;
}

.reset_btn {
  background-color: #5b5b5b;
}

.search_btn {
  background-color: #169bd5;
}
</style>