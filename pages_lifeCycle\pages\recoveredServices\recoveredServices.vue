<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="康复指导服务" />
    <view class="grace-body container" slot="gBody">
      <view class="navtop">
        <view class="navtopItem" @click="goToAddRecoveredServices"
          >申请康复指导服务>></view
        >
        <view class="navtopItem" @click="goToRecoveredServicesRecord"
          >康复指导服务记录>></view
        >
      </view>

      <view class="tablist"> 
        
      </view>
    </view>
  </gracePage>
</template>
	  
<script>
export default {
  data() {
    return {
      valiFormData: {
        datetimesingle: "",
        physician: "",
      },
    };
  },
  methods: {
    goToAddRecoveredServices() {
      uni.navigateTo({
        url: "/pages_lifeCycle/pages/recoveredServices/addrecoveredServices",
      });
    },
    goToRecoveredServicesRecord() {
      uni.navigateTo({
        url: "/pages_lifeCycle/pages/recoveredServices/recoveredServicesRecord",
      });
    },
  },
};
</script>

<style scoped lang="less">
.grace-body {
	min-height: calc(100vh - 120rpx);
  padding: 0;
  width: 100%;
  background-color: #f6f6f6;
  .navtop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background-color: #fff;

    .navtopItem {
      width: 50%;
      color: #2ba4d9;
      padding: 20rpx 40rpx;
      text-align: center;
      border-right: 1rpx solid #000;
      &:last-child {
        border: none;
      }
    }
  }
}
</style>