<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="重点人群健康管理记录" />
		<view slot="gBody" class="grace-body container">

			<view class="section">
				<view class="section-title">健康咨询与科普记录</view>
				<view class="career-list">
					<view v-if="zxkpHistory && zxkpHistory.length > 0">
						<view v-for="(item, index) in zxkpHistory" :key="index" class="career-item">
							<view class="career-header">
								<text class="company">健康教育时间</text>
								<text class="time">{{ item.startTime }} - {{ item.endTime }}</text>
							</view>
							<view class="career-content">
								<view class="info-item">
									<text class="label">主题</text>
									<text class="value">{{ item.topic }}</text>
								</view>
								<view class="info-item">
									<text class="label">内容</text>
									<text class="value" style="max-width: 12em;">{{  item.content.join('，') }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-tip">
						<text class="grace-text-center grace-gray">暂无健康咨询与科普记录</text>
					</view>
				</view>
			</view>

			<view class="section">
				<view class="section-title">职业病康复治疗记录</view>
				<view class="career-list">
					<view v-if="kfjzHistory && kfjzHistory.length > 0">
						<view v-for="(item, index) in kfjzHistory" :key="index" class="career-item">
							<view class="career-header">
								<text class="company">日期</text>
								<text class="time">{{ item.startTime }} - {{ item.endTime }}</text>
							</view>
							<view class="career-content">
								<view class="info-item">
									<text class="label">康复机构</text>
									<text class="value">{{ item.org }}</text>
								</view>
								<view class="info-item">
									<text class="label">康复治疗内容</text>
									<text class="value" style="max-width: 12em;">{{  item.content.join('，') }}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-tip">
						<text class="grace-text-center grace-gray">暂无职业病康复治疗记录</text>
					</view>
				</view>
			</view>

		</view>
	</gracePage>

</template>
<script>
export default {
	data() {
		return {
			zxkpHistory: [
				// {
				// 	startTime: '2024-03-15',
				// 	endTime: '2024-03-15',
				// 	topic: '职业病防护知识讲座',
				// 	content: [
				// 		'长期伏案工作的颈椎保护技巧',
				// 		'正确使用人体工学椅的五个要点',
				// 		'预防鼠标手的日常锻炼方法'
				// 	]
				// },
				// {
				// 	startTime: '2024-04-20',
				// 	endTime: '2024-04-20',
				// 	topic: '心理健康管理',
				// 	content: [
				// 		'工作压力识别与缓解技巧',
				// 		'正念冥想基础训练指南',
				// 		'职场人际关系处理策略'
				// 	]
				// },
				// {
				// 	startTime: '2024-05-12',
				// 	endTime: '2024-05-12',
				// 	topic: '职业性眼病预防',
				// 	content: [
				// 		'蓝光防护屏幕使用指南',
				// 		'20-20-20护眼法则实践',
				// 		'眼科检查推荐频率及项目'
				// 	]
				// }
			],
			kfjzHistory: [
				// {
				// 	startTime: '2023-06-05',
				// 	endTime: '2023-12-15',
				// 	org: '上海市职业病康复中心',
				// 	content: [
				// 		'颈椎牵引治疗（每周2次）',
				// 		'肩颈肌肉电刺激理疗',
				// 		'职业性骨骼疾病康复训练'
				// 	]
				// },
				// {
				// 	startTime: '2024-01-10',
				// 	endTime: '2024-03-25',
				// 	org: '华东职业健康研究院',
				// 	content: [
				// 		'手部肌腱康复治疗',
				// 		'职业性腕管综合征专项训练',
				// 		'工作姿势矫正评估与指导'
				// 	]
				// }
			]
		}
	},
	methods: {

	},

}
</script>

<style lang="scss" scoped>
.grace-body {
	// height: 100%;
	// background-color: #f6f6f6 !important;
	// display: flex;
	// flex-direction: column;
	// align-items: center;

	// gap: 20rpx;
	// padding-top: 30rpx;

	.section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		margin-top: 20rpx;

		&-title {
			font-size: 16px;
			font-weight: bold;
			color: #333333;
			margin-bottom: 30rpx;
			position: relative;
			padding-left: 20rpx;
		}

		&-title::before {
			content: '';
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 6rpx;
			height: 24rpx;
			background-color: #2979ff;
			border-radius: 3rpx;
		}


		.career-item {
			border: 1px solid #eaeaea;
			border-radius: 8rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			background-color: #f9fafb;
		}

		.career-header {
			display: flex;
			justify-content: space-between;
			margin-bottom: 20rpx;
		}


		.info-item {
			width: 100%;
			margin-bottom: 20rpx;
			padding-right: 20rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.info-item.full-width {
			width: 100%;
		}

		.label {
			font-size: 14px;
			color: #666666;
			margin-right: 20rpx;
		}

		.value {
			font-size: 14px;
			color: #333333;
		}
	}
}

.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #f5f5f5;
}

.empty-tip {
	padding: 30rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>