<template>
  <gracePage headerBG="#008AFF" statusBarBG="#000">
    <my-header slot="gHeader" title="生命周期管理" />
    <view class="grace-body container" slot="gBody">
      <view class="lifeCycle">
        <lifeCycleItemVue
          v-for="(item, index) in lifeCycleItemList"
          :key="index"
          :info="item"
        >
        </lifeCycleItemVue>
      </view>
    </view>
  </gracePage>
</template>

<script>
import lifeCycleItemVue from "./lifeCycleItem.vue";
export default {
  name: "lifeCycle",
  components: {
    lifeCycleItemVue,
  },
  data() {
    return {
      lifeCycleItemList: [
        {
          icon: "/static/lifeCycle/职业病病人跟踪随访记录.png",
          title: "职业病病人跟踪随访记录",
          label: "劳动者可查看本人相关历次跟踪随访记录",
          btntext: "查看",
          path: "/pages_lifeCycle/pages/followUp/followUp",
        },
        {
          icon: "/static/lifeCycle/职业病病人就诊预约.png",
          title: "职业病病人就诊预约",
          label: "劳动者在线进行就诊预约，选择合适的医院",
          btntext: "预约",
          path: "/pages_lifeCycle/pages/Appointment/Appointment",
        },
        {
          icon: "/static/lifeCycle/职业病病人诊疗服务.png",
          title: "职业病病人诊疗服务",
          label: "劳动者可查看历次诊疗服务记录清单",
          btntext: "查看",
          path: "/pages_lifeCycle/pages/treatmentService/treatmentService",
        },
        {
          icon: "/static/lifeCycle/职业病病人用药服务.png",
          title: "职业病病人用药服务",
          label: "劳动者可以查查询药品用药规范，在线指导",
          btntext: "查询",
          path: "/pages_lifeCycle/pages/MedicationServices/MedicationServices",
        },
        {
          icon: "/static/lifeCycle/职业病病人康复指导服务.png",
          title: "职业病病人康复指导服务",
          label: "劳动者可申请一对一健康指导服务",
          btntext: "申请",
          path: "/pages_lifeCycle/pages/recoveredServices/recoveredServices",
        },
      ],
    };
  },
  methods: {},
};
</script>s

<style scoped>
.grace-body {
  min-height: 93vh;
  padding-top: 30rpx;
  background-color: #f6f6f6;
}
</style>                                             