<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="申请康复指导" />
    <view class="grace-body container" slot="gBody">
      <uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
        <uni-forms-item
          label="预约指导时间"
          name="create_date"
          required
          :label-width="110"
        >
          <uni-datetime-picker
            type="datetime"
            v-model="valiFormData.create_date"
            @change="handleDateChange"
          />
        </uni-forms-item>
        <uni-forms-item
          label="指导方式"
          required
          name="guide_type"
          :label-width="110"
        >
          <uni-data-select
            v-model="valiFormData.guide_type"
            :localdata="range"
            @change="change"
            style="background-color: #fff"
          ></uni-data-select>
        </uni-forms-item>
        <uni-forms-item
          label="康复站"
          required
          name="inst_id"
          :label-width="110"
        >
          <uni-data-select
            v-model="valiFormData.inst_id"
            :localdata="institutions"
            @change="handleInstitutionChange"
            style="background-color: #fff"
          ></uni-data-select>
        </uni-forms-item>
        <view class="btn">
          <button type="primary" @click="submit('valiForm')">立即预约</button>
        </view>
      </uni-forms>
    </view>
  </gracePage>
</template>
        
<script>
import stationApi from "@/api/rehabStation.js";

export default {
  data() {
    return {
      valiFormData: {
        create_date: "",
        guide_type: "",
        inst_id: "",
        content: "",
      },
      range: [
        { value: "online", text: "线上" },
        { value: "offline", text: "线下" },
      ],
      institutions: [],
      rules: {
        create_date: {
          rules: [
            {
              required: true,
              errorMessage: "预约时间不能为空",
            },
          ],
        },
        guide_type: {
          rules: [
            {
              required: true,
              errorMessage: "指导方式不能为空",
            },
          ],
        },
        inst_id: {
          rules: [
            {
              required: true,
              errorMessage: "康复站不能为空",
            },
          ],
        },
        content: {
          rules: [
            {
              required: true,
              errorMessage: "申请内容不能为空",
            },
          ],
        },
      },
    };
  },
  created() {
    this.getInstitutions();
  },
  methods: {
    // 获取康复站列表
    async getInstitutions() {
      try {
        const params = { ...this.pageParams };
        const res = await stationApi.station(params);
        console.log(res);
        if (res && res.data) {
          this.institutions = res.data.list.map((item) => ({
            value: item.id,
            text: item.stationName,
          }));
        }
      } catch (error) {
        console.error("获取康复站列表失败:", error);
        uni.showToast({
          title: "获取康复站列表失败",
          icon: "none",
        });
      }
    },
    handleDateChange(value) {
      // 直接使用选择的值
      if (value) {
        this.valiFormData.create_date = value;
      }
    },
    change(e) {
      console.log("指导方式变更:", e);
    },
    handleInstitutionChange(e) {
      console.log("康复站变更:", e);
    },
    submit(valiForm) {
      this.$refs.valiForm
        .validate()
        .then(async (res) => {
          try {
            const params = {
              create_date: this.valiFormData.create_date,
              guide_type: this.valiFormData.guide_type,
              inst_id: this.valiFormData.inst_id,
              content: this.valiFormData.content,
            };

            const result = await stationApi.createRehabGuideApplication(params);
            if (result.status === 200) {
              uni.showToast({
                title: "预约成功",
                icon: "success",
              });
              uni.navigateTo({
                url: "/pages_lifeCycle/pages/recoveredServices/recoveredServices",
              });
            } else {
              uni.showToast({
                title: result.message || "预约失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("预约失败:", error);
            uni.showToast({
              title: "预约失败，请重试",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.log("表单错误信息：", err);
        });
    },
  },
};
</script>
        
<style scoped lang="less">
.grace-body {
  padding-top: 30rpx;
  min-height: calc(100vh - 120rpx);
  background-color: #f6f6f6;
  .navtop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background-color: #fff;

    .navtopItem {
      width: 50%;
      color: #2ba4d9;
      padding: 20rpx 40rpx;
      text-align: center;
      border-right: 1rpx solid #000;
      &:last-child {
        border: none;
      }
    }
  }
}
@media screen and (max-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 100%;
    left: 0;
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
@media screen and (min-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 24rem;
    left: 50%;
    transform: translateX(-50%);
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
</style>