<template>
  <view :style="style">
    <div ref="videoRef" :style="{ width, height }"></div>
  </view>
</template>

<script>
export default {
  props: {
    videoTrack: {
      type: Object,
      default: null,
    },
    audioTrack: {
      type: Object,
      default: null,
    },
    config: {
      type: Object,
      default: () => ({
        mirror: false,
      }),
    },
    isLocal: {
      type: Boolean,
      default: false,
    },
    text: {
      type: [String, Number],
      default: "",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "26rem",
    },
    style: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      videoRef: null,
    };
  },
  mounted() {
    this.videoRef = this.$refs.videoRef;
    if (this.videoTrack) {
      this.videoTrack.play(this.videoRef, this.config);
    }
    if (!this.isLocal && this.audioTrack) {
      this.audioTrack.play();
    }

    this.$watch("videoTrack", (track) => {
      if (track && this.videoRef) {
        track.play(this.videoRef);
      }
    });

    this.$watch("audioTrack", (track) => {
      if (!this.isLocal && track) {
        track.play();
      }
    });
  },
  beforeDestroy() {
    if (this.videoTrack) {
      this.videoTrack.close();
    }
    if (this.audioTrack) {
      this.audioTrack.close();
    }
  },
  methods: {
    handleClick() {
      this.$emit("click");
    },
  },
};
</script>
