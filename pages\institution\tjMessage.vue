<template>
	<view class="institution">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					机构信息
				</view>
			</block>
		</uni-nav-bar>
		<view class="mes-body">
			<view class="mes-section">
				<!-- 基本信息 -->
				<view class="info-card">
					<view class="info-title">基本信息</view>
					<view class="info-group">
						<view class="info-item">
							<text class="label">机构名称：</text>
							<text class="value">{{DetailMes.name}}</text>
						</view>
						<view class="info-item">
							<text class="label">曾用名：</text>
							<text class="value">{{getFormerNames()}}</text>
						</view>
						<view class="info-item">
							<text class="label">联系人：</text>
							<text class="value">{{DetailMes.contract}}</text>
						</view>
						<view class="info-item">
							<text class="label">联系电话：</text>
							<text class="value">{{DetailMes.phoneNum}}</text>
						</view>
						<view class="info-item">
							<text class="label">注册地址：</text>
							<text class="value">{{getRegAddr()}}</text>
						</view>
						<view class="info-item">
							<text class="label">详细地址：</text>
							<text class="value">{{DetailMes.address}}</text>
						</view>
						<view class="info-item">
							<text class="label">法人代表：</text>
							<text class="value">{{DetailMes.corp}}</text>
						</view>
						<view class="info-item">
							<text class="label">统一社会信用代码：</text>
							<text class="value">{{DetailMes.organization}}</text>
						</view>
						<view class="info-item">
							<text class="label">机构类型：</text>
							<text class="value">{{getUnitNature()}}</text>
						</view>
						<view class="info-item">
							<text class="label">备案号：</text>
							<text class="value">{{DetailMes.regNo}}</text>
						</view>
						<view class="info-item">
							<text class="label">外出检查：</text>
							<text class="value">{{DetailMes.egress ? '可以' : '不可以'}}开展外出职业健康检查</text>
						</view>
						<view class="info-item">
							<text class="label">机构简介：</text>
							<text class="value">{{DetailMes.introduction }}</text>
						</view>
					</view>
				</view>

				<!-- 检查类别 -->
				<view class="info-card">
					<view class="info-title">职业健康检查类别</view>
					<view class="check-range-container">
						<div v-if="DetailMes && DetailMes.latestCheckRecord && DetailMes.latestCheckRecord.checkType && DetailMes.latestCheckRecord.checkType.length > 0">
							<div v-if="checkTypeData && checkTypeData.length > 0">
								<div v-for="(category, index) in checkTypeData" :key="index" class="check-range-item">
									<div class="check-range-title">{{ category.title }}</div>
									<div class="check-range-content">
										<p>{{ category.items.join('；') }}</p>
									</div>
								</div>
								<div class="check-range-summary">
									<p>合计：{{ checkTypeData.length }}类{{ getTotalItems() }}项。</p>
								</div>
							</div>
						</div>
						<div v-else class="check-range-empty">
							<p>暂无检测范围数据</p>
						</div>
					</view>
				</view>
			</view>
		</view>

		<view class="full-nav" v-if="options.typeShow === 'true'">
			<view class="close" @click="back">取消</view>
			<view class="sub" @click="show=true">立即预约</view>
		</view>
		<u-popup :show="show" mode="bottom" :closeable="true" @close="handleClose">
			<view style="width: 100%;height: 50vh;padding: 0 10px;box-sizing: border-box;">
				<u--form labelPosition="left" labelWidth='100' ref="uForm" :rules="rules" :model="form"
					style="padding-top: 50px;">
					<u-form-item label="体检类别:" prop="examType" borderBottom>
						<u-radio-group v-model="form.examType">
							<u-radio label="离岗" name="0"></u-radio>
							<u-radio label="岗前" name="1"></u-radio>
							<u-radio label="在岗" name="2"></u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="体检日期:" prop="checkDate" borderBottom @click="showCalendar=true">
						<u--input disabledColor="#ffffff" placeholder="请选择体检日期" v-model="form.checkDate"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</u--form>
				<u-button type="primary" text="确定" @click="saveForm"></u-button>
			</view>
		</u-popup>
		<u-calendar :show="showCalendar" mode="single" @confirm="confirm" @close="showCalendar=false"></u-calendar>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex'
	import healthApi from '../../api/health.js'
	export default {
		data() {
			return {
				show: false,
				showCalendar: false,
				options: {},
				DetailMes: {},
				checkTypeData: [], // 检查类别数据
				form: {
					id: "",
					idNumber: "",
					checkDate: '',
					examType: ''
				},
				rules: {
					examType: [{
						required: true,
						message: '请选择体检类型',
						trigger: ['blur', 'change']
					}],
					checkDate: [{
						required: true,
						message: '请选择体检日期',
						trigger: ['blur', 'change']
					}]
				}
			}
		},
		onLoad(options) {
			this.options = options
			this.getMessageDetail()
		},
		computed: {
			...mapGetters(['userInfo', 'hasLogin', ]),
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			confirm(e) {
				this.form.checkDate = e.toString()
				this.showCalendar = false;
			},
			async getMessageDetail() {
				const res = await healthApi.getOneDetail({
					id: this.options.id
				})
				this.DetailMes = res.data[0]
				this.processCheckTypeData() // 处理检查类别数据
			},
			// 处理检测范围数据
			processCheckTypeData() {
				this.checkTypeData = []

				// 如果有检测范围数据
				if (this.DetailMes && this.DetailMes.latestCheckRecord && this.DetailMes.latestCheckRecord.checkType) {
					const checkTypes = this.DetailMes.latestCheckRecord.checkType

					// 按类别分组
					const categoryMap = {}
					let categoryIndex = 1 // 类别序号

					// 收集所有类别和项目
					checkTypes.forEach(item => {
						if (item && item.length >= 2) {
							const category = item[0]
							const checkItem = item[1]

							if (!categoryMap[category]) {
								categoryMap[category] = {
									title: `${this.getChineseNumber(categoryIndex)}、${category}`,
									items: []
								}
								categoryIndex++
							}

							categoryMap[category].items.push(checkItem)
						}
					})

					// 将分组后的数据转换为数组
					for (const category in categoryMap) {
						// 为每个项目添加序号
						const numberedItems = categoryMap[category].items.map((item, index) => {
							return `${index + 1}、${item}`
						})

						this.checkTypeData.push({
							title: categoryMap[category].title,
							items: numberedItems
						})
					}
				}
			},
			// 获取中文数字
			getChineseNumber(num) {
				const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
				return num <= 10 ? chineseNumbers[num - 1] : num
			},
			// 计算总项目数
			getTotalItems() {
				let total = 0
				this.checkTypeData.forEach(category => {
					total += category.items.length
				})
				return total
			},
			// 获取检查类别
			getCheckTypes() {
				if (!this.DetailMes.latestCheckRecord || !this.DetailMes.latestCheckRecord.checkType || !this.DetailMes.latestCheckRecord.checkType.length) return [];
				// 从checkType数组中提取唯一的类别（第一个元素）
				const uniqueCategories = [...new Set(this.DetailMes.latestCheckRecord.checkType.map(item => item[0]))];
				return uniqueCategories;
			},
			// 获取曾用名
			getFormerNames() {
				if (!this.DetailMes.formerNames || !this.DetailMes.formerNames.length) return '暂无';
				return this.DetailMes.formerNames.join('、');
			},
			// 获取注册地址
			getRegAddr() {
				if (!this.DetailMes.regAddr || !this.DetailMes.regAddr.length) return '暂无';
				return this.DetailMes.regAddr.join('');
			},
			// 获取单位性质
			getUnitNature() {
				if (!this.DetailMes.latestCheckRecord || !this.DetailMes.latestCheckRecord.unitNature) return '暂无数据';
				return this.DetailMes.latestCheckRecord.unitNature;
			},
			// 最终确定预约
			saveForm() {
				if (this.hasLogin && this.userInfo) {
					this.$refs.uForm.validate().then(res => {
						this.form.id = this.options.id
						this.form.idNumber = this.userInfo.idNo
						healthApi.getappointment(this.form).then(res => {
							if (Array.isArray(res.data)) {
								uni.showToast({
									title: '预约成功',
									icon: "none",
									duration: 3000,
								});

								this.show = false;
								setTimeout(() => {
									uni.navigateTo({
										url: "/pages/institution/tjBooking"
									})
								}, 1500)
							} else {
								uni.showToast({
									title: res.data,
									icon: "none",
									duration: 3000,
								});
							}
						})
					})
				}
			},
			// 取消预约并置空字段
			handleClose() {
				this.show = false
				this.$refs.uForm.resetFields()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.institution {
		width: 100%;
		height: 100vh;
		background-color: #f6f6f6;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.nav-left {
		display: flex;
		align-items: center;
		width: auto;
		color: #fff;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.mes-body {
		width: 100%;
		background-color: #fff;
		border-radius: 5px;
		margin-top: 18px;
		padding: 20rpx;
		box-sizing: border-box;

		.mes-section {
			.info-card {
				background-color: #fff;
				border-radius: 8rpx;
				padding: 20rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				.info-title {
					font-size: 16px;
					font-weight: bold;
					color: #333;
					margin-bottom: 20rpx;
					padding-left: 20rpx;
					position: relative;

					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 6rpx;
						height: 24rpx;
						background-color: #2979ff;
						border-radius: 3rpx;
					}
				}

				.info-group {
					.info-item {
						display: flex;
						margin-bottom: 16rpx;
						font-size: 14px;

						.label {
							color: #666;
							min-width: 220rpx;
							flex-shrink: 0;
						}

						.value {
							color: #333;
							flex: 1;
							line-height: 1.5;
						}
					}
				}

				.check-range-container {
					background: #fff;
					padding: 15rpx;
					margin-bottom: 20rpx;

					.check-range-item {
						margin-bottom: 30rpx;

						.check-range-title {
							font-weight: bold;
							color: #333;
							margin-bottom: 16rpx;
							font-size: 28rpx;
						}

						.check-range-content {
							color: #666;
							line-height: 1.6;
							font-size: 28rpx;
							text-align: justify;

							p {
								margin: 0;
								padding: 0;
							}
						}
					}

					.check-range-summary {
						font-weight: bold;
						color: #333;
						margin-top: 20rpx;
						font-size: 28rpx;

						p {
							margin: 0;
							padding: 0;
						}
					}

					.check-range-empty {
						text-align: center;
						padding: 40rpx 0;
						color: #999;
						font-size: 28rpx;

						p {
							margin: 0;
							padding: 0;
						}
					}
				}
			}
		}
	}

	.full-nav {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 56px;
		background: #FFFFFF;
		box-shadow: 0px 1px 7px 1px rgba(0, 0, 0, 0.2046);
		display: flex;
		align-items: center;
		justify-content: flex-end;

		view {
			font-size: 14px;
			font-weight: 500;
			border-radius: 4px;
			margin-left: 10px;
			display: flex;
			align-items: center;
			justify-content: center;

			&.close {
				width: 74px;
				height: 32px;
				color: #909399;
				background-color: #F4F4F5;
				border: 1px solid #C7C9CC;
			}

			&.sub {
				width: 88px;
				height: 32px;
				border: 1px solid #4163E1;
				background-color: #4163E1;
				color: #fff;
				margin-right: 15px;
			}
		}
	}
</style>