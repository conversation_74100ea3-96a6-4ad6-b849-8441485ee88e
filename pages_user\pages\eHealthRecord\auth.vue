<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="授权管理" />

    <view slot="gBody" class="grace-body  container">

      <view class="search-box">
        <input type="text" v-model="searchForm.name" placeholder="请输入单位名称" />
        <view class="btn-box" style="display: flex; justify-content: space-between;">
          <button @click="handleSearch" style="width: 40%;">搜索</button>
          <button @click="reset" style="width: 40%; background: #fff; color: #007AFF;">重置</button>
        </view>
      </view>

      <template v-if="list.length > 0">

        <view class="health-check-item" v-for="(item, index) in list" :key="index">
          <view class="item-header">
            <view class="org-name">{{ item.orgName || item.EnterpriseID.cname }}</view>
          </view>

          <view class="item-content">
            <!-- <view class="org-name">
              <uni-icons custom-prefix="iconfont" type="hospital" size="16" color="#666"></uni-icons>
              <text>{{ item.orgName }}</text>
            </view> -->

            <view class="check-info">
              <view class="info-item">
                <text class="label">申请时间：</text>
                <text class="value">{{ getFormatedDate(item.createdAt) }}</text>
              </view>
              <view class="info-item">
                <text class="label">单位类型：</text>
                <text class="value">{{ getType(item.source) }}</text>
              </view>
              <view class="info-item">
                <text class="label">授权状态：</text>
                <text class="value type-tag" :class="getConclusionClass(item.status)">{{ getStatus(item.status)
                }}</text>
              </view>
            </view>
          </view>

          <view class="item-footer"
            style="display:flex;justify-content:end;gap:.5em;  height: 40rpx;  line-height: normal;">
            <button v-if="item.status == 1" size="mini" type="warn" class="grace-button circleBtn"
              @click="handle(item._id, 3)">
              <text style="font-size:28rpx;width:1em;" class="grace-grids-icon grace-icons icon-close3"></text>
            </button>
            <button v-if="item.status == 1" size="mini" type="success" class="grace-button circleBtn"
              @click="handle(item._id, 2)">
              <text style="font-size:28rpx;width:1em;" class="grace-grids-icon grace-icons icon-right"></text>
            </button>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-data">
          <view class="empty-icon">
            <text class="grace-grids-icon grace-icons icon-info" style="font-size:80rpx;"></text>
          </view>
          <view class="empty-text">暂无授权申请记录</view>
        </view>
      </template>
    </view>
  </gracePage>
</template>
<script>
import recordApi from '@/api/record.js'
import moment from "moment";

export default {
  data() {
    return {
      list: [],
      searchForm: {
        name: ''
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {

    handleSearch() {
      this.getData();
    },
    reset() {
      this.searchForm = {
        name: '',
        contract: '',
        phoneNum: ''
      }
      this.getData();
    },

    async getData() {
      const res = await recordApi.getEHealthRecordAuthList({
        ...this.searchForm
      });
      this.list = res.data
    },

    getFormatedDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

    getType(type) {
      if (type == 'qy') {
        return '用人单位'
      } else if (type == 'jg') {
        return '管理单位'
      } else {
        return '未知'
      }
    },

    getStatus(status) {
      if (status == 1) {
        return '待处理'
      } else if (status == 2) {
        return '已授权'
      } else if (status == 3) {
        return '已拒绝'
      } else {
        return '-'
      }
    },

    getConclusionClass(conclusion) {
      const classMap = {
        '2': 'conclusion-normal',
        '1': 'conclusion-warning',
        '3': 'conclusion-danger',
        '': 'conclusion-alert'
      };
      return classMap[conclusion] || '';
    },

    async handle(_id, status) {
      const res = await recordApi.handleEHealthRecordAuth({ _id, status });
      if (res.status == 200) {
        uni.showToast({
          title: '操作成功',
          icon: 'success'
        })
        this.getData()

      }
      else {
        uni.showToast({
          title: res.message,
          icon: 'none'
        })
      }
    },


  },
}
</script>
<style lang="scss" scoped>
.header {
  height: 60rpx;

}

.grace-td {
  height: 120rpx;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 超过两行以...显示 */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 限制显示文本的行数为3行 */

}

.circleBtn {
  height: 48rpx;
  width: 48rpx;
  font-size: 16rpx;
  border-radius: 99px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0
}

.health-check-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);

  &:first-child {
    margin-top: 20rpx;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #f0f0f0;

    .date {
      font-size: 14px;
      color: #666666;
    }

    .org-name {
      display: flex;
      align-items: center;
      /* margin-bottom: 20rpx; */
      font-size: 16px;
      color: #333333;

      text {
        margin-left: 10rpx;
      }
    }
  }

  .item-content {
    padding: 20rpx 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .check-info {
    margin-top: 20rpx;

    .info-item {
      margin-bottom: 16rpx;
      font-size: 14px;
      color: #666666;

      .label {
        color: #999999;
      }
    }
  }

  .item-footer {
    margin-top: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

}

.conclusion-normal {
  color: #52c41a;
}

.conclusion-warning {
  color: #fa8c16;
}

.conclusion-danger {
  color: #f5222d;
}

.conclusion-alert {
  color: #722ed1;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    margin-bottom: 20rpx;
    color: #c0c4cc;
  }

  .empty-text {
    font-size: 28rpx;
    color: #909399;
  }
}

.search-box {
  padding: 15px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20rpx;

  input {
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
  }

  button {
    height: 40px;
    background: #007AFF;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>