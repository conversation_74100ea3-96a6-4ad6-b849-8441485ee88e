<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="重点人群健康管理记录" />
    <view slot="gBody" class="grace-body container">

      <view class="grace-list-items grace-border-b" style="height:auto">
        <view class="grace-list-title">车间岗位:</view>
        <view class="grace-list-body">
          <p v-for="ele in stationinfo" :key="ele._id">
            {{ ele.workshop ? ele.workshop + '-' : '' }} {{ ele.workspace }}
          </p>
        </view>
      </view>
      <view class="grace-list-items grace-border-b">
        <view class="grace-list-title">工种:</view>
        <view class="grace-list-body">
          {{stationinfo.map(ele => ele.workType).join(' ')}}
        </view>
      </view>
      <view class="grace-list-items grace-border-b" style="height:auto">
        <view class="grace-list-title">接害因素:</view>
        <view class="grace-list-body">
          <p v-for="ele in stationinfo" :key="ele._id">
            {{ ele.harmFactors }}
          </p>
        </view>
      </view>

      <view class="grace-list-items grace-border-b">
        <view class="grace-list-title">车间危害因素检测结果:</view>
        <view class="grace-list-body">{{ checkResult.length === 0 ? '暂无信息' : '' }}</view>
        <!-- <text class="grace-list-arrow-right grace-icons icon-arrow-right"></text> -->
      </view>
      <view class="cu-item">
        <!-- <view class="content"  v-if="checkResult.length===0">暂无信息</view> -->
        <view class="grace-table grace-margin-top checkResult" v-if="checkResult.length">
          <view class="grace-theader grace-bg-blue">
            <text class="grace-td grace-bold">岗位</text>
            <text class="grace-td grace-bold">危害因素</text>
            <text class="grace-td grace-bold" style="width:10em">检测结果</text>
            <text class="grace-td grace-bold">检测日期</text>
          </view>
          <view class="grace-tbody" v-for="(item, index) in checkResult" :key="index">
            <text class="grace-td">{{ item.workType }}</text>
            <text class="grace-td">{{ item.checkProject }}</text>
            <text class="grace-td" style="width:10em">{{ item.checkResult }}</text>
            <text class="grace-td">{{ item.checkTime }}</text>
          </view>
        </view>
      </view>

    </view>
  </gracePage>

</template>
<script>
import { mapGetters } from 'vuex'
import employeeApi from '@/api/employee.js' //导入接口

export default {
  data() {
    return {
      stationinfo: [],
      checkResult: [],
      EnterpriseID: '',

    }
  },
  created() {
    this.stationInfo();
    if (this.userInfo.companyId && this.userInfo.companyStatus == 2) {
      this.EnterpriseID = this.userInfo.companyId[this.userInfo.companyId.length - 1] || '';
    }
  },
  computed: {
    ...mapGetters({
      'hasLogin': 'hasLogin',
      'userInfo': 'userInfo'
    }),
    formatTime() {
      return function (time) {
        const newTime = new Date(time);
        return moment(newTime).format('YYYY/MM/DD');
      }
    },
  },
  methods: {
    // 获取岗位信息
    async stationInfo() {
      const userInfo = this.userInfo
      // 岗位信息
      const stationinfo = await employeeApi.stationInfo2(userInfo);
      // 检测结果
      const getCheckResult = await employeeApi.getCheckResult2(userInfo);
      this.stationinfo = stationinfo.data;
      if (getCheckResult.data && getCheckResult.data.length) {
        let tempArr = [];
        getCheckResult.data.forEach(ele => {
          this.checkResult = tempArr.concat(ele.data);
          tempArr = this.checkResult;
        })
      }
    },
  },

}
</script>

<style lang="scss" scoped>
.grace-body {

  .section {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    margin-top: 20rpx;

    &-title {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 30rpx;
      position: relative;
      padding-left: 20rpx;
    }

    &-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 24rpx;
      background-color: #2979ff;
      border-radius: 3rpx;
    }


    .career-item {
      border: 1px solid #eaeaea;
      border-radius: 8rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      background-color: #f9fafb;
    }

    .career-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
    }


    .info-item {
      width: 100%;
      margin-bottom: 20rpx;
      padding-right: 20rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .info-item.full-width {
      width: 100%;
    }

    .label {
      font-size: 14px;
      color: #666666;
      margin-right: 20rpx;
    }

    .value {
      font-size: 14px;
      color: #333333;
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}
</style>