<template>
	<view class="add">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					详情
				</view>
			</block>
		</uni-nav-bar>
		<view class="add_body">
			<view class="add_content">
				<u-tabs :list="tabList" @change="handleTabChange"></u-tabs>
				<!-- 基础信息 -->
				<view class="basicFrom" v-if="curTab ==='基本信息'">
					<view class="row">
						<view class="title">
							<text>员工姓名：</text>
						</view>
						<view class="content">
							<text>{{formData.employee_name}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>性别：</text>
						</view>
						<view class="content">
							<text>{{formData.gender}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>出生年月：</text>
						</view>
						<view class="content">
							<text>{{formData.birthday}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>身份证号码：</text>
						</view>
						<view class="content">
							<text>{{formData.id_code}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>联系电话：</text>
						</view>
						<view class="content">
							<text>{{formData.employee_phone}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>家庭住址：</text>
						</view>
						<view class="content">
							<text>{{formData.employee_address}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>邮政编码：</text>
						</view>
						<view class="content">
							<text>{{formData.employee_postcode}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>工作单位：</text>
						</view>
						<view class="content">
							<text>{{formData.enterprise_name}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>联系电话(单位)：</text>
						</view>
						<view class="content">
							<text>{{formData.enterprise_phone}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>单位地址：</text>
						</view>
						<view class="content">
							<text>{{formData.enterprise_address}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>单位邮政编码：</text>
						</view>
						<view class="content">
							<text>{{formData.enterprise_postcode}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>职业、工种或工作岗位：</text>
						</view>
						<view class="content">
							<text>{{formData.employee_job}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>参加工作时间：</text>
						</view>
						<view class="content">
							<text>{{formData.employee_work_date}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>事故时间：</text>
						</view>
						<view class="content">
							<text>{{formData.accident_time}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>事故地点：</text>
						</view>
						<view class="content">
							<text>{{formData.accident_place}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>主要原因：</text>
						</view>
						<view class="content">
							<text>{{formData.main_reason}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>诊断时间：</text>
						</view>
						<view class="content">
							<text>{{formData.diagnosis_time}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>受伤部位：</text>
						</view>
						<view class="content">
							<text>{{formData.injury_part}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>职业病名称：</text>
						</view>
						<view class="content">
							<text>{{formData.disease_name}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>接触职业病危害岗位：</text>
						</view>
						<view class="content">
							<text>{{formData.exposure_post}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>接触职业病危害时间：</text>
						</view>
						<view class="content">
							<text>{{formData.exposure_time}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>受伤经过简述：</text>
						</view>
						<view class="content">
							<text>{{formData.description}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>申请事项：</text>
						</view>
						<view class="content">
							<text>{{formData.apply_matter}}</text>
						</view>
					</view>
					<view class="operator">
						<u-button size="small" type="primary" @click="handleCancel">关闭</u-button>
					</view>
				</view>
				<!-- 工伤附件 -->
				<view class="annexFile" v-else-if="curTab === '工伤附件'">
					<view class="annex">
						<view class="title">
							工伤认定申请表
						</view>
						<u--text class="file-item" type="primary" :text="item.split('/').pop()"
							v-for="item in formData.annex_application" :key="item"
							@click="handleDownloadFile(item.split('/').pop(), item)"></u--text>
					</view>
					<view class="annex">
						<view class="title">
							劳动关系证明
						</view>
						<u--text class="file-item" type="primary" :text="item.split('/').pop()"
							v-for="item in formData.annex_labor_contract" :key="item"
							@click="handleDownloadFile(item.split('/').pop(), item)"></u--text>
					</view>
					<view class="annex">
						<view class="title">
							诊断证明
						</view>
						<u--text class="file-item" type="primary" :text="item.split('/').pop()"
							v-for="item in formData.annex_certificate" :key="item"
							@click="handleDownloadFile(item.split('/').pop(), item)"></u--text>
					</view>
					<view class="annex">
						<view class="title">
							身份证复件
						</view>
						<u--text class="file-item" type="primary" :text="item.split('/').pop()"
							v-for="item in formData.annex_id" :key="item"
							@click="handleDownloadFile(item.split('/').pop(), item)"></u--text>
					</view>
					<view class="annex">
						<view class="title">
							一寸照片
						</view>
						<u--text class="file-item" type="primary" :text="item.split('/').pop()"
							v-for="item in formData.annex_photo" :key="item"
							@click="handleDownloadFile(item.split('/').pop(), item)"></u--text>
					</view>
					<view class="annex">
						<view class="title">
							调查报告
						</view>
						<u--text class="file-item" type="primary" :text="item.split('/').pop()"
							v-for="item in formData.annex_investigation" :key="item"
							@click="handleDownloadFile(item.split('/').pop(), item)"></u--text>
					</view>
					<view class="operator">
						<u-button size="small" type="primary" @click="handleCancel">关闭</u-button>
					</view>
				</view>
				<!-- 审核结果 -->
				<view class="auditResult" v-else-if="curTab === '审核结果'">
					<view class="row">
						<view class="title">
							<text>审核人：</text>
						</view>
						<view class="content">
							<text>{{formData.audit_user}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>审核时间：</text>
						</view>
						<view class="content">
							<text>{{formData.audit_time}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>审核意见：</text>
						</view>
						<view class="content">
							<text>{{formData.audit_opinion}}</text>
						</view>
					</view>
					<view class="row">
						<view class="title">
							<text>工伤认定书：</text>
						</view>
						<view class="content">
							<u--text type="primary" text="工伤认定书" v-for="item in formData.annex_recognition" :key="item"
								@click="handleDownloadFile('工伤认定书', item)"></u--text>
						</view>
					</view>
					<view class="operator">
						<u-button size="small" type="primary" @click="handleCancel">关闭</u-button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import config from '@/common.js';

	import {
		addOrEditWorkInjuryRecognition,
		getWorkInjuryRecognitionDetail,
		downloadTemplateFile,
		uploadFile,
		deleteFile
	} from '../../api/workInjuryRecognition.js'

	import {
		downloadFile
	} from "./utils.js"

	export default {
		name: 'WIRAdd',
		data() {
			return {
				tabList: [{
						name: '基本信息'
					},
					{
						name: '工伤附件'
					},
				],
				curTab: '基本信息',
				// 表单
				formData: {
					employee_name: "",
					gender: "",
					birthday: "",
					id_code: "",
					employee_phone: "",
					employee_address: "",
					employee_postcode: "",
					enterprise_name: "",
					enterprise_phone: "",
					enterprise_address: "",
					enterprise_postcode: "",
					employee_job: "",
					employee_work_date: "",
					accident_time: "",
					accident_place: "",
					main_reason: "",
					diagnosis_time: "",
					injury_part: "",
					disease_name: "",
					exposure_post: "",
					exposure_time: "",
					description: "",
					apply_matter: "",
					annex_application: [],
					annex_labor_contract: [],
					annex_certificate: [],
					annex_id: [],
					annex_photo: [],
					annex_investigation: [],
				},
			}
		},
		async onLoad(options) {
			const {
				_id
			} = options
			if (_id) { // 编辑
				const res = await getWorkInjuryRecognitionDetail({
					_id
				})
				this.formData = res.data

				// this.formData.annex_application.pop()

				if (this.formData.status === '2' || this.formData.status === '3') {
					this.tabList.push({
						name: '审核结果'
					})
				}
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			// tab 切换
			handleTabChange({
				name
			}) {
				this.curTab = name

			},
			// 取消
			handleCancel() {
				uni.navigateTo({
					url: `/pages/workInjuryRecognition/index`,
				})
			},
			// 下载文件
			handleDownloadFile(filename, url) {
				// downloadFile(filename, config.apiServer + url.replace(/^\//, ''))
				window.open(config.apiServer + url.replace(/^\//, ''))
			}
		}
	}
</script>

<style lang="scss" scoped>
	.add {
		width: 100%;
		padding: 0 30rpx;
		box-sizing: border-box;

		.nav-left {
			display: flex;
			align-items: center;
			width: auto;
			color: #fff;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		&_body {

			box-sizing: border-box;
			width: 100%;
			// height: 100vh;
			// background-color: #f6f6f6;
			display: flex;
			flex-direction: column;
			align-items: center;

			.add_content {

				width: 720rpx;
				height: 100%;

				.basicFrom {
					.row {
						margin-bottom: 22rpx;
						display: flex;
						align-items: center;
						width: 100%;


						text {
							font-family: Source Han Sans;
							font-size: 28rpx;
							color: #555555;
							margin-right: 32rpx;
						}

						.title {
							width: 340rpx;
							text-align: right;
						}

						.content {
							flex: 1;
						}
					}
				}

				.annexFile {
					.annex {
						margin-bottom: 32px;

						display: flex;
						flex-direction: column;
						gap: 12px;

						.title {
							font-size: 16px;
						}
					}
				}

				.auditResult {
					.row {
						margin-bottom: 22rpx;
						display: flex;
						align-items: center;
						width: 100%;

						text {
							font-family: Source Han Sans;
							font-size: 28rpx;
							color: #555555;
							margin-right: 32rpx;
						}

						.title {
							width: 200rpx;
							text-align: right;
						}

						.content {
							flex: 1;
						}
					}
				}

				.operator {
					display: flex;
					padding: 16px 0;
					gap: 16px;
				}
			}

		}
	}
</style>