import req from '@/utils/http.js' //导入 封装的请求
let api = {
	followupRecordList: data => {
	    return req({
	        url: 'manage/rehab/followupRecordList', 
	        method: 'get',  
			data
	    })
	},
	treatmentInformationList: data => {
	    return req({
	        url: 'manage/rehab/treatmentInformationList', 
	        method: 'get',  
			data
	    })
	},
	treatmentInformationDetail: data => {
	    return req({
	        url: 'manage/rehab/treatmentInformationDetail', 
	        method: 'get',  
			data
	    })
	},
	medicationGuidanceList: data => {
	    return req({
	        url: 'manage/rehab/medicationGuidanceList', 
	        method: 'get',  
			data
	    })
	},
	medicationGuidanceDetail: data => {
	    return req({
	        url: 'manage/rehab/medicationGuidanceDetail', 
	        method: 'get',  
			data
	    })
	},
	recoveryInfo: data => {
	    return req({
	        url: 'manage/rehab/recoveryInfo', 
	        method: 'get',  
			data
	    })
	},
	recoveryInfoUpload: data => {
	    return req({
	        url: 'manage/rehab/recoveryInfoUpload', 
	        method: 'get',  
			data
	    })
	},
	personnel: data => {
	    return req({
	        url: 'manage/rehab/personnel', 
	        method: 'get',  
			data
	    })
	},
	station: data => {
	    return req({
	        url: 'manage/rehab/station', 
	        method: 'get',  
			data
	    })
	},
	appointment: data => {
	    return req({
	        url: 'manage/rehab/appointment', 
	        method: 'get',  
			data
	    })
	},
	createAppointment: data => {
	    return req({
	        url: 'manage/rehab/createAppointment', 
	        method: 'post',  
			data
	    })
	},
	createRehabGuideApplication: data => {
	    return req({
	        url: 'manage/rehab/createRehabGuideApplication', 
	        method: 'post',  
			data
	    })
	},
  getDiseaseClassify: data => {
	    return req({
	        url: 'manage/eHealthRecord/getDiseaseClassify', 
	        method: 'get',  
			data
	    })
	},

};
export default api;  //导出