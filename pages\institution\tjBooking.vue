<template>
	<view class="institution">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					体检机构
				</view>
			</block>
		</uni-nav-bar>

		<view class="search-box">
			<input type="text" v-model="searchForm.name" placeholder="请输入机构名称" />
			<input type="text" v-model="searchForm.contract" placeholder="请输入联系人" />
			<input type="text" v-model="searchForm.phoneNum" placeholder="请输入联系电话" />
			<view class="btn-box" style="display: flex; justify-content: space-between;">
				<button @click="handleSearch" style="width: 40%;">搜索</button>
				<button @click="reset" style="width: 40%; background: #fff; color: #007AFF;">重置</button>
			</view>
		</view>

		<!-- <picker mode="multiSelector" :range="range" @change="bindTimeChange">
			<view class="nav-address">
				<button>地址：{{myregion}}</button>
			</view>
		</picker> -->

		<view class="card-body">
			<view class="card-section">
				<view class="card" v-for="item in tjMakeList" :key="item._id">
					<view class="title">
						<image src="../../static/hospital.png" mode=""></image>
						<text>{{ item.name }}</text>
					</view>
					<view class="info">
						<view class="phone">
							<image src="../../static/telephone.png"></image>
							{{ item.phoneNum }}
						</view>
						<view class="name">
							<image src="../../static/place.png"></image>
							{{ item.contract }}
						</view>
					</view>
					<view class="address">
						<image src="../../static/telephone.png"></image>
						{{ item.address }}
					</view>
					<view class="operaction">
						<view class="btn btn-look" @click="goToDetail(item, false)">查看详情</view>
						<!-- <view class="btn btn-make" @click="goToDetail(item,true)">立即预约</view> -->
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
import healthApi from '../../api/health.js'
export default {
	data() {
		return {
			range: [
				['浙江', '苏州'],
				['杭州', '南京'],
				['a', 'b']
			],
			myregion: '',
			tjMakeList: [],
			searchForm: {
				name: '',
				contract: '',
				phoneNum: ''
			}
		}
	},
	methods: {
		// 获取省市区
		bindTimeChange(e) {
			console.log(e.detail.value);
		},
		back() {
			uni.navigateBack()
		},
		handleSearch() {
			this.getList();
		},
		reset() {
			this.searchForm = {
				name: '',
				contract: '',
				phoneNum: ''
			}
			this.getList();
		},
		getList() {
			healthApi.getCheckHealthList({
				...this.searchForm
			}).then(res => {
				this.tjMakeList = res.data.list
			})
		},
		// 跳转详情页
		goToDetail(data, type) {
			uni.navigateTo({
				url: `/pages/institution/tjMessage?id=${data._id}&typeShow=${type}`,
			})
		}
	},
	onLoad() {
		this.getList()
	}
}
</script>

<style lang="scss" scoped>
.institution {
	width: 100%;
	background-color: #f6f6f6;
}

.search-box {
	padding: 15px;
	background: #fff;
	display: flex;
	flex-direction: column;
	gap: 10px;

	input {
		height: 40px;
		border: 1px solid #ddd;
		border-radius: 4px;
		padding: 0 10px;
		font-size: 14px;
	}

	button {
		height: 40px;
		background: #007AFF;
		color: #fff;
		border-radius: 4px;
		font-size: 14px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.nav-left {
	display: flex;
	align-items: center;
	width: auto;
	color: #fff;

	image {
		width: 40rpx;
		height: 40rpx;
	}
}

.card-body {
	width: 100%;
	padding: 15px;
	box-sizing: border-box;

	.card-section {
		.card {
			display: flex;
			flex-direction: column;
			width: 100%;
			padding: 15px;
			box-sizing: border-box;
			border-radius: 4px;
			background: #FFFFFF;
			box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.0372);
			margin-bottom: 15px;

			.title {
				display: flex;
				align-items: center;
				font-family: Source Han Sans;
				font-size: 16px;
				font-weight: bold;
				color: #3E73FE;

				image {
					width: 24px;
					height: 24px;
					margin-right: 6px;
				}
			}

			.info {
				display: flex;
				align-items: center;
				margin: 18px 0 15px;

				view {
					display: flex;
					align-items: center;
					font-family: PingFangSC;
					font-size: 14px;
					font-weight: normal;
					color: #555555;

					image {
						width: 13px;
						height: 13px;
						margin-right: 6px;
					}

					&.name {
						margin-left: 45px;
					}
				}
			}

			.address {
				display: flex;
				align-items: center;
				font-family: PingFangSC;
				font-size: 14px;
				font-weight: normal;
				color: #555555;
				margin-bottom: 20px;

				image {
					width: 13px;
					height: 13px;
					margin-right: 6px;
				}
			}

			.operaction {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.btn {
					margin-left: 12px;
					width: 90px;
					height: 32px;
					border-radius: 4px;
					font-size: 14px;
					display: flex;
					align-items: center;
					justify-content: center;

					&.btn-look {
						background: #F0F9EB;
						border: 1px solid #B3E09C;
						color: #67C23A;
					}

					&.btn-make {
						color: #fff;
						background: #4163E1;
					}
				}
			}
		}
	}
}
</style>