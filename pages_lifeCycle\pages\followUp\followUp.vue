<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="随访记录" />
    <view class="grace-body container" slot="gBody">
      <view class="search">
        <view class="searchInfo" @click="openSearchPopup">
          <uni-icons type="search" size="20" color="dodgerblue"></uni-icons>
          <text>查询</text>
        </view>
      </view>
      <view class="search_content">
        <view class="records" v-for="item in recordsList" :key="item.id">
          <view class="text">
            <view class="label"> 随访时间: </view>
            <view class="content"> {{ item.followupPlanRowResponse? item.followupPlanRowResponse.followupTime : '-' }} </view>
          </view>
          <view class="text">
            <view class="label"> 随访机构: </view>
            <view class="content"> {{ item.createBy }} </view>
          </view>
          <view class="text">
            <view class="label"> 随访医师: </view>
            <view class="content"> {{ item.followupPlanRowResponse? item.followupPlanRowResponse.personnelName : '-' }} </view>
          </view>
          <view class="text">
            <view class="label"> 随访内容: </view>
            <view class="content"> {{ item.followupPlanRowResponse? item.followupPlanRowResponse.followupPlanName : '-' }} </view>
          </view>
        </view>
      </view>

      <uni-popup
        ref="popup"
        background-color="#fff"
        @change="change"
        style="z-index: 90"
        mask-background-color="#0000000"
      >
        <view class="popup-content">
          <view class="forms">
            <view class="forms_item">
              <view class="label"> 随访时间 </view>
              <uni-datetime-picker
                v-model="formDate.range"
                type="daterange"
                rangeSeparator="至"
              />
            </view>
            <view class="forms_item">
              <view class="label"> 随访医师 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.physician"
                placeholder="请输入随访医师"
              ></uni-easyinput>
            </view>
            <view class="forms_btn">
              <button type="default" plain="true" @click="reset">重置</button>
              <button type="primary" @click="search">查询</button>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </gracePage>
</template>

<script>
import stationApi from '@/api/rehabStation.js'
export default {
  name: "followUp",

  data() {
    return {
      formDate: {
        range: "",
        physician: "",
      },
      // 分页参数
      pageParams: {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime"
      },
      recordsList: [],
    };
  },
  onLoad() {
    this.getRecordsList()
  },
  methods: {
    async getRecordsList() {
      try {
        // 构建查询参数
        const params = {
          ...this.pageParams,
          followupPerson: this.formDate.physician || undefined,
          fromFollowupTime: this.formDate.range?.[0] || undefined,
          toFollowupTime: this.formDate.range?.[1] || undefined
        }
        
        const data = await stationApi.followupRecordList(params)
        if (data.status === 200) {
          this.recordsList = data.data.list
        } 
      } catch (error) {
        console.error('获取随访记录列表失败:', error)
      }
    },
    search() {
      this.pageParams.pageNum = 1 // 重置到第一页
      this.getRecordsList()
      this.$refs.popup.close()
    },
    reset() {
      this.formDate = {
        range: "",
        physician: "",
      }
      this.pageParams = {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime"
      }
      this.getRecordsList()
    },
    change(e) {
      console.log(e);
    },
    openSearchPopup() {
      this.$refs.popup.open("right");
    },
  },
};
</script>

<style scoped lang="less">
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
}

.grace-body .search {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: column;
  color: #169bd5;
  margin-bottom: 30rpx;
}

.grace-body .search .searchInfo {
  display: flex;
  align-items: center;
}

.records {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 60rpx;
  margin-bottom: 20rpx;
}

.records .text {
  display: flex;
  margin-bottom: 6rpx;
  align-items: center;
}

.records .text .label {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.records .text .content {
  font-size: 28rpx;
}

.popup-content {
  position: relative;
  width: 70vw;
  height: 88vh;
  padding: 40rpx;
  padding-top: 120rpx;
}

.forms_item {
  margin-bottom: 20rpx;
}

.forms_item .label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.forms_item input {
  border: 1px solid #f6f6f6;
  border-radius: 5rpx;
}

.forms_btn {
  position: absolute;
  bottom: 5%;
  width: 100%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.forms_btn button {
  margin: 0;
  font-size: 28rpx;
  padding: 0 80rpx;
}
.reset_btn {
  background-color: #5b5b5b;
}
.search_btn {
  background-color: #169bd5;
}
</style>