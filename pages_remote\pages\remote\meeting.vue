<template>
  <view>
    <view class="main-container">
      <view class="videoLeft">
        <view class="reviewVideo">
          <view class="topStyle">xxxx</view>
          <view
            class="noJoin"
            id="noJoin"
            v-show="Object.keys(remoteUsers).length === 0"
          ></view>
          <view class="videoStyle">
            <view v-if="joined" class="localVideoStyle">
              <AgoraVideoPlayer
                :is-local="true"
                :video-track="videoTrack"
                :audio-track="audioTrack"
                :width="'8.896rem'"
                :height="'9.88625rem'"
                :style="{
                  position: 'absolute',
                  'z-index': '1',
                  right: '0',
                  top: '1.37rem',
                }"
                ref="localVideoPlayer"
              ></AgoraVideoPlayer>
            </view>
            <view
              v-if="Object.keys(remoteUsers).length"
              class="remoteVideoStyle"
            >
              <AgoraVideoPlayer
                v-for="item in remoteUsers"
                :key="item.uid"
                :video-track="item.videoTrack"
                :audio-track="item.audioTrack"
              ></AgoraVideoPlayer>
            </view>
          </view>
          <view class="bottomBtn buttons-container">
            <button
              class="el-button"
              :disabled="joined"
              @click="joinVideo"
              size="mini"
              type="text"
            >
              加入
            </button>
            <button
              class="el-button"
              :disabled="!joined"
              @click="leave"
              size="mini"
              type="text"
            >
              离开
            </button>
            <button
              class="el-button"
              :disabled="!joined"
              @click="changeCamera"
              size="mini"
              icon="el-icon-refresh"
              type="text"
            >
              切换摄像头
            </button>
          </view>
        </view>
      </view>
    </view>
    <JoinForm ref="formRef" v-show="false"></JoinForm>
  </view>
</template>

<script>
const AgoraRTC = require('@/js_sdk/Agora_Web_SDK_FULL/AgoraRTC_N-4.21.0.js')
import AgoraVideoPlayer from './agora-video-player'
import JoinForm from './join-form'
import consultationApi from '@/api/consultation'
export default {
  data() {
    return {
      client: AgoraRTC.createClient({
        mode: "rtc",
        codec: "vp8",
      }),
      joined: false,
      remoteUsers: {},
      videoTrack: null,
      audioTrack: null,
      recorder: null,
      chunks: [],
      appId: "",
      channel: "",
      token: "",
      uid: null,
      facingMode: "user",
      source: "",
      currentId:""
    };
  },
  components: {
		AgoraVideoPlayer,
    JoinForm
	},
  async mounted() {
    const res = await consultationApi.getAgoraConfig({
      consultationId: this.currentId,
      role: 0,
    })
    const data = res.data.data.data.data
    this.appId = data.appId;
    this.channel = data.channel;
    this.token = data.token;
    this.uid = Math.floor(Math.random) * 100000;
    this.$refs.formRef.setValue(data);
    window.addEventListener("beforeunload", this.handleBeforeUnload);
  },

  onLoad(options) {
    this.currentId = options.id
  },

  beforeDestroy() {
    if (this.source) {
      this.source.close();
    }
    window.removeEventListener("beforeunload", this.handleBeforeUnload);
    if (this.joined) {
      this.leave();
    }
  },

  methods: {
    handleBeforeUnload(event) {
      if (this.joined) {
        const message = "你确定要离开吗？请确保已经离开频道。";
        event.returnValue = message;
        return message;
      }
    },
    changeCamera() {
      if (this.facingMode === "user") {
        this.facingMode = "environment";
      } else {
        this.facingMode = "user";
      }
      this.videoTrack.setDevice({
        facingMode: this.facingMode,
      });
    },
    async initTracks() {
      if (this.audioTrack && this.videoTrack) {
        return;
      }
      const tracks = await Promise.all([
        AgoraRTC.createMicrophoneAudioTrack(),
        AgoraRTC.createCameraVideoTrack({
          encoderConfig: "1080p_2",
        }),
      ]);
      this.audioTrack = tracks[0];
      this.videoTrack = tracks[1];
    },
    async handleUserPublished(user, mediaType) {
      await this.client.subscribe(user, mediaType);
      this.$set(this.remoteUsers, user.uid, user);
    },
    handleUserUnpublished(user, mediaType) {
      if (mediaType === "video") {
        this.$delete(this.remoteUsers, user.uid);
      }
    },
    joinVideo() {
      this.join(this.appId, this.channel, this.token, this.uid);
    },
    async join(appId, channel, token, uid) {
      this.client.on("user-published", this.handleUserPublished);
      this.client.on("user-unpublished", this.handleUserUnpublished);

      const options = this.$refs.formRef.getValue();
      // Join a channel
      options.uid = await this.client.join(
        options.appId,
        options.channel,
        options.token || null,
        options.uid || null
      );
      await this.initTracks();
      await this.client.publish([this.videoTrack, this.audioTrack]);
      this.joined = true;
    },
    async leave() {
      if (this.videoTrack) {
        this.videoTrack.close();
        this.videoTrack = null;
      }
      if (this.audioTrack) {
        this.audioTrack.close();
        this.audioTrack = null;
      }
      this.remoteUsers = {};
      await this.client.leave();
      this.joined = false;
      this.$message.success("已离开频道!");
    },
  },
};
</script>

<style scoped lang="scss">
.reviewVideo {
  /* padding: 20px; */
  position: relative;
  /* height: 77.55vh; */
}
.topStyle {
  background-color: #000000;
  opacity: 0.6026;
}

.noJoin {
  /* width: 64.53vw; */
  height: 24.54vh;
  background-color: #000000;
  opacity: 0.5;
}

.bottomBtn {
  position: absolute;
  /* left: 20px; */
  width: 100%;
  /* height: 3vh; */
  opacity: 0.6026;
  background: #000000;
  font-size: 1rem;
}
.el-button {
  background-color: transparent;
  border: 0;
  color: #c0c4cc;
}
.el-button + .el-button,
.el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 0;
}
.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background-color: transparent;
  border: 0;
}
.el-button:hover {
  background-color: transparent;
  border: 0;
}
.buttons-container {
  display: flex;
  flex-direction: row;
  justify-content: space-around; /* 根据需要调整，space-around 在项目之间提供了均等的空间 */
  flex-wrap: wrap; /* 允许在必要时换行 */
}
</style>
