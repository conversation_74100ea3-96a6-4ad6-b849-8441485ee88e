<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="诊疗服务记录" />
    <view class="grace-body container" slot="gBody">
      <view class="search">
        <view class="searchInfo" @click="openSearchPopup">
          <uni-icons type="search" size="20" color="dodgerblue"></uni-icons>
          <text>查询</text>
        </view>
      </view>
      <view class="mechanism">
        <view class="mechanismList" v-for="item in recordsList" :key="item.id">
          <view class="left">
            <view class="text">
              <view class="label"> 诊疗时间: </view>
              <view class="content"> {{ item.treatmentDate }} </view>
            </view>
            <view class="text">
              <view class="label"> 诊疗机构: </view>
              <view class="content"> {{ item.stationName }} </view>
            </view>
            <view class="text">
              <view class="label"> 诊疗医师: </view>
              <view class="content"> {{ item.doctorName }} </view>
            </view>
            <view class="text">
              <view class="label"> 诊疗结果: </view>
              <view class="content"> {{ item.treatmentResult }} </view>
            </view>
          </view>
          <view class="right">
            <view
              class="tag tag_green"
              @click="gotoTreatmentServiceInfo(item.id)"
            >
              查看
            </view>
          </view>
        </view>
      </view>

      <uni-popup
        ref="popup"
        background-color="#fff"
        @change="change"
        style="z-index: 90"
        mask-background-color="#0000000"
      >
        <view class="popup-content">
          <view class="forms">
            <view class="forms_item">
              <view class="label"> 就诊时间 </view>
              <uni-datetime-picker
                v-model="formDate.range"
                type="daterange"
                rangeSeparator="至"
              />
            </view>
            <view class="forms_item">
              <view class="label"> 诊疗机构 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.mechanism"
                placeholder="请输入诊疗机构"
              ></uni-easyinput>
            </view>
            <view class="forms_item">
              <view class="label"> 诊疗医师 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.physician"
                placeholder="请输入诊疗医师"
              ></uni-easyinput>
            </view>
            <view class="forms_item">
              <view class="label"> 诊疗结果 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.result"
                placeholder="请输入诊疗结果"
              ></uni-easyinput>
            </view>
            <view class="forms_item">
              <view class="label"> 预约就诊时间</view>
              <uni-datetime-picker
                v-model="formDate.reservationRange"
                type="daterange"
                rangeSeparator="至"
              />
            </view>
            <view class="forms_item">
              <view class="label"> 预约诊疗机构 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.reservationMechanism"
                placeholder="请输入预约诊疗机构"
              ></uni-easyinput>
            </view>
            <view class="forms_item">
              <view class="label"> 预约诊疗医师 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.reservationPhysician"
                placeholder="请输入预约诊疗医师"
              ></uni-easyinput>
            </view>
            <view class="forms_btn">
              <button
                type="default"
                plain="true"
                class="reset_btn"
                @click="reset"
              >
                重置
              </button>
              <button type="primary" @click="search" class="search_btn">
                查询
              </button>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </gracePage>
</template>
  
<script>
import stationApi from "@/api/rehabStation.js";
export default {
  data() {
    return {
      formDate: {
        range: "",
        mechanism: "",
        physician: "",
        result: "",
        reservationRange: "",
        reservationMechanism: "",
        reservationPhysician: "",
      },
      // 分页参数
      pageParams: {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime",
      },
      recordsList: [],
    };
  },
  onLoad() {
    this.getRecordsList();
  },
  methods: {
    async getRecordsList() {
      try {
        // 构建查询参数
        const params = {
          ...this.pageParams,
          physician: this.formDate.physician || undefined,
          mechanism: this.formDate.mechanism || undefined,
          result: this.formDate.result || undefined,
          fromTreatmentTime: this.formDate.range?.[0] || undefined,
          toTreatmentTime: this.formDate.range?.[1] || undefined,
          reservationPhysician: this.formDate.reservationPhysician || undefined,
          reservationMechanism: this.formDate.reservationMechanism || undefined,
          fromReservationTime: this.formDate.reservationRange?.[0] || undefined,
          toReservationTime: this.formDate.reservationRange?.[1] || undefined,
        };

        const data = await stationApi.treatmentInformationList(params);
        if (data.status === 200) {
          this.recordsList = data.data.list;
        }
      } catch (error) {
        console.error("获取诊疗记录列表失败:", error);
      }
    },
    gotoTreatmentServiceInfo(id) {
      console.log("查看诊疗记录：", id);
      uni.navigateTo({
        url: `/pages_lifeCycle/pages/treatmentService/treatmentServiceInfo?id=${id}`,
      });
    },
    change(e) {
      console.log(e);
    },
    reset() {
      this.formDate = {
        range: "",
        mechanism: "",
        physician: "",
        result: "",
        reservationRange: "",
        reservationMechanism: "",
        reservationPhysician: "",
      };
      this.pageParams = {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime",
      };
      this.getRecordsList();
    },
    search() {
      this.pageParams.pageNum = 1; // 重置到第一页
      this.getRecordsList();
      this.$refs.popup.close();
    },
    openSearchPopup() {
      this.$refs.popup.open("right");
    },
  },
};
</script>
  
<style scoped lang="less">
@media screen and (min-width: 960px) {
  /deep/ .uni-date-range--x {
    background-color: #fff;
    position: absolute;
    top: -5rem !important;
    z-index: 999;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }
  /deep/ .uni-popper__arrow{
    display: none;
  }
}

.tag {
  padding: 10rpx 30rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
}
.tag_green {
  background-color: #21bd9f30;

  color: #21bd9f;
  border: 1px solid #21bd9f;
}
.tag_black {
  background-color: #5b5b5b30;

  color: #5b5b5b;
  border: 1px solid #5b5b5b;
}
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
}

.grace-body .search {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: column;
  color: #169bd5;
  margin-bottom: 30rpx;
}

.grace-body .search .searchInfo {
  display: flex;
  align-items: center;
}

.mechanismList {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 30rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mechanismList .left {
  flex: 1;
  margin-right: 20rpx;
  overflow: hidden; /* 隐藏超出部分 */
  white-space: nowrap; /* 禁止换行 */
  text-overflow: ellipsis; /* 使用省略号表示超出部分 */
}

.mechanismList .right {
  color: dodgerblue;
  font-size: 28rpx;
}

.mechanismList .left .text {
  display: flex;
  margin-bottom: 10rpx;
  align-items: center;
}

.mechanismList .left .text .label {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.mechanismList .left .text .content {
  font-size: 28rpx;
  overflow: hidden; /* 隐藏超出部分 */
  white-space: nowrap; /* 禁止换行 */
  text-overflow: ellipsis; /* 使用省略号表示超出部分 */
}

.popup-content {
  position: relative;
  width: 70vw;
  height: 88vh;
  padding: 40rpx;
  padding-top: 120rpx;
}

.forms_item {
  margin-bottom: 20rpx;
}

.forms_item .label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.forms_item input {
  border: 1px solid #f6f6f6;
  border-radius: 5rpx;
}

.forms_btn {
  position: absolute;
  bottom: 5%;
  width: 100%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.forms_btn button {
  margin: 0;
  font-size: 28rpx;
  padding: 0 80rpx;
}
.reset_btn {
  background-color: #5b5b5b;
}
.search_btn {
  background-color: #169bd5;
}
</style>