<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="诊疗详情" />
    <view class="grace-body container" slot="gBody">
      <view class="treatmentServiceInfo">
        <view class="label">诊疗信息</view>
        <view class="content">
          <view class="item">
            <view class="label">诊疗时间:</view>
            <view class="value">{{ currentRecord.treatmentDate }}</view>
          </view>
          <view class="item">
            <view class="label">诊疗机构:</view>
            <view class="value">{{ currentRecord.stationName }}</view>
          </view>
          <view class="item">
            <view class="label">诊疗医师:</view>
            <view class="value">{{ currentRecord.doctorName }}</view>
          </view>
          <view class="item">
            <view class="label">诊疗结果:</view>
            <view class="value">{{ currentRecord.treatmentResult }}</view>
          </view>
          <view class="item">
            <view class="label">诊疗种类:</view>
            <view class="value">{{ currentRecord.diseaseCategory }}</view>
          </view>
        </view>
        <view class="btn">
          <button type="primary" @click="download">下载</button>
        </view>
      </view>
    </view>
  </gracePage>
</template>
  
<script>
import stationApi from "@/api/rehabStation.js";
import config from "@/common.js";

export default {
  data() {
    return {
      currentRecord: null,
      recordId: null,  
    };
  },
  onLoad(options) {
    this.recordId = parseInt(options.id) || 0;
    this.fetchServiceDetail();
  },
  methods: {
    async fetchServiceDetail() {
      if (!this.recordId) {
        this.errorMsg = "参数错误，无法获取诊疗详情";
        return;
      }

      try {
        const res = await stationApi.treatmentInformationDetail({
          id: this.recordId,
        });
        this.currentRecord = res.data;
      } catch (err) {
        console.error("请求失败", err);
      }
    },
    download() {
      if (this.currentRecord && this.currentRecord.url) {
        const domain = config.apiServer.substr(0, config.apiServer.length - 1);
        const url = domain + this.currentRecord.url;
        console.log(url, "url");
        window.open(url, "_blank");
      } else {
        uni.showToast({
          title: "暂无数据",
          icon: "none",
        });
      }
    },
  },
};
</script>
  
<style scoped lang="less">
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
  position: relative;
  .treatmentServiceInfo {
    margin-bottom: 20rpx;
    .content {
      margin-top: 20rpx;
      background-color: #fff;
      border-radius: 10rpx;
      padding: 20rpx 30rpx;
      .item {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        .label {
          font-size: 28rpx;
          margin-right: 10rpx;
        }
        .value {
          font-size: 28rpx;
        }
      }
    }
  }
}
@media screen and (max-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 100%;
    left: 0;
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
@media screen and (min-width: 960px) {
  .btn {
    bottom: 3%;
    position: fixed;
    width: 24rem;
    left: 50%;
    transform: translateX(-50%);
    button {
      width: 90%;
      margin: 0 auto;
      font-size: 28rpx;
      background-color: #008aff;
    }
  }
}
</style>