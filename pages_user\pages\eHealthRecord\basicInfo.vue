<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="个人基本信息" />

    <view class="grace-body container" slot="gBody">
      <!-- <scroll-view scroll-y class="content"> -->
      <!-- 基础信息 -->
      <view class="section">
        <view class="section-title">基础信息</view>
        <view class="info-group">
          <view class="info-item">
            <text class="label">姓名</text>
            <text class="value" v-if="!isEditing">{{ form.name }}</text>
            <input v-else class="form-input" v-model="editForm.name" placeholder="请输入姓名" />
          </view>
          <view class="info-item">
            <text class="label">性别</text>
            <text class="value" v-if="!isEditing">{{
              getGender(form.gender)
            }}</text>
            <picker v-else :value="editForm.gender" :range="genderOptions" range-key="label"
              @change="handleGenderChange" class="picker-wrapper">
              <view class="form-input">{{ getGender(editForm.gender) }}</view>
            </picker>
          </view>
          <view class="info-item">
            <text class="label">年龄</text>
            <text class="value" v-if="!isEditing">{{ form.age }}</text>
            <input v-else class="form-input" v-model="editForm.age" placeholder="请输入年龄" type="number" />
          </view>
          <view class="info-item">
            <text class="label">身份证号码</text>
            <text class="value" v-if="!isEditing">{{ form.IDNum }}</text>
            <input v-else class="form-input" v-model="editForm.IDNum" placeholder="请输入身份证号码" />
          </view>
          <view class="info-item">
            <text class="label">手机号码</text>
            <text class="value" v-if="!isEditing">{{ form.phoneNum }}</text>
            <input v-else class="form-input" v-model="editForm.phoneNum" placeholder="请输入手机号码" type="number" />
          </view>
          <view class="info-item">
            <text class="label">联系电话</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("contactPhoneNum")
            }}</text>
            <input v-else class="form-input" v-model="editForm.contactPhoneNum" placeholder="请输入联系电话" type="number" />
          </view>
          <view class="info-item">
            <text class="label">
              <text class="required-star" v-if="isEditing">*</text>户籍所在地
            </text>
            <text class="value" v-if="!isEditing">{{
              getNativePlaceDisplay()
            }}</text>
            <AreaPicker v-else :value="editForm.nativePlace" :isEditing="true" :level="2" placeholder="请选择户籍所在地"
              @change="handleNativePlaceChange" />
          </view>
          <view class="info-item full-width">
            <text class="label">户籍地址</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("nativePlaceAddress")
            }}</text>
            <input v-else class="form-input" v-model="editForm.nativePlaceAddress" placeholder="请输入户籍地址" />
          </view>

          <view class="info-item">
            <text class="label">
              <text class="required-star" v-if="isEditing">*</text>常住所在地
            </text>
            <template v-if="!isEditing">
              <!-- 建设兵团特殊处理 -->
              <template v-if="isResidencePlaceCorps()">
                <view class="residence-place-wrapper">
                  <text class="value">{{ getResidencePlaceSimpleDisplay() }}</text>
                  <uni-icons :type="residencePlaceCollapsed ? 'arrowdown' : 'arrowup'" size="16" color="#2979ff"
                    @click="toggleResidencePlace" class="expand-icon" />
                </view>
              </template>
              <!-- 普通地区显示 -->
              <text v-else class="value">{{ getResidencePlaceDisplay() }}</text>
            </template>
            <AreaPicker v-else :value="editForm.residencePlace" :isEditing="true" :level="4" placeholder="请选择常住所在地"
              @change="handleResidencePlaceChange" />
          </view>
          <!-- 展开后的完整内容 - 独立行显示 -->
          <view v-if="!isEditing && isResidencePlaceCorps() && !residencePlaceCollapsed" class="info-item full-width">
            <view class="residence-place-detail">
              <text class="detail-text">{{ getResidencePlaceDisplay() }}</text>
            </view>
          </view>
          <view class="info-item full-width">
            <text class="label">常住地址</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("residenceAddress")
            }}</text>
            <input v-else class="form-input" v-model="editForm.residenceAddress" placeholder="请输入常住地址" />
          </view>
        </view>
      </view>

      <!-- 人口学与社会经济学信息 -->
      <view class="section">
        <view class="section-title">人口学与社会经济学信息</view>
        <view class="info-group">
          <view class="info-item">
            <text class="label">民族</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("nation") || "汉族"
            }}</text>
            <input v-else class="form-input" v-model="editForm.nation" placeholder="请输入民族" />
          </view>
          <view class="info-item">
            <text class="label">婚姻状况</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("maritalStatus") || form.marriage
            }}</text>
            <picker v-else :value="marriageIndex" :range="marriageOptions" @change="handleMarriageChange"
              class="picker-wrapper">
              <view class="form-input">{{
                editForm.maritalStatus || "请选择"
              }}</view>
            </picker>
          </view>
          <view class="info-item">
            <text class="label">文化程度</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("education") || "-"
            }}</text>
            <picker v-else :value="educationIndex" :range="educationOptions" @change="handleEducationChange"
              class="picker-wrapper">
              <view class="form-input">{{
                editForm.education || "请选择"
              }}</view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 职业史 -->
      <view class="section">
        <view class="section-title-bar" @click="careerCollapsed = !careerCollapsed">
          <view class="section-title">职业史</view>
          <uni-icons :type="careerCollapsed ? 'arrowdown' : 'arrowup'" size="18" color="#2979ff" />
        </view>
        <view v-show="!careerCollapsed">
          <view v-if="careerHistory && careerHistory.length > 0">
            <view class="career-list">
              <view v-for="(item, index) in careerHistory" :key="index" class="career-item">
                <view class="career-header">
                  <text class="company">{{ item.workUnit }}</text>
                  <text class="time">{{ item.entryTime }} - {{ item.leaveTime }}</text>
                </view>
                <view class="career-content">
                  <view class="info-item">
                    <text class="label">车间</text>
                    <text class="value">{{ item.workshop }}</text>
                  </view>
                  <view class="info-item">
                    <text class="label">岗位</text>
                    <text class="value">{{ item.station }}</text>
                  </view>
                  <view class="info-item">
                    <text class="label">工种</text>
                    <text class="value">{{ item.workType }}</text>
                  </view>
                  <!-- 危害因素显示 -->
                  <view class="info-item">
                    <text class="label">危害因素</text>
                    <view class="value" style="flex-wrap: wrap;display: flex;">
                      <view class="tag" v-for="factor in item.harmFactors" :key="factor._id"
                        style="margin-right: 8rpx;">
                        {{ factor.name }}
                      </view>
                    </view>
                  </view>
                </view>
                <view class="career-actions" v-if="!isEditing">
                  <button class="action-btn edit" @click="handleEditCareer(item)">
                    <uni-icons type="compose" size="16" color="#2979ff"></uni-icons>
                    <text>编辑</text>
                  </button>
                  <button class="action-btn delete" @click="handleDeleteCareer(item)">
                    <uni-icons type="trash" size="16" color="#FF4D4F"></uni-icons>
                    <text>删除</text>
                  </button>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-history"><text class="empty-text">暂无职业史记录</text></view>
          <view v-if="!isEditing" class="career-add-fab" @click.stop="handleAddCareer">
            <uni-icons type="plusempty" size="28" color="#fff" />
          </view>
        </view>
      </view>

      <!-- 职业史弹窗 -->
      <uni-popup ref="careerPopup" type="center">
        <view class="career-popup">
          <view class="popup-title">{{ isEditingCareer ? '编辑职业史' : '添加职业史' }}</view>
          <view class="popup-content">
            <view class="form-item">
              <text class="form-label">工作单位</text>
              <input class="form-input" v-model="careerForm.workUnit" placeholder="请输入工作单位" />
            </view>
            <view class="form-item">
              <text class="form-label">入职时间</text>
              <picker mode="date" :value="careerForm.entryTime" @change="handleEntryTimeChange" class="picker-wrapper">
                <view class="form-input">{{ careerForm.entryTime || '请选择入职时间' }}</view>
              </picker>
            </view>
            <view class="form-item">
              <text class="form-label">离职时间</text>
              <picker mode="date" :value="careerForm.leaveTime" @change="handleLeaveTimeChange" class="picker-wrapper">
                <view class="form-input">{{ careerForm.leaveTime || '请选择离职时间' }}</view>
              </picker>
            </view>
            <view class="form-item">
              <text class="form-label">车间</text>
              <input class="form-input" v-model="careerForm.workshop" placeholder="请输入车间" />
            </view>
            <view class="form-item">
              <text class="form-label">岗位</text>
              <input class="form-input" v-model="careerForm.station" placeholder="请输入岗位" />
            </view>
            <view class="form-item">
              <text class="form-label">工种</text>
              <input class="form-input" v-model="careerForm.workType" placeholder="请输入工种" />
            </view>
            <!-- 危害因素多选模糊查询 -->
            <view class="form-item">
              <text class="form-label">危害因素</text>
              <view class="tags-wrapper" v-if="careerForm.harmFactors && careerForm.harmFactors.length">
                <view class="tag" v-for="(factor, idx) in careerForm.harmFactors" :key="factor._id">
                  {{ factor.name }}
                  <uni-icons type="close" size="14" color="#FF4D4F" @click="removeHarmFactor(idx)" />
                </view>
              </view>
              <view class="harm-factor-input-wrapper">
                <input class="form-input" v-model="harmFactorInput" placeholder="请输入危害因素名称"
                  @input="handleHarmFactorInput" @focus="handleHarmFactorFocus" @blur="onHarmFactorBlur" />
                <view class="search-dropdown" style="position: relative;" v-if="harmFactorDropdownVisible">
                  <view v-if="isHarmFactorSearching" class="dropdown-loading">
                    <uni-icons type="spinner-cycle" size="16" color="#999999"></uni-icons>
                    <text>搜索中...</text>
                  </view>
                  <view v-else-if="harmFactorSearchList && harmFactorSearchList.length === 0" class="dropdown-empty">
                    <text>暂无相关危害因素</text>
                  </view>
                  <view v-else class="dropdown-list">
                    <view class="dropdown-item" v-for="item in harmFactorSearchList" :key="item._id"
                      @mousedown.prevent="selectHarmFactor(item)">
                      {{ item.name }} <span style="color:#999;" v-if="item.otherName">(别名:{{ item.otherName }})</span>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="popup-footer">
            <button class="popup-btn cancel" @click="closeCareerPopup">取消</button>
            <button class="popup-btn submit" @click="submitCareer">确定</button>
          </view>
        </view>
      </uni-popup>

      <!-- 职业病发生情况信息 -->
      <view class="section">
        <view class="section-title-bar" @click="diseaseCollapsed = !diseaseCollapsed">
          <view class="section-title">职业病发生情况信息</view>
          <uni-icons :type="diseaseCollapsed ? 'arrowdown' : 'arrowup'" size="18" color="#2979ff" />
        </view>
        <view v-show="!diseaseCollapsed">
          <view v-if="occupationalDiseaseRecords && occupationalDiseaseRecords.length > 0">
            <view class="career-list">
              <view v-for="(item, index) in occupationalDiseaseRecords" :key="index" class="career-item">
                <view class="career-content">
                  <view class="info-item">
                    <text class="label">诊断日期</text>
                    <text class="value">{{ formatDate(item.diagnosisDate) }}</text>
                  </view>
                  <view class="info-item">
                    <text class="label">诊断结论</text>
                    <text class="value">{{ item.diagnosisConclusionDescription }}</text>
                  </view>
                  <view class="info-item">
                    <text class="label">诊断机构</text>
                    <text class="value">{{ item.diagnosisInstitution }}</text>
                  </view>
                  <view class="info-item">
                    <text class="label">职业病</text>
                    <text class="value">{{ getOccupationalDiseaseNames(item.occupationalDisease) }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-history"><text class="empty-text">暂无职业病记录</text></view>
        </view>
      </view>

      <!-- 健康相关信息 -->
      <view class="section">
        <view class="section-title-bar" @click="allergyCollapsed = !allergyCollapsed">
          <view class="section-title">过敏史</view>
          <uni-icons :type="allergyCollapsed ? 'arrowdown' : 'arrowup'" size="18" color="#2979ff" />
        </view>
        <view v-show="!allergyCollapsed">
          <template v-if="!isEditing">
            <view v-if="getAllergyHistory().length > 0" class="history-list">
              <view v-for="(item, index) in getAllergyHistory()" :key="index" class="history-item">
                <view class="info-item"><text class="label">过敏原</text><text class="value">{{ item.allergySource
                }}</text></view>
                <view class="info-item"><text class="label">过敏症状</text><text class="value">{{ item.allergySymptoms
                }}</text></view>
                <view class="info-item"><text class="label">过敏日期</text><text class="value">{{ item.allergyDate }}</text>
                </view>
                <view class="info-item"><text class="label">处理措施</text><text class="value">{{ item.treatment }}</text>
                </view>
                <view class="info-item"><text class="label">效果记录</text><text class="value">{{ item.effectRecord
                }}</text></view>
              </view>
            </view>
            <view v-else class="empty-history"><text class="empty-text">暂无过敏史记录</text></view>
          </template>
          <template v-else>
            <view class="history-form">
              <view v-for="(item, index) in editForm.allergy" :key="index" class="history-form-item">
                <view class="history-form-header">
                  <text class="history-form-title">过敏史 {{ index + 1 }}</text>
                  <button class="delete-btn" @click.stop="removeAllergy(index)" v-if="editForm.allergy.length > 1">
                    <uni-icons type="trash" size="16" color="#FF4D4F"></uni-icons>
                  </button>
                </view>
                <view class="info-item"><text class="label">过敏原</text><input class="form-input"
                    v-model="item.allergySource" placeholder="请输入过敏原" /></view>
                <view class="info-item"><text class="label">过敏症状</text><input class="form-input"
                    v-model="item.allergySymptoms" placeholder="请输入过敏症状" /></view>
                <view class="info-item"><text class="label">过敏日期</text>
                  <picker mode="date" :value="item.allergyDate" @change="(e) => handleAllergyDateChange(e, index)"
                    class="picker-wrapper">
                    <view class="form-input">{{ item.allergyDate || '请选择过敏日期' }}</view>
                  </picker>
                </view>
                <view class="info-item"><text class="label">处理措施</text><input class="form-input"
                    v-model="item.treatment" placeholder="请输入处理措施" /></view>
                <view class="info-item"><text class="label">效果记录</text><input class="form-input"
                    v-model="item.effectRecord" placeholder="请输入效果记录" /></view>
              </view>
              <button class="add-history-btn" @click.stop="addAllergy">
                <uni-icons type="plusempty" size="16" color="#2979ff"></uni-icons>
                <text>添加过敏史</text>
              </button>
            </view>
          </template>
        </view>
      </view>

      <view class="section">
        <view class="section-title-bar" @click="pastHistoryCollapsed = !pastHistoryCollapsed">
          <view class="section-title">既往史</view>
          <uni-icons :type="pastHistoryCollapsed ? 'arrowdown' : 'arrowup'" size="18" color="#2979ff" />
        </view>
        <view v-show="!pastHistoryCollapsed">
          <template v-if="!isEditing">
            <view v-if="getPastHistory().length > 0" class="history-list">
              <view v-for="(item, index) in getPastHistory()" :key="index" class="history-item">
                <view class="info-item"><text class="label">疾病名称</text><text class="value">{{ item.diseaseName }}</text>
                </view>
                <view class="info-item"><text class="label">诊断日期</text><text class="value">{{ item.diagnosisDate
                }}</text></view>
                <view class="info-item"><text class="label">机构名称</text><text class="value">{{ item.institutionName
                }}</text></view>
                <view class="info-item"><text class="label">治疗经过</text><text class="value">{{ item.treatmentProcess
                }}</text></view>
                <view class="info-item"><text class="label">转归情况</text><text class="value">{{
                  getOutcomeText(item.outcomeCode) }}</text></view>
              </view>
            </view>
            <view v-else class="empty-history"><text class="empty-text">暂无既往史记录</text></view>
          </template>
          <template v-else>
            <view class="history-form">
              <view v-for="(item, index) in editForm.pastHistory" :key="index" class="history-form-item">
                <view class="history-form-header">
                  <text class="history-form-title">既往史 {{ index + 1 }}</text>
                  <button class="delete-btn" @click.stop="removePastHistory(index)"
                    v-if="editForm.pastHistory.length > 1">
                    <uni-icons type="trash" size="16" color="#FF4D4F"></uni-icons>
                  </button>
                </view>
                <view class="info-item"><text class="label">疾病名称</text><input class="form-input"
                    v-model="item.diseaseName" placeholder="请输入疾病名称" /></view>
                <view class="info-item"><text class="label">诊断日期</text>
                  <picker mode="date" :value="item.diagnosisDate" @change="(e) => handlePastHistoryDateChange(e, index)"
                    class="picker-wrapper">
                    <view class="form-input">{{ item.diagnosisDate || '请选择诊断日期' }}</view>
                  </picker>
                </view>
                <view class="info-item"><text class="label">机构名称</text><input class="form-input"
                    v-model="item.institutionName" placeholder="请输入机构名称" /></view>
                <view class="info-item"><text class="label">治疗经过</text><input class="form-input"
                    v-model="item.treatmentProcess" placeholder="请输入治疗经过" /></view>
                <view class="info-item"><text class="label">转归情况</text>
                  <picker :value="getOutcomeIndex(item.outcomeCode)" :range="outcomeOptions"
                    @change="(e) => handleOutcomeChange(e, index)" class="picker-wrapper">
                    <view class="form-input">{{ getOutcomeText(item.outcomeCode) || '请选择转归情况' }}</view>
                  </picker>
                </view>
              </view>
              <button class="add-history-btn" @click.stop="addPastHistory">
                <uni-icons type="plusempty" size="16" color="#2979ff"></uni-icons>
                <text>添加既往史</text>
              </button>
            </view>
          </template>
        </view>
      </view>

      <view class="section">
        <view class="section-title-bar" @click="familyHistoryCollapsed = !familyHistoryCollapsed">
          <view class="section-title">家族史</view>
          <uni-icons :type="familyHistoryCollapsed ? 'arrowdown' : 'arrowup'" size="18" color="#2979ff" />
        </view>
        <view v-show="!familyHistoryCollapsed">
          <template v-if="!isEditing">
            <view v-if="getFamilyHistory().length > 0" class="history-list">
              <view v-for="(item, index) in getFamilyHistory()" :key="index" class="history-item">
                <view class="info-item"><text class="label">家族成员</text><text class="value">{{ item.familyMember
                }}</text></view>
                <view class="info-item"><text class="label">疾病名称</text><text class="value">{{ item.diseaseName }}</text>
                </view>
                <view class="info-item"><text class="label">诊断日期</text><text class="value">{{ item.diagnosisDate
                }}</text></view>
                <view class="info-item"><text class="label">机构名称</text><text class="value">{{ item.institutionName
                }}</text></view>
                <view class="info-item"><text class="label">治疗经过</text><text class="value">{{ item.treatmentProcess
                }}</text></view>
              </view>
            </view>
            <view v-else class="empty-history"><text class="empty-text">暂无家族史记录</text></view>
          </template>
          <template v-else>
            <view class="history-form">
              <view v-for="(item, index) in editForm.familyHistory" :key="index" class="history-form-item">
                <view class="history-form-header">
                  <text class="history-form-title">家族史 {{ index + 1 }}</text>
                  <button class="delete-btn" @click.stop="removeFamilyHistory(index)"
                    v-if="editForm.familyHistory.length > 1">
                    <uni-icons type="trash" size="16" color="#FF4D4F"></uni-icons>
                  </button>
                </view>
                <view class="info-item"><text class="label">家族成员</text><input class="form-input"
                    v-model="item.familyMember" placeholder="请输入家族成员" /></view>
                <view class="info-item"><text class="label">疾病名称</text><input class="form-input"
                    v-model="item.diseaseName" placeholder="请输入疾病名称" /></view>
                <view class="info-item"><text class="label">诊断日期</text>
                  <picker mode="date" :value="item.diagnosisDate"
                    @change="(e) => handleFamilyHistoryDateChange(e, index)" class="picker-wrapper">
                    <view class="form-input">{{ item.diagnosisDate || '请选择诊断日期' }}</view>
                  </picker>
                </view>
                <view class="info-item"><text class="label">机构名称</text><input class="form-input"
                    v-model="item.institutionName" placeholder="请输入机构名称" /></view>
                <view class="info-item"><text class="label">治疗经过</text><input class="form-input"
                    v-model="item.treatmentProcess" placeholder="请输入治疗经过" /></view>
              </view>
              <button class="add-history-btn" @click.stop="addFamilyHistory">
                <uni-icons type="plusempty" size="16" color="#2979ff"></uni-icons>
                <text>添加家族史</text>
              </button>
            </view>
          </template>
        </view>
      </view>

      <!-- 健康行为 -->
      <view class="section">
        <view class="section-title">健康相关信息</view>
        <view class="info-group">
          <view class="info-item">
            <text class="label" style="margin-right: 1em;">血型</text>
            <text class="value" v-if="!isEditing">{{
              getBasicInfoValue("bloodType") || "-"
            }}</text>
            <picker v-else :value="bloodTypeIndex" :range="bloodTypeOptions" @change="handleBloodTypeChange"
              class="picker-wrapper">
              <view class="form-input">{{
                editForm.bloodType || "请选择"
              }}</view>
            </picker>
          </view>
        </view>
        <view class="sub-section">
          <view class="sub-title">吸烟史</view>
          <view class="info-group">
            <view class="info-item">
              <text class="label">吸烟情况</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("smokingHistory")
              }}</text>
              <picker v-else :value="smokingHistoryIndex" :range="smokingOptions" @change="handleSmokingHistoryChange"
                class="picker-wrapper">
                <view class="form-input">{{
                  editForm.smokingHistory || "请选择"
                }}</view>
              </picker>
            </view>
            <view class="info-item">
              <text class="label">吸烟数量</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("smokingAmount")
              }}</text>
              <input v-else class="form-input" v-model="editForm.smokingAmount" placeholder="请输入吸烟数量" />
            </view>
            <view class="info-item">
              <text class="label">吸烟年限</text>
              <text class="value" v-if="!isEditing">{{ getSmokeYears() }}</text>
              <view v-else class="form-input-group">
                <input class="form-input form-input-half" v-model="editForm.smokingYear" placeholder="年"
                  type="number" />
                <input class="form-input form-input-half" v-model="editForm.smokingMonth" placeholder="月"
                  type="number" />
              </view>
            </view>
          </view>
        </view>

        <view class="sub-section">
          <view class="sub-title">饮酒史</view>
          <view class="info-group">
            <view class="info-item">
              <text class="label">饮酒情况</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("drinkingHistory")
              }}</text>
              <picker v-else :value="drinkingHistoryIndex" :range="drinkingOptions"
                @change="handleDrinkingHistoryChange" class="picker-wrapper">
                <view class="form-input">{{
                  editForm.drinkingHistory || "请选择"
                }}</view>
              </picker>
            </view>
            <view class="info-item">
              <text class="label">饮酒量</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("drinkingAmount")
              }}</text>
              <input v-else class="form-input" v-model="editForm.drinkingAmount" placeholder="请输入饮酒量" />
            </view>
            <view class="info-item">
              <text class="label">饮酒年限</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("drinkingYear")
              }}</text>
              <input v-else class="form-input" v-model="editForm.drinkingYear" placeholder="请输入饮酒年限" />
            </view>
          </view>
        </view>

        <view class="sub-section" v-if="form.gender == '1'">
          <view class="sub-title">生育史</view>
          <view class="info-group">
            <view class="info-item">
              <text class="label">现有子女数</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("currentChildren") || "0"
              }}</text>
              <input v-else class="form-input" v-model="editForm.currentChildren" placeholder="请输入现有子女数"
                type="number" />
            </view>
            <view class="info-item">
              <text class="label">流产数</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("abortion") || "0"
              }}</text>
              <input v-else class="form-input" v-model="editForm.abortion" placeholder="请输入流产数" type="number" />
            </view>
            <view class="info-item">
              <text class="label">死产数</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("stillbirth") || "0"
              }}</text>
              <input v-else class="form-input" v-model="editForm.stillbirth" placeholder="请输入死产数" type="number" />
            </view>
            <view class="info-item">
              <text class="label">早产数</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("premature") || "0"
              }}</text>
              <input v-else class="form-input" v-model="editForm.premature" placeholder="请输入早产数" type="number" />
            </view>
            <view class="info-item">
              <text class="label">异常胎数</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("abnormalFetus") || "0"
              }}</text>
              <input v-else class="form-input" v-model="editForm.abnormalFetus" placeholder="请输入异常胎数" type="number" />
            </view>
            <view class="info-item full-width">
              <text class="label">子女健康状况</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("childrenHealth") || "暂无记录"
              }}</text>
              <textarea v-else class="form-textarea" v-model="editForm.childrenHealth" placeholder="请输入子女健康状况" />
            </view>
          </view>
        </view>

        <view class="sub-section" v-if="form.gender == '1'">
          <view class="sub-title">月经史</view>
          <view class="info-group">
            <view class="info-item">
              <text class="label">初潮年龄</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("menarcheAge") || "暂无记录"
              }}</text>
              <input v-else class="form-input" v-model="editForm.menarcheAge" placeholder="请输入初潮年龄" type="number" />
            </view>
            <view class="info-item">
              <text class="label">经期（天）</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("menstruationDays") || "暂无记录"
              }}</text>
              <input v-else class="form-input" v-model="editForm.menstruationDays" placeholder="请输入经期天数"
                type="number" />
            </view>
            <view class="info-item">
              <text class="label">周期（天）</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("menstruationCycle") || "暂无记录"
              }}</text>
              <input v-else class="form-input" v-model="editForm.menstruationCycle" placeholder="请输入周期天数"
                type="number" />
            </view>
            <view class="info-item">
              <text class="label">绝经年龄</text>
              <text class="value" v-if="!isEditing">{{
                getBasicInfoValue("menopauseAge") || "暂无记录"
              }}</text>
              <input v-else class="form-input" v-model="editForm.menopauseAge" placeholder="请输入绝经年龄" type="number" />
            </view>
          </view>
        </view>

        <view class="sub-section">
          <view class="sub-title">运动习惯</view>
          <view class="info-item full-width">
            <text class="value" style="text-align: left" v-if="!isEditing">{{
              getBasicInfoValue("exerciseHabit") || "暂无运动习惯记录"
            }}</text>
            <textarea v-else class="form-textarea" v-model="editForm.exerciseHabit"
              placeholder="请输入您的运动习惯描述，如频率、时长、运动类型等" />
          </view>
        </view>
      </view>

      <!-- 紧急联系人 -->
      <view class="section">
        <view class="section-title-bar" @click="emergencyContactCollapsed = !emergencyContactCollapsed">
          <view class="section-title">紧急联系人</view>
          <uni-icons :type="emergencyContactCollapsed ? 'arrowdown' : 'arrowup'" size="18" color="#2979ff" />
        </view>
        <view v-show="!emergencyContactCollapsed">
          <template v-if="!isEditing">
            <view v-if="getEmergencyContacts().length > 0">
              <view class="contact-list">
                <view class="contact-item" v-for="(contact, index) in getEmergencyContacts()" :key="index">
                  <view class="contact-header">
                    <text class="contact-name">{{ contact.name }}</text>
                    <text class="contact-relation">{{ contact.relationship }}</text>
                  </view>
                  <text class="contact-phone">{{ contact.phoneNum }}</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-history"><text class="empty-text">暂无紧急联系人信息</text></view>
          </template>
          <template v-else>
            <view class="emergency-contact-form">
              <view class="contact-form-item" v-for="(contact, index) in editForm.emergencyContacts" :key="index">
                <view class="contact-form-header">
                  <text class="contact-form-title">联系人 {{ index + 1 }}</text>
                  <button class="delete-btn" @click="removeEmergencyContact(index)"
                    v-if="editForm.emergencyContacts.length > 1">
                    <uni-icons type="trash" size="16" color="#FF4D4F"></uni-icons>
                  </button>
                </view>
                <view class="info-item">
                  <text class="label">姓名</text>
                  <input class="form-input" v-model="contact.name" placeholder="请输入联系人姓名" />
                </view>
                <view class="info-item">
                  <text class="label">关系</text>
                  <input class="form-input" v-model="contact.relationship" placeholder="请输入与联系人的关系" />
                </view>
                <view class="info-item">
                  <text class="label">电话</text>
                  <input class="form-input" v-model="contact.phoneNum" placeholder="请输入联系人电话" type="number" />
                </view>
              </view>
              <button class="add-contact-btn" @click="addEmergencyContact">
                <uni-icons type="plusempty" size="16" color="#2979ff"></uni-icons>
                <text>添加联系人</text>
              </button>
            </view>
          </template>
        </view>
      </view>
      <!-- </scroll-view> -->

      <view class="empty"></view>

      <!-- 底部按钮 -->
      <view class="footer">
        <template v-if="!isEditing">
          <button class="btn edit-btn" @click="handleEdit">
            <uni-icons type="compose" size="16" color="#FFFFFF"></uni-icons>
            <text>编辑</text>
          </button>
          <button class="btn appeal-btn" @click="showAppealPopup">
            <uni-icons type="help" size="16" color="#FFFFFF"></uni-icons>
            <text>申诉</text>
          </button>
        </template>
        <template v-else>
          <button class="btn cancel-btn" @click="handleCancel">
            <uni-icons type="close" size="16" color="#FFFFFF"></uni-icons>
            <text>取消</text>
          </button>
          <button class="btn save-btn" @click="handleSave">
            <uni-icons type="checkmarkempty" size="16" color="#FFFFFF"></uni-icons>
            <text>保存</text>
          </button>
        </template>
      </view>

      <!-- 申诉弹窗 -->
      <uni-popup ref="appealPopup" type="center">
        <view class="appeal-popup">
          <view class="popup-title">信息申诉</view>
          <view class="popup-content">
            <view class="form-item">
              <text class="form-label">申诉单位类型</text>
              <radio-group class="radio-group" @change="handleTypeChange">
                <label class="radio" v-for="(item, index) in orgTypes" :key="index">
                  <radio :value="item.value" :checked="item.checked" color="#2979ff" />
                  <text>{{ item.label }}</text>
                </label>
              </radio-group>
            </view>
            <view class="form-item" v-if="appealForm.orgType === 'jg'">
              <text class="form-label">申诉单位名称</text>
              <view class="search-wrapper">
                <input class="form-input" v-model="appealForm.orgName" placeholder="请输入申诉单位名称" @input="handleUnitSearch"
                  @focus="handleInputFocus" @blur="handleInputBlur" />
                <view class="search-dropdown" v-if="showUnitDropdown && isInputFocused">
                  <view v-if="isSearching" class="dropdown-loading">
                    <uni-icons type="spinner-cycle" size="16" color="#999999"></uni-icons>
                    <text>搜索中...</text>
                  </view>
                  <view v-else-if="unitSearchResults.length === 0" class="dropdown-empty">
                    <text>暂无相关单位</text>
                  </view>
                  <view v-else class="dropdown-list">
                    <view class="dropdown-item" v-for="(item, index) in unitSearchResults" :key="index"
                      @click="selectUnit(item)">
                      {{ item.cname }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view class="form-item">
              <text class="form-label">申诉内容</text>
              <textarea class="form-textarea" v-model="appealForm.content" placeholder="请输入申诉内容" />
            </view>
          </view>
          <view class="popup-footer">
            <button class="popup-btn cancel" @click="closeAppealPopup">
              取消
            </button>
            <button class="popup-btn submit" @click="submitAppeal">提交</button>
          </view>
        </view>
      </uni-popup>
    </view>
  </gracePage>
</template>

<script>
import recordApi from "@/api/record.js";
import moment from "moment";
import AreaPicker from "@/components/AreaPicker.vue";

export default {
  components: {
    AreaPicker
  },
  data() {
    return {
      isEditing: false,
      form: {},
      // 编辑表单数据
      editForm: {
        name: "",
        gender: "",
        age: "",
        IDNum: "",
        phoneNum: "",
        contactPhoneNum: "",
        nativePlace: [], // 户籍所在地（数组格式）
        nativePlaceCode: "", // 户籍所在地编码
        nativePlaceAddress: "",
        residencePlace: [], // 常住所在地（数组格式）
        residencePlaceCode: "", // 常住所在地编码
        residenceAddress: "",
        nation: "",
        maritalStatus: "",
        education: "",
        bloodType: "",
        smokingHistory: "",
        smokingAmount: "",
        smokingYear: "",
        smokingMonth: "",
        drinkingHistory: "",
        drinkingAmount: "",
        drinkingYear: "",
        exerciseHabit: "",
        // 生育史
        currentChildren: "",
        abortion: "",
        stillbirth: "",
        premature: "",
        abnormalFetus: "",
        childrenHealth: "",
        // 月经史
        menarcheAge: "",
        menstruationDays: "",
        menstruationCycle: "",
        menopauseAge: "",
        emergencyContacts: [
          {
            _id: "",
            name: "",
            relationship: "",
            phoneNum: "",
          },
        ],
        // 过敏史
        allergy: [{
          _id: "",
          allergySource: "",
          allergySymptoms: "",
          allergyDate: "",
          treatment: "",
          effectRecord: "",
        }],
        // 既往史
        pastHistory: [{
          _id: "",
          diseaseName: "",
          diagnosisDate: "",
          institutionName: "",
          treatmentProcess: "",
          outcomeCode: "",
        }],
        // 家族史
        familyHistory: [{
          _id: "",
          familyMember: "",
          diseaseName: "",
          diagnosisDate: "",
          institutionName: "",
          treatmentProcess: "",
        }],
      },
      // 选项数据
      genderOptions: [
        { label: "男", value: "0" },
        { label: "女", value: "1" },
      ],
      marriageOptions: ["未婚", "已婚", "离异", "丧偶"],
      marriageIndex: 0,
      educationOptions: [
        "小学",
        "初中",
        "高中",
        "大专",
        "本科",
        "硕士",
        "博士",
      ],
      educationIndex: 0,
      bloodTypeOptions: ["A型", "B型", "AB型", "O型", "其他"],
      bloodTypeIndex: 0,
      smokingOptions: ["不吸烟", "偶尔吸烟", "经常吸烟", "已戒烟"],
      smokingHistoryIndex: 0,
      drinkingOptions: ["不饮酒", "偶尔饮酒", "经常饮酒", "已戒酒"],
      drinkingHistoryIndex: 0,
      // 转归情况选项
      outcomeOptions: ["治愈", "好转", "致残", "转院", "其他"],

      careerHistory: [],
      isEditingCareer: false,
      canEditCareer: false,
      careerForm: {
        historyId: "",
        workUnit: "",
        entryTime: "",
        leaveTime: "",
        workshop: "",
        station: "",
        workType: "",
        harmFactors: [], // 新增
      },
      medicalHistory: [],
      // 职业病记录
      occupationalDiseaseRecords: [],
      // 查询参数
      postData: {
        status: "",
        idCardType: "1",
        idCardCode: "",
        pageNum: 1,
        pageSize: 100,
      },
      orgTypes: [
        { label: "用人单位", value: "qy", checked: true },
        { label: "管理单位", value: "jg", checked: false },
      ],
      appealForm: {
        orgType: "qy",
        orgName: "",
        content: "",
        superUserID: "",
      },
      showUnitDropdown: false,
      unitSearchResults: [],
      searchTimer: null,
      isSearching: false,
      isInputFocused: false,
      allergyCollapsed: true,
      pastHistoryCollapsed: true,
      familyHistoryCollapsed: true,
      careerCollapsed: true,
      diseaseCollapsed: true,
      emergencyContactCollapsed: true,
      residencePlaceCollapsed: true, // 常住所在地展开收起状态
      harmFactorInput: "",
      harmFactorSearchList: [],
      harmFactorDropdownVisible: false,
      harmFactorSearchTimer: null,
      isHarmFactorSearching: false,
    };
  },
  created() {
    this.getData();
    this.getEmploymentHistory();
    this.checkCanEditCareer();
    this.getOccupationalDiseaseList();
  },
  methods: {
    async getData() {
      const res = await recordApi.getEHealthRecordBaseInfo();
      this.form = res.data;
      console.log("获取的个人信息数据:", this.form);
    },

    // 检查是否可以编辑职业史
    async checkCanEditCareer() {
      try {
        const res = await recordApi.getLaborIsEdit({
          enterpriseId: uni.getStorageSync('enterpriseId')
        });
        if (res.data && res.data.length > 0) {
          this.canEditCareer = res.data[0].lobarIsEdit || false;
        } else {
          this.canEditCareer = false;
        }
        console.log("是否可以编辑职业史:", this.canEditCareer);
      } catch (error) {
        console.error("获取职业史编辑权限失败:", error);
        this.canEditCareer = false;
      }
    },

    // 获取职业史列表
    async getEmploymentHistory() {
      try {
        const res = await recordApi.getEmploymentHistory({});
        this.careerHistory = res.data || [];
        console.log("获取的职业史数据:", this.careerHistory);
      } catch (error) {
        console.error("获取职业史失败:", error);
        this.careerHistory = [];
      }
    },

    // 添加职业史
    handleAddCareer() {
      if (!this.canEditCareer) {
        return uni.showToast({
          title: "您没有编辑职业史的权限",
          icon: "none"
        });
      }

      this.isEditingCareer = false;
      this.careerForm = {
        historyId: "",
        workUnit: "",
        entryTime: "",
        leaveTime: "",
        workshop: "",
        station: "",
        workType: "",
        harmFactors: [],
      };
      this.$refs.careerPopup.open();
    },

    // 编辑职业史
    handleEditCareer(item) {
      if (!this.canEditCareer) {
        return uni.showToast({
          title: "您没有编辑职业史的权限",
          icon: "none"
        });
      }

      this.isEditingCareer = true;
      this.careerForm = {
        historyId: item._id,
        workUnit: item.workUnit,
        entryTime: item.entryTime,
        leaveTime: item.leaveTime,
        workshop: item.workshop,
        station: item.station,
        workType: item.workType,
        harmFactors: item.harmFactors ? JSON.parse(JSON.stringify(item.harmFactors)) : [],
      };
      this.$refs.careerPopup.open();
    },

    // 删除职业史
    async handleDeleteCareer(item) {
      if (!this.canEditCareer) {
        return uni.showToast({
          title: "您没有编辑职业史的权限",
          icon: "none"
        });
      }

      uni.showModal({
        title: "提示",
        content: "确定要删除该职业史记录吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: "删除中..."
              });

              const result = await recordApi.deleteEmploymentHistory({
                historyId: item._id
              });

              uni.hideLoading();

              if (result.status === 200) {
                uni.showToast({
                  title: "删除成功",
                  icon: "success"
                });

                // 重新获取职业史列表
                this.getEmploymentHistory();
              } else {
                uni.showToast({
                  title: result.msg || "删除失败",
                  icon: "none"
                });
              }
            } catch (error) {
              uni.hideLoading();
              uni.showToast({
                title: "删除失败，请稍后重试",
                icon: "none"
              });
              console.error("删除职业史失败:", error);
            }
          }
        }
      });
    },

    // 关闭职业史弹窗
    closeCareerPopup() {
      this.$refs.careerPopup.close();
    },

    // 入职时间变更
    handleEntryTimeChange(e) {
      this.careerForm.entryTime = e.detail.value;
    },

    // 离职时间变更
    handleLeaveTimeChange(e) {
      this.careerForm.leaveTime = e.detail.value;
    },

    // 提交职业史
    async submitCareer() {
      // 表单验证
      if (!this.careerForm.workUnit) {
        return uni.showToast({
          title: "请输入工作单位",
          icon: "none"
        });
      }
      if (!this.careerForm.entryTime) {
        return uni.showToast({
          title: "请选择入职时间",
          icon: "none"
        });
      }

      try {
        uni.showLoading({
          title: this.isEditingCareer ? "更新中..." : "添加中..."
        });

        let result;
        if (this.isEditingCareer) {
          // 编辑职业史
          result = await recordApi.editEmploymentHistory({
            historyId: this.careerForm.historyId,
            workUnit: this.careerForm.workUnit,
            entryTime: this.careerForm.entryTime,
            leaveTime: this.careerForm.leaveTime,
            workshop: this.careerForm.workshop,
            station: this.careerForm.station,
            workType: this.careerForm.workType,
            harmFactors: this.careerForm.harmFactors,
          });
        } else {
          // 添加职业史
          result = await recordApi.addEmploymentHistory({
            workUnit: this.careerForm.workUnit,
            entryTime: this.careerForm.entryTime,
            leaveTime: this.careerForm.leaveTime,
            workshop: this.careerForm.workshop,
            station: this.careerForm.station,
            workType: this.careerForm.workType,
            harmFactors: this.careerForm.harmFactors,
          });
        }

        uni.hideLoading();

        if (result.status === 200) {
          uni.showToast({
            title: this.isEditingCareer ? "更新成功" : "添加成功",
            icon: "success"
          });

          // 关闭弹窗
          this.closeCareerPopup();

          // 重新获取职业史列表
          this.getEmploymentHistory();
        } else {
          uni.showToast({
            title: result.msg || (this.isEditingCareer ? "更新失败" : "添加失败"),
            icon: "none"
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: this.isEditingCareer ? "更新失败，请稍后重试" : "添加失败，请稍后重试",
          icon: "none"
        });
        console.error(this.isEditingCareer ? "更新职业史失败:" : "添加职业史失败:", error);
      }
    },

    // 获取基本信息值
    getBasicInfoValue(field) {
      if (!this.form || !this.form.employeeBasicInfo) return "";
      return this.form.employeeBasicInfo[field] || "";
    },

    // 获取户籍所在地显示文本
    getNativePlaceDisplay() {
      const basicInfo = this.form.employeeBasicInfo || {};
      if (Array.isArray(basicInfo.nativePlace) && basicInfo.nativePlace.length > 0) {
        return basicInfo.nativePlace.join(' ');
      }
      // 兼容旧格式
      return basicInfo.nativePlace || "";
    },

    // 获取常住所在地显示文本
    getResidencePlaceDisplay() {
      const basicInfo = this.form.employeeBasicInfo || {};
      if (Array.isArray(basicInfo.residencePlace) && basicInfo.residencePlace.length > 0) {
        return basicInfo.residencePlace.join(' ');
      }
      // 兼容旧格式
      return basicInfo.residencePlace || "";
    },

    // 获取常住所在地简化显示文本（用于建设兵团收起状态）
    getResidencePlaceSimpleDisplay() {
      const basicInfo = this.form.employeeBasicInfo || {};
      if (Array.isArray(basicInfo.residencePlace) && basicInfo.residencePlace.length > 0) {
        // 如果第一个元素是"建设兵团"，只显示"建设兵团"
        if (basicInfo.residencePlace[0] === "建设兵团") {
          return basicInfo.residencePlace[basicInfo.residencePlace.length - 1];
        }
        return basicInfo.residencePlace.join(' ');
      }
      // 兼容旧格式
      return basicInfo.residencePlace || "";
    },

    // 检查常住所在地是否以建设兵团开头
    isResidencePlaceCorps() {
      const basicInfo = this.form.employeeBasicInfo || {};
      if (Array.isArray(basicInfo.residencePlace) && basicInfo.residencePlace.length > 0) {
        return basicInfo.residencePlace[0] === "建设兵团";
      }
      return false;
    },

    // 切换常住所在地展开收起状态
    toggleResidencePlace() {
      this.residencePlaceCollapsed = !this.residencePlaceCollapsed;
    },

    // 处理户籍所在地变化
    handleNativePlaceChange(value) {
      // value 现在包含 names 数组和 code
      if (value && value.names) {
        this.editForm.nativePlace = value.names;
        this.editForm.nativePlaceCode = value.code || '';
      } else {
        this.editForm.nativePlace = [];
        this.editForm.nativePlaceCode = '';
      }
    },

    // 处理常住所在地变化
    handleResidencePlaceChange(value) {
      // value 现在包含 names 数组和 code
      if (value && value.names) {
        this.editForm.residencePlace = value.names;
        this.editForm.residencePlaceCode = value.code || '';
      } else {
        this.editForm.residencePlace = [];
        this.editForm.residencePlaceCode = '';
      }
    },

    // 获取吸烟年限显示
    getSmokeYears() {
      const year = this.getBasicInfoValue("smokingYear");
      const month = this.getBasicInfoValue("smokingMonth");

      if (!year && !month) return ""; // 默认值

      if (year && month) {
        return `${year}年${month}月`;
      } else if (year) {
        return `${year}年`;
      } else if (month) {
        return `${month}月`;
      }

      return "";
    },

    // 获取紧急联系人列表
    getEmergencyContacts() {
      if (!this.form || !this.form.employeeBasicInfo || !this.form.employeeBasicInfo.emergencyContact) {
        return [];
      }
      const arr = Array.isArray(this.form.employeeBasicInfo.emergencyContact)
        ? this.form.employeeBasicInfo.emergencyContact
        : [this.form.employeeBasicInfo.emergencyContact];
      if (!arr.length || arr.every(item => !item || Object.values(item).every(v => !v))) {
        return [];
      }
      return arr;
    },

    // 添加紧急联系人
    addEmergencyContact() {
      this.editForm.emergencyContacts.push({
        _id: "",
        name: "",
        relationship: "",
        phoneNum: "",
      });
    },

    // 移除紧急联系人
    removeEmergencyContact(index) {
      this.editForm.emergencyContacts.splice(index, 1);
    },

    // 进入编辑模式
    handleEdit() {
      this.isEditing = true;

      // 从employeeBasicInfo中获取数据
      const basicInfo = this.form.employeeBasicInfo || {};
      const emergencyContacts = basicInfo.emergencyContact || [];
      const allergy = basicInfo.allergy || [];
      const pastHistory = basicInfo.pastHistory || [];
      const familyHistory = basicInfo.familyHistory || [];

      // 复制表单数据到编辑表单
      this.editForm = {
        name: this.form.name || "",
        gender: this.form.gender || "",
        age: this.form.age || "",
        IDNum: this.form.IDNum || "",
        phoneNum: this.form.phoneNum || "",
        contactPhoneNum: basicInfo.contactPhoneNum || "",
        nativePlace: Array.isArray(basicInfo.nativePlace) ? basicInfo.nativePlace : [],
        nativePlaceCode: basicInfo.nativePlaceCode || "",
        nativePlaceAddress: basicInfo.nativePlaceAddress || "",
        residencePlace: Array.isArray(basicInfo.residencePlace) ? basicInfo.residencePlace : [],
        residencePlaceCode: basicInfo.residencePlaceCode || "",
        residenceAddress: basicInfo.residenceAddress || "",
        nation: basicInfo.nation || "",
        maritalStatus: basicInfo.maritalStatus || this.form.marriage || "",
        education: basicInfo.education || "",
        bloodType: basicInfo.bloodType || "",
        smokingHistory: basicInfo.smokingHistory || "",
        smokingAmount: basicInfo.smokingAmount || "",
        smokingYear: basicInfo.smokingYear || "",
        smokingMonth: basicInfo.smokingMonth || "",
        drinkingHistory: basicInfo.drinkingHistory || "",
        drinkingAmount: basicInfo.drinkingAmount || "",
        drinkingYear: basicInfo.drinkingYear || "",
        exerciseHabit: basicInfo.exerciseHabit || "",
        // 生育史
        currentChildren: basicInfo.currentChildren || "",
        abortion: basicInfo.abortion || "",
        stillbirth: basicInfo.stillbirth || "",
        premature: basicInfo.premature || "",
        abnormalFetus: basicInfo.abnormalFetus || "",
        childrenHealth: basicInfo.childrenHealth || "",
        // 月经史
        menarcheAge: basicInfo.menarcheAge || "",
        menstruationDays: basicInfo.menstruationDays || "",
        menstruationCycle: basicInfo.menstruationCycle || "",
        menopauseAge: basicInfo.menopauseAge || "",
        emergencyContacts:
          Array.isArray(emergencyContacts) && emergencyContacts.length > 0
            ? emergencyContacts.map((contact) => ({
              _id: contact._id || "",
              name: contact.name || "",
              relationship: contact.relationship || "",
              phoneNum: contact.phoneNum || "",
            }))
            : [
              {
                _id: "",
                name: "",
                relationship: "",
                phoneNum: "",
              },
            ],
        allergy: Array.isArray(allergy) && allergy.length > 0
          ? allergy.map(item => ({
            _id: item._id || "",
            allergySource: item.allergySource || "",
            allergySymptoms: item.allergySymptoms || "",
            allergyDate: item.allergyDate || "",
            treatment: item.treatment || "",
            effectRecord: item.effectRecord || "",
          }))
          : [{
            _id: "",
            allergySource: "",
            allergySymptoms: "",
            allergyDate: "",
            treatment: "",
            effectRecord: "",
          }],
        pastHistory: Array.isArray(pastHistory) && pastHistory.length > 0
          ? pastHistory.map(item => ({
            _id: item._id || "",
            diseaseName: item.diseaseName || "",
            diagnosisDate: item.diagnosisDate || "",
            institutionName: item.institutionName || "",
            treatmentProcess: item.treatmentProcess || "",
            outcomeCode: item.outcomeCode || "",
          }))
          : [{
            _id: "",
            diseaseName: "",
            diagnosisDate: "",
            institutionName: "",
            treatmentProcess: "",
            outcomeCode: "",
          }],
        familyHistory: Array.isArray(familyHistory) && familyHistory.length > 0
          ? familyHistory.map(item => ({
            _id: item._id || "",
            familyMember: item.familyMember || "",
            diseaseName: item.diseaseName || "",
            diagnosisDate: item.diagnosisDate || "",
            institutionName: item.institutionName || "",
            treatmentProcess: item.treatmentProcess || "",
          }))
          : [{
            _id: "",
            familyMember: "",
            diseaseName: "",
            diagnosisDate: "",
            institutionName: "",
            treatmentProcess: "",
          }],
      };

      // 设置选择器索引
      this.setPickerIndexes();
    },

    // 设置选择器索引
    setPickerIndexes() {
      // 婚姻状况索引
      const maritalStatus = this.editForm.maritalStatus;
      if (maritalStatus) {
        const index = this.marriageOptions.findIndex(
          (item) => item === maritalStatus
        );
        this.marriageIndex = index > -1 ? index : 0;
      }

      // 文化程度索引
      const education = this.editForm.education;
      if (education) {
        const index = this.educationOptions.findIndex(
          (item) => item === education
        );
        this.educationIndex = index > -1 ? index : 0;
      }

      // 血型索引
      const bloodType = this.editForm.bloodType;
      if (bloodType) {
        const index = this.bloodTypeOptions.findIndex(
          (item) => item === bloodType
        );
        this.bloodTypeIndex = index > -1 ? index : 0;
      }

      // 吸烟情况索引
      const smokingHistory = this.editForm.smokingHistory;
      if (smokingHistory) {
        const index = this.smokingOptions.findIndex(
          (item) => item === smokingHistory
        );
        this.smokingHistoryIndex = index > -1 ? index : 0;
      }

      // 饮酒情况索引
      const drinkingHistory = this.editForm.drinkingHistory;
      if (drinkingHistory) {
        const index = this.drinkingOptions.findIndex(
          (item) => item === drinkingHistory
        );
        this.drinkingHistoryIndex = index > -1 ? index : 0;
      }
    },

    // 取消编辑
    handleCancel() {
      this.isEditing = false;
      // 重新获取数据
      this.getData();
    },

    // 保存编辑
    handleSave() {
      // 必填校验
      if (!this.editForm.nativePlace || this.editForm.nativePlace.length === 0) {
        return uni.showToast({
          title: "请选择户籍所在地",
          icon: "none"
        });
      }

      if (!this.editForm.residencePlace || this.editForm.residencePlace.length === 0) {
        return uni.showToast({
          title: "请选择常住所在地",
          icon: "none"
        });
      }

      // 构建employeeBasicInfo对象
      const employeeBasicInfo = {
        _id: this.form.employeeBasicInfo?._id || "",
        contactPhoneNum: this.editForm.contactPhoneNum,
        nativePlace: this.editForm.nativePlace,
        nativePlaceCode: this.editForm.nativePlaceCode,
        nativePlaceAddress: this.editForm.nativePlaceAddress,
        residencePlace: this.editForm.residencePlace,
        residencePlaceCode: this.editForm.residencePlaceCode,
        residenceAddress: this.editForm.residenceAddress,
        nation: this.editForm.nation,
        maritalStatus: this.editForm.maritalStatus,
        education: this.editForm.education,
        bloodType: this.editForm.bloodType,
        smokingHistory: this.editForm.smokingHistory,
        smokingAmount: this.editForm.smokingAmount,
        smokingYear: this.editForm.smokingYear,
        smokingMonth: this.editForm.smokingMonth,
        drinkingHistory: this.editForm.drinkingHistory,
        drinkingAmount: this.editForm.drinkingAmount,
        drinkingYear: this.editForm.drinkingYear,
        exerciseHabit: this.editForm.exerciseHabit,
        // 生育史
        currentChildren: this.editForm.currentChildren,
        abortion: this.editForm.abortion,
        stillbirth: this.editForm.stillbirth,
        premature: this.editForm.premature,
        abnormalFetus: this.editForm.abnormalFetus,
        childrenHealth: this.editForm.childrenHealth,
        // 月经史
        menarcheAge: this.editForm.menarcheAge,
        menstruationDays: this.editForm.menstruationDays,
        menstruationCycle: this.editForm.menstruationCycle,
        menopauseAge: this.editForm.menopauseAge,
        emergencyContact: this.editForm.emergencyContacts.map((contact) => ({
          _id: contact._id || undefined,
          name: contact.name,
          relationship: contact.relationship,
          phoneNum: contact.phoneNum,
        })),
        allergy: this.editForm.allergy.map(item => ({
          _id: item._id || undefined,
          allergySource: item.allergySource,
          allergySymptoms: item.allergySymptoms,
          allergyDate: item.allergyDate,
          treatment: item.treatment,
          effectRecord: item.effectRecord,
        })),
        pastHistory: this.editForm.pastHistory.map(item => ({
          _id: item._id || undefined,
          diseaseName: item.diseaseName,
          diagnosisDate: item.diagnosisDate,
          institutionName: item.institutionName,
          treatmentProcess: item.treatmentProcess,
          outcomeCode: item.outcomeCode,
        })),
        familyHistory: this.editForm.familyHistory.map(item => ({
          _id: item._id || undefined,
          familyMember: item.familyMember,
          diseaseName: item.diseaseName,
          diagnosisDate: item.diagnosisDate,
          institutionName: item.institutionName,
          treatmentProcess: item.treatmentProcess,
        })),
      };

      // 构建请求参数
      const payload = {
        _id: this.form._id,
        name: this.editForm.name,
        gender: this.editForm.gender,
        age: this.editForm.age,
        IDNum: this.editForm.IDNum,
        phoneNum: this.editForm.phoneNum,
        employeeBasicInfo,
      };

      // 调用更新接口
      this.saveData(payload);
    },

    // 保存数据到服务器
    async saveData(payload) {
      try {
        uni.showLoading({
          title: "保存中...",
        });

        // 调用接口保存数据
        const res = await recordApi.updateEHealthRecordBaseInfo(payload);

        uni.hideLoading();

        if (res.status === 200) {
          uni.showToast({
            title: "保存成功",
            icon: "success",
          });

          // 退出编辑模式
          this.isEditing = false;

          // 重新获取数据
          this.getData();
        } else {
          uni.showToast({
            title: res.msg || "保存失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: "保存失败，请稍后重试",
          icon: "none",
        });
        console.error("保存数据失败:", error);
      }
    },

    // 性别选择器变更
    handleGenderChange(e) {
      const index = e.detail.value;
      this.editForm.gender = this.genderOptions[index].value;
    },

    // 婚姻状况选择器变更
    handleMarriageChange(e) {
      const index = e.detail.value;
      this.marriageIndex = index;
      this.editForm.maritalStatus = this.marriageOptions[index];
    },

    // 文化程度选择器变更
    handleEducationChange(e) {
      const index = e.detail.value;
      this.educationIndex = index;
      this.editForm.education = this.educationOptions[index];
    },

    // 血型选择器变更
    handleBloodTypeChange(e) {
      const index = e.detail.value;
      this.bloodTypeIndex = index;
      this.editForm.bloodType = this.bloodTypeOptions[index];
    },

    // 吸烟情况选择器变更
    handleSmokingHistoryChange(e) {
      const index = e.detail.value;
      this.smokingHistoryIndex = index;
      this.editForm.smokingHistory = this.smokingOptions[index];
    },

    // 饮酒情况选择器变更
    handleDrinkingHistoryChange(e) {
      const index = e.detail.value;
      this.drinkingHistoryIndex = index;
      this.editForm.drinkingHistory = this.drinkingOptions[index];
    },

    showAppealPopup() {
      this.$refs.appealPopup.open();
    },
    closeAppealPopup() {
      this.$refs.appealPopup.close();
    },
    handleTypeChange(e) {
      this.appealForm.orgType = e.detail.value;
      this.appealForm.orgName = ""; // 切换类型时清空单位名称
      this.showUnitDropdown = false;
    },
    handleUnitSearch(e) {
      const keyword = e.detail.value;
      console.log("搜索关键字:", keyword);

      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，实现防抖
      this.searchTimer = setTimeout(async () => {
        if (!keyword) {
          this.showUnitDropdown = false;
          return;
        }

        this.isSearching = true;
        this.showUnitDropdown = true;

        try {
          const res = await recordApi.getSupervisionList({
            cname: keyword,
          });
          this.unitSearchResults = res.data;
        } catch (error) {
          console.error("搜索失败:", error);
          this.unitSearchResults = [];
        } finally {
          this.isSearching = false;
        }
      }, 300); // 300ms的防抖时间
    },
    selectUnit(item) {
      this.appealForm.orgName = item.cname;
      if (this.appealForm.orgType === "jg") {
        this.appealForm.superUserID = item._id;
      }
      this.showUnitDropdown = false;
    },
    async submitAppeal() {
      if (this.appealForm.orgType === "jg" && !this.appealForm.orgName) {
        uni.showToast({
          title: "请输入申诉单位名称",
          icon: "none",
        });
        return;
      }
      if (!this.appealForm.content) {
        uni.showToast({
          title: "请输入申诉内容",
          icon: "none",
        });
        return;
      }

      // 构建请求参数
      const params = {
        orgType: this.appealForm.orgType,
        orgName: this.appealForm.orgName,
        content: this.appealForm.content,
      };
      if (this.appealForm.orgType === "jg") {
        params.superUserID = this.appealForm.superUserID;
        if (!params.superUserID) {
          return uni.showToast({
            title: "请选择正确的监管单位",
            icon: "none",
          });
        }
      }

      console.log("申诉参数:", params);
      const res = await recordApi.postComplaint(params);
      if (res.status !== 200) {
        uni.showToast({
          title: res.msg || "申诉提交失败",
          icon: "error",
        });
        return;
      }
      uni.showToast({
        title: "提交成功",
        icon: "success",
      });
      this.appealForm = this.$options.data().appealForm;
      this.closeAppealPopup();
    },

    getGender(val) {
      // 0男 1女
      if (val == "0") {
        return "男";
      } else if (val == "1") {
        return "女";
      } else {
        return "未知";
      }
    },

    // 获取职业病记录列表
    async getOccupationalDiseaseList() {
      try {
        // 设置身份证号
        this.postData.idCardCode = this.form?.IDNum || uni.getStorageSync('idNo') || '';
        const res = await recordApi.getDiaList(this.postData);
        // 过滤出hasOccupationalDisease为true的记录
        this.occupationalDiseaseRecords = res.data.filter(item => item.hasOccupationalDisease === true) || [];
        console.log("获取的职业病记录数据:", this.occupationalDiseaseRecords);
      } catch (error) {
        console.error("获取职业病记录失败:", error);
        this.occupationalDiseaseRecords = [];
      }
    },

    // 格式化日期年份
    formatDateYear(date) {
      if (!date) return "";
      return moment(date).format("YYYY") + "年";
    },

    // 格式化日期月日
    formatDateMonthDay(date) {
      if (!date) return "";
      return moment(date).format("MM") + "月" + moment(date).format("DD") + "日";
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "";
      return moment(date).format("YYYY-MM-DD");
    },

    // 获取职业病名称列表
    getOccupationalDiseaseNames(diseases) {
      if (!diseases || !Array.isArray(diseases) || diseases.length === 0) return "";
      return diseases.map(disease => disease.name).join("、");
    },
    handleInputFocus() {
      this.isInputFocused = true;
      if (this.appealForm.orgName) {
        this.showUnitDropdown = true;
      }
    },
    handleInputBlur() {
      // 使用setTimeout确保点击下拉选项时能触发selectUnit事件
      setTimeout(() => {
        this.isInputFocused = false;
        this.showUnitDropdown = false;
      }, 200);
    },
    // 获取过敏史列表
    getAllergyHistory() {
      if (!this.form || !this.form.employeeBasicInfo || !this.form.employeeBasicInfo.allergy) {
        return [];
      }
      const arr = Array.isArray(this.form.employeeBasicInfo.allergy)
        ? this.form.employeeBasicInfo.allergy
        : [this.form.employeeBasicInfo.allergy];
      if (!arr.length || arr.every(item => !item || Object.values(item).every(v => !v))) {
        return [];
      }
      return arr;
    },

    // 添加过敏史
    addAllergy() {
      this.editForm.allergy.push({
        _id: "",
        allergySource: "",
        allergySymptoms: "",
        allergyDate: "",
        treatment: "",
        effectRecord: "",
      });
    },

    // 移除过敏史
    removeAllergy(index) {
      this.editForm.allergy.splice(index, 1);
    },

    // 处理过敏日期变更
    handleAllergyDateChange(e, index) {
      this.editForm.allergy[index].allergyDate = e.detail.value;
    },

    // 获取既往史列表
    getPastHistory() {
      if (!this.form || !this.form.employeeBasicInfo || !this.form.employeeBasicInfo.pastHistory) {
        return [];
      }
      const arr = Array.isArray(this.form.employeeBasicInfo.pastHistory)
        ? this.form.employeeBasicInfo.pastHistory
        : [this.form.employeeBasicInfo.pastHistory];
      if (!arr.length || arr.every(item => !item || Object.values(item).every(v => !v))) {
        return [];
      }
      return arr;
    },

    // 添加既往史
    addPastHistory() {
      this.editForm.pastHistory.push({
        _id: "",
        diseaseName: "",
        diagnosisDate: "",
        institutionName: "",
        treatmentProcess: "",
        outcomeCode: "",
      });
    },

    // 移除既往史
    removePastHistory(index) {
      this.editForm.pastHistory.splice(index, 1);
    },

    // 处理既往史日期变更
    handlePastHistoryDateChange(e, index) {
      this.editForm.pastHistory[index].diagnosisDate = e.detail.value;
    },

    // 获取转归情况索引
    getOutcomeIndex(code) {
      if (!code) return 0;
      const index = this.outcomeOptions.findIndex(item => item === this.getOutcomeText(code));
      return index > -1 ? index : 0;
    },

    // 获取转归情况文本
    getOutcomeText(code) {
      const codeMap = {
        "1": "治愈",
        "2": "好转",
        "3": "致残",
        "5": "转院",
        "99": "其他"
      };
      return codeMap[code] || "";
    },

    // 处理转归情况变更
    handleOutcomeChange(e, index) {
      const codeMap = {
        "治愈": "1",
        "好转": "2",
        "致残": "3",
        "转院": "5",
        "其他": "99"
      };
      this.editForm.pastHistory[index].outcomeCode = codeMap[this.outcomeOptions[e.detail.value]];
    },

    // 获取家族史列表
    getFamilyHistory() {
      if (!this.form || !this.form.employeeBasicInfo || !this.form.employeeBasicInfo.familyHistory) {
        return [];
      }
      const arr = Array.isArray(this.form.employeeBasicInfo.familyHistory)
        ? this.form.employeeBasicInfo.familyHistory
        : [this.form.employeeBasicInfo.familyHistory];
      if (!arr.length || arr.every(item => !item || Object.values(item).every(v => !v))) {
        return [];
      }
      return arr;
    },

    // 添加家族史
    addFamilyHistory() {
      this.editForm.familyHistory.push({
        _id: "",
        familyMember: "",
        diseaseName: "",
        diagnosisDate: "",
        institutionName: "",
        treatmentProcess: "",
      });
    },

    // 移除家族史
    removeFamilyHistory(index) {
      this.editForm.familyHistory.splice(index, 1);
    },

    // 处理家族史日期变更
    handleFamilyHistoryDateChange(e, index) {
      this.editForm.familyHistory[index].diagnosisDate = e.detail.value;
    },

    // 危害因素输入防抖查询
    handleHarmFactorInput(e) {
      const val = e.detail?.value ?? e;
      this.harmFactorInput = val;
      if (this.harmFactorSearchTimer) clearTimeout(this.harmFactorSearchTimer);
      this.harmFactorSearchTimer = setTimeout(() => {
        this.searchHarmFactors();
      }, 300);
    },

    // 处理危害因素输入框获得焦点
    handleHarmFactorFocus() {
      this.harmFactorDropdownVisible = true;
      this.searchHarmFactors();
    },

    // 搜索危害因素
    async searchHarmFactors() {
      this.isHarmFactorSearching = true;
      try {
        const res = await recordApi.findHarmFactors({ query: { name: this.harmFactorInput }, pageSize: 999 });
        const arr = (res.data && res.data.data) || [];
        this.harmFactorSearchList = arr.map(item => ({
          _id: item._id,
          code: item.code,
          category: item.catetory,
          name: item.harmFactorName,
          otherName: item.otherName || "",
        }));

      } catch (err) {
        this.harmFactorSearchList = [];
      } finally {
        this.isHarmFactorSearching = false;
      }
    },

    // 失焦时隐藏下拉
    onHarmFactorBlur() {
      setTimeout(() => {
        this.harmFactorDropdownVisible = false;
      }, 200);
    },

    // 选择危害因素
    selectHarmFactor(item) {
      console.log("选择的危害因素:", item);
      if (!this.careerForm.harmFactors.some(f => f._id === item._id)) {
        this.careerForm.harmFactors.push({
          _id: item._id,
          code: item.code,
          category: item.category,
          name: item.name,
        });
      }
      // this.harmFactorInput = "";
      // this.harmFactorSearchList = [];
      // this.harmFactorDropdownVisible = false;
    },
    // 删除已选危害因素
    removeHarmFactor(idx) {
      this.careerForm.harmFactors.splice(idx, 1);
    },
  },
};
</script>

<style>
page {
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  overflow: auto;
  padding: 20rpx;
}

.section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.section-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  position: relative;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #2979ff;
  border-radius: 3rpx;
}

.add-career-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f7ff;
  border: 1px solid #d6e9ff;
  border-radius: 30rpx;
  padding: 6rpx 20rpx;
  font-size: 12px;
  color: #2979ff;
  line-height: 1.5;

  position: absolute;
  right: 0;
}

.add-career-btn text {
  margin-left: 4rpx;
}

.info-group {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 100%;
  margin-bottom: 20rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.info-item.full-width {
  width: 100%;
}

.label {
  font-size: 14px;
  color: #666666;
  margin-right: 20rpx;
  min-width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  color: #333333;
  flex: 1;

  text-align: right;
}

.sub-section {
  margin-top: 30rpx;
}

.sub-title {
  font-size: 14px;
  color: #666666;
  margin-bottom: 20rpx;
}

.career-item {
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f9fafb;
  position: relative;
}

.career-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px dashed #eaeaea;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  font-size: 14px;
  padding: 12rpx 0;
  width: 48%;
  line-height: 1.5;
}

.action-btn.edit {
  color: #2979ff;
  background-color: #f0f7ff;
  border-color: #d6e9ff;
}

.action-btn.delete {
  color: #FF4D4F;
  background-color: #fff0f0;
  border-color: #ffd6d6;
}

.action-btn text {
  margin-left: 8rpx;
}

.empty-career {
  padding: 40rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #909399;
}

.career-popup {
  width: 600rpx;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: auto;
}

.career-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.company {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
}

.time {
  font-size: 12px;
  color: #999999;
}

.medical-item {
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.empty {
  height: 130rpx;
}

.footer {
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

@media screen and (min-width: 960px) {
  .footer {
    width: 23.5rem;
    left: 50%;
    transform: translateX(-50%);
  }
}

.btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.btn text {
  margin-left: 10rpx;
}

.edit-btn {
  background-color: #2979ff;
  color: #ffffff;
}

.appeal-btn {
  background-color: #ff9500;
  color: #ffffff;
}

.appeal-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.popup-title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  padding: 30rpx;
  border-bottom: 1px solid #eaeaea;
}

.popup-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 20rpx;
}

.radio-group {
  display: flex;
}

.radio {
  margin-right: 40rpx;
  font-size: 14px;
}

.picker-wrapper {
  flex: 1;
}

.form-input {
  flex: 1;
  height: 70rpx;
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 14px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
}

/* 添加焦点效果 */
input.form-input {
  background-color: #ffffff;
}

input.form-input:focus {
  border-color: #2979ff;
}

.form-textarea {
  width: 100%;
  height: 150rpx;
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 14px;
  background-color: #ffffff;
}

.popup-footer {
  display: flex;
  border-top: 1px solid #eaeaea;
}

.popup-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.popup-btn.cancel {
  background-color: #f5f5f5;
  color: #666666;
}

.popup-btn.submit {
  background-color: #2979ff;
  color: #ffffff;
}

/* 联系人 */
.contact-item {
  background-color: #f9fafb;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-name {
  font-size: 28rpx;
  font-weight: 500;
}

.contact-relation {
  font-size: 24rpx;
  color: #6b7280;
}

.contact-phone {
  color: #3176ff;
  font-size: 28rpx;
}

.cancel-btn {
  background-color: #999999;
  color: #ffffff;
}

.save-btn {
  background-color: #2979ff;
  color: #ffffff;
}

.form-input-group {
  flex: 1;
  display: flex;
  gap: 10rpx;
}

.form-input-half {
  flex: 1;
}

.empty-contact {
  padding: 40rpx 0;
  text-align: center;
}

.empty-text {
  color: #999999;
  font-size: 14px;
}

.emergency-contact-form {
  background-color: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
}

.contact-form-item {
  background-color: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.contact-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.contact-form-title {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.delete-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.add-contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.add-contact-btn text {
  margin-left: 10rpx;
  color: #2979ff;
  font-size: 14px;
}

.search-wrapper {
  position: relative;
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  max-height: 240rpx;
  overflow: auto;
  z-index: 1000;
  margin-top: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  padding: 16rpx 20rpx;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-loading,
.dropdown-empty {
  padding: 20rpx;
  text-align: center;
  color: #999999;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-loading uni-icons {
  margin-right: 8rpx;
}

.history-list {
  margin-bottom: 20rpx;
}

.history-item {
  background-color: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.history-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.history-date {
  font-size: 24rpx;
  color: #6b7280;
}

.history-content {
  margin-bottom: 16rpx;
}

.history-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px dashed #eaeaea;
}

.history-form {
  background-color: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
}

.history-form-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.history-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.history-form-title {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.add-history-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.add-history-btn text {
  margin-left: 10rpx;
  color: #2979ff;
  font-size: 14px;
}

.empty-history {
  padding: 40rpx 0;
  text-align: center;
}

.empty-text {
  color: #999999;
  font-size: 14px;
}

.section-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  cursor: pointer;
}

.section-title-bar uni-icons {
  margin-left: 10rpx;
}

.career-add-fab {
  position: relative;
  margin: 24rpx auto 0 auto;
  width: 56rpx;
  height: 56rpx;
  background: #2ecc71;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(46, 204, 113, 0.15);
  cursor: pointer;
}

.career-add-fab uni-icons {
  display: block;
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8rpx;
}

.tag {
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 4rpx;
  padding: 4rpx 12rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.tag uni-icons {
  margin-left: 4rpx;
  cursor: pointer;
}

.harm-factor-input-wrapper {
  position: relative;
  width: 100%;
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 8rpx;
  max-height: 240rpx;
  overflow: auto;
  z-index: 1000;
  margin-top: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.dropdown-loading,
.dropdown-empty {
  padding: 20rpx;
  text-align: center;
  color: #999999;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-loading uni-icons {
  margin-right: 8rpx;
}

.dropdown-item {
  padding: 16rpx 20rpx;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

/* 必填标识样式 */
.required-star {
  color: #ff4d4f;
  margin-right: 4rpx;
}

/* 常住所在地展开收起样式 */
.residence-place-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.expand-icon {
  margin-left: 10rpx;
  cursor: pointer;
}

.residence-place-detail {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #2979ff;
}

.detail-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style>
