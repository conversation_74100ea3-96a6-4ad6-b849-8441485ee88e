<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="鉴定记录" />
		<view slot="gBody" class="grace-body content">
			<view class="diagnosis-result">
				<view class="search-wrap">
					<u--input style="width: 100%;" v-model="selectedStatusText" placeholder="请选择状态"
						@focus="showStatusPicker = true">
					</u--input>
					<u-picker :show="showStatusPicker" :columns="[identifyStatus]" @confirm="onStatusConfirm"
						@cancel="showStatusPicker = false"></u-picker>
				</view>
				<view class="diagnosis-list">
					<u-empty v-if="diagnosisList.length == 0" mode="data" icon="/static/empty.png" text="暂无鉴定记录">
					</u-empty>
					<u-list v-else>
						<u-list-item v-for="(item, index) in diagnosisList" :key="index">
							<view class="diagnosis-item">
								<view class="item-header">
									<text class="time">申请时间：{{formatDate(item.applicationDate)}}</text>
									<u-tag v-for="el in getTags(item)" :key="el.value" :type="el.type"
										:text="el.text"></u-tag>
								</view>
								<view class="item-content">
									<view class="info-row">
										<text class="label">劳动者姓名：</text>
										<text class="value">{{item.workerName}}</text>
									</view>
									<view class="info-row">
										<text class="label">用人单位名称：</text>
										<text class="value">{{item.empName}}</text>
									</view>
									<view class="info-row">
										<text class="label">用工单位名称：</text>
										<text class="value">{{item.workEmpName}}</text>
									</view>
									<view class="info-row">
										<text class="label">鉴定机构：</text>
										<text
											class="value">{{item.institutionInfo && item.institutionInfo.institutionName || ''}}</text>
									</view>
									<!-- <view class="info-row">
								<text class="label">申请病种：</text>
								<text class="value">{{item.diseaseType}}</text>
							</view> -->
									<view class="info-row" v-if="item.status == '-1'">
										<text class="label">不受理原因：</text>
										<text class="value">{{item.rejectedReason}}</text>
									</view>
								</view>
								<view class="item-footer">
									<u-button v-if="item.status == '0'" type="primary" size="mini"
										@click="editApplication(item)">
										修改申请
									</u-button>
                  <u-button v-if="item.status >= '5'" type="success" size="mini"
										@click="downloadAcceptNotice(item)">
										下载受理通知书
									</u-button>
                  <u-button v-if=" item.status == '-1'" type="warning" size="mini"
										@click="downloadRefuseNotice(item)">
										下载不予受理通知书
									</u-button>
									<u-button v-if="item.status == '0'" type="warning" size="mini"
										@click="uploadMaterials(item)">
										补充材料
									</u-button>
									<u-button v-if="item.status == '99'" type="success" size="mini"
										@click="downloadReport(item)">
										下载鉴定报告
									</u-button>
									<u-button v-if="item.status == '99'" type="primary" size="mini"
										@click="firstIdentify(item)">
										申请兵团级鉴定
									</u-button>
								</view>
							</view>
						</u-list-item>
					</u-list>
				</view>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import identifyApi from '@/api/identify.js'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import moment from 'moment'
	export default {
		data() {
			return {
				diagnosisList: [],
				postData: {
					idCardType: '1',
					idCardCode: '',
					pageNum: 1,
					pageSize: 100,
				},
				total: 0,
				showStatusPicker: false,
				selectedStatusText: '',
			}
		},
		onShow() {
			this.postData.idCardCode = this.userInfo?.idNo || ''
			this.fetchIdentifyList();
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			});
		},
		async created() {
			await this.getIdentifyStatus()
		},
		mounted() {
			this.postData.idCardCode = this.userInfo?.idNo || ''
			this.fetchIdentifyList()
		},
		computed: {
			...mapGetters(["userInfo", "identifyStatus"]),
		},
		methods: {
			...mapActions(['getIdentifyStatus']),
			formatDate(date) {
				return moment(date).format('YYYY-MM-DD')
			},

			getTags(row) {
				let identifyStatus = this.identifyStatus.filter(item => item.type)
				return identifyStatus.filter((item) => item.value == row.status);
			},
			onStatusConfirm(e) {
				this.selectedStatusText = e.value[0].text
				this.postData.status = e.value[0].value
				this.showStatusPicker = false
				this.diagnosisList = []
				this.fetchIdentifyList()
			},
			async fetchIdentifyList() {
				const res = await identifyApi.getIdentificationInfo(this.postData)
				this.diagnosisList = res.data.list
				this.postData.pageNum = res.data.pageNum
				this.postData.pageSize = res.data.pageSize
				this.total = res.data.total
			},
			async downloadReport(item) {
				// 下载报告
				try {
					uni.showLoading({
						title: '下载中...'
					});
					const res = await identifyApi.downloadIdentify({
						identificationId: item.id
					});
					if (res.data.success && res.data.data[0].fileUrl) {
						uni.hideLoading();
						const fileUrl = res.data.data[0].fileUrl;
						window.open(fileUrl, '_blank');
					} else {
						uni.hideLoading();
						uni.showToast({
							title: res.data.message || '获取文件失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					uni.showToast({
						title: error.message || '网络错误',
						icon: 'none'
					});
				}
			},
      // 
			editApplication(item) {
				// 修改申请
				uni.navigateTo({
					url: `/pages_user/pages/identify/jgForm?userId=${item.id}`
				})
			},
			uploadMaterials(item) {
				// 补充材料
				uni.navigateTo({
					url: `/pages/institution/addInformation?id=${item.id}&type=jd`
				})
			},
			firstIdentify(item) {
				// 兵团级鉴定
				uni.navigateTo({
					url: `/pages_user/pages/identify/apply?firstIdentificationId=${item.id}`
				})
			},
      // 下载不予受理通知书
      async downloadRefuseNotice(item) {
        const res = await identifyApi.getIdeRefuseNotice({identificationId: item.id})
        if(res.data.success) {
          window.open(res.data.data[0].fileUrl, '_blank')
        } else {
          uni.showToast({
            title: res.data.message || '获取文件失败',
            icon: 'error'
          })
        }
      },

      // 下载受理通知书
      async downloadAcceptNotice(item){
        const res = await identifyApi.getIdeAcceptNotice({identificationId: item.id})
        if(res.data.success) {
          window.open(res.data.data[0].fileUrl, '_blank')
        } else {
          uni.showToast({
            title: res.data.message || '获取文件失败',
            icon: 'error'
          })
        }
      },
		}
	}
</script>

<style lang="scss" scoped>
	.diagnosis-result {
		// padding: 0 30rpx;

		.nav-left {
			display: flex;
			align-items: center;
			color: #fff;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}

		.diagnosis-list {
			margin-top: 20rpx;


			.diagnosis-item {
				background: #fff;
				border-radius: 12rpx;
				padding: 20rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;

					.time {
						color: #666;
						font-size: 28rpx;
					}

					.status {
						font-size: 24rpx;
						padding: 4rpx 16rpx;
						border-radius: 20rpx;

						&.status-success {
							background: #e1f3d8;
							color: #67c23a;
						}

						&.status-warning {
							background: #fdf6ec;
							color: #e6a23c;
						}

						&.status-info {
							background: #ecf5ff;
							color: #409eff;
						}

						&.status-danger {
							background: #fef0f0;
							color: #f56c6c;
						}
					}
				}

				.item-content {
					.info-row {
						display: flex;
						margin-bottom: 10rpx;

						.label {
							color: #666;
							font-size: 28rpx;
							width: 198rpx;
						}

						.value {
							color: #333;
							font-size: 28rpx;
							flex: 1;
						}
					}
				}

				.item-footer {
					margin-top: 20rpx;
					display: flex;
					justify-content: flex-end;
					gap: 20rpx;
				}
			}
		}
	}

	.search-wrap {
		margin-top: 10px;
		height: 70rpx;
		border-radius: 30rpx;
		background: #FFFFFF;
		border: 1px solid #ccc;
	}
</style>