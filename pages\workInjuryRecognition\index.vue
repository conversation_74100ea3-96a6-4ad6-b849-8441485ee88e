<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="工伤认定" />
		<view slot="gBody" class="grace-body section-body">
			<view class="map-body">
				<view class="search-wrap">
					<u--input v-model.trim="postData.keyWord" placeholder="请输入关键字" clearable @input="handleInputChange">
						<template slot="suffix">
							<u-button @click="handleSearch" type="primary" size="mini">搜索</u-button>
						</template>
					</u--input>
				</view>
				<view class="operact">
					<u-button @click="goToPathForm" type="primary" size="mini">新建</u-button>
				</view>
			</view>
			<view class="some-element">
				<view class="element" v-if="dataList.length">
					<view class="ele" v-for="item in dataList" :key="item.id">
						<view class="name row">
							<view class="tit">
								<text class="element-title">{{item.employee_name}}</text>
							</view>
							<view class="status">
								<u-tag v-if="item.status === '0'" type="info" plain size="mini" text="未提交"></u-tag>
								<u-tag v-else-if="item.status === '1'" plain size="mini" text="已提交"></u-tag>
								<u-tag v-else-if="item.status === '2'" plain size="mini" type="success" text="审核通过"></u-tag>
								<u-tag v-else-if="item.status === '3'" plain size="mini" type="error" text="审核不通过"></u-tag>
								<u-tag v-else-if="item.status === '4'" plain size="mini" type="warning" text="已撤销"></u-tag>
							</view>
							<view class="more" @click="goToPathDetail(item)">查看详情
								<image src="../../static/right-jt.png" mode=""></image>
							</view>
						</view>

						<view class="row">
							<view class="title">
								<text>性别：</text>
							</view>
							<view class="content">
								<text>{{item.gender}}</text>
							</view>
						</view>
						<view class="row">
							<view class="title">
								<text>出生年月：</text>
							</view>
							<view class="content">
								<text>{{item.birthday}}</text>
							</view>
						</view>
						<view class="row">
							<view class="title">
								<text>职业、工种或工作岗位：</text>
							</view>
							<view class="content">
								<text>{{item.employee_job}}</text>
							</view>
						</view>
						<view class="row">
							<view class="title">
								<text>参加工作时间：</text>
							</view>
							<view class="content">
								<text>{{item.employee_work_date}}</text>
							</view>
						</view>
						<view class="row">
							<view class="title">
								<text>职业病名称：</text>
							</view>
							<view class="content">
								<text>{{item.disease_name}}</text>
							</view>
						</view>
						<view class="row">
							<view class="title">
								<text>接触职业病危害岗位：</text>
							</view>
							<view class="content">
								<text>{{item.exposure_post}}</text>
							</view>
						</view>
						<view class="row">
							<view class="title">
								<text>接触职业病危害时间：</text>
							</view>
							<view class="content">
								<text>{{item.exposure_time}}</text>
							</view>
						</view>
						<view class="operaction">
							<u-button class="btn" type="primary" v-if="item.status !== '2'" @click="goToPathForm(item)">编辑</u-button>
							<u-button class="btn" type="warning" v-if="item.status === '1' || item.status === '3'"
								@click="handleUndo(item)">撤销</u-button>
							<u-button class="btn" type="error" v-if="item.status !== '2'" @click="handleDelete(item)">删除</u-button>
						</view>
					</view>
				</view>
				<u-loadmore v-else status="nomore" nomoreText="暂无数据" lineColor="#1CD29B"></u-loadmore>
			</view>
			<!-- 撤销提示框 -->
			<u-modal :show="undoShow" title="撤销" content='您确定撤销当前申请吗？' :asyncClose="true" :showCancelButton="true"
				@confirm="handleUndoConfirm" @cancel="undoShow = false"></u-modal>
			<!-- 删除提示框 -->
			<u-modal :show="deleteShow" title="删除" content='您确定删除当前申请吗？' :asyncClose="true" :showCancelButton="true"
				@confirm="handleDeleteConfirm" @cancel="deleteShow = false"></u-modal>
		</view>
	</gracePage>
</template>
S
<script>
	import {
		getWorkInjuryRecognitionList,
		addOrEditWorkInjuryRecognition,
		deleteWorkInjuryRecognition
	} from '../../api/workInjuryRecognition.js'
	
	export default {
		data() {
			return {
				dataList: [],
				postData: {
					keyWord: '',
					curPage: 1,
					pageSize: 9999
				},
				total: 0,
				undoShow: false,
				undoId: undefined,
				deleteShow: false,
				deleteId: undefined
			}
		},
		created() {
			this.getList()
		},
		methods: {
			handleSearch() {
				this.getList()
			},
			async getList() {
				const res = await getWorkInjuryRecognitionList(this.postData)
				this.dataList = res.data.list
				this.total = res.data.total
			},
			handleInputChange(value) {
				if (value == '') {
					this.getList();
				}
			},
			goToPathDetail(item) {
				uni.navigateTo({
					url: `/pages/workInjuryRecognition/detail?_id=${item?._id}`,
				})
			},
			goToPathForm(item) {
				if (item?._id) {
					uni.navigateTo({
						url: `/pages/workInjuryRecognition/add?_id=${item?._id}`
					})
					return;
				}
				uni.navigateTo({
					url: `/pages/workInjuryRecognition/add`
				})
			},
			handleUndo(item) {
				this.undoId = item._id
				this.undoShow = true
			},
			async handleUndoConfirm() {
				if (!this.undoId) return;
				try {
					await addOrEditWorkInjuryRecognition({
						_id: this.undoId,
						status: '4'
					})
					this.getList()
				} catch (error) {
					console.log(error)
					uni.$u.toast('撤销失败')

				} finally {
					this.undoShow = false
				}
			},
			handleDelete(item) {
				this.deleteId = item._id
				this.deleteShow = true
			},
			async handleDeleteConfirm() {
				if (!this.deleteId) return;
				try {
					await deleteWorkInjuryRecognition({
						_id: this.deleteId,
					})
					this.getList()
				} catch (error) {
					console.log(error)
					uni.$u.toast('删除失败')
			
				} finally {
					this.deleteShow = false
				}
			},
		},
	}
</script>


<style lang="scss" scoped>
	.section-body {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: #f5f5f5;

		.map-body {
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			gap: 16px;

			.search-wrap {
				height: 70rpx;
				border-radius: 30rpx;
				background: #FFFFFF;
				flex: 1;
			}
		}

		.some-element {
			margin-top: 20rpx;

			.element {
				border-radius: 5rpx;
				background: #FFFFFF;
				padding: 30rpx;
				box-sizing: border-box;

				.ele {
					width: 100%;
					padding: 30rpx 24rpx;
					box-sizing: border-box;
					border-radius: 8rpx;
					background: rgba(62, 115, 254, 0.06);
					margin-bottom: 24rpx;

					.status {
						display: flex;
						justify-content: flex-start;
					}

					.row {
						margin-bottom: 22rpx;
						display: flex;
						align-items: center;
						width: 100%;


						text {
							font-family: Source Han Sans;
							font-size: 28rpx;
							color: #555555;
							margin-right: 32rpx;
						}

						.title {
							width: 340rpx;
							text-align: right;

							text {}
						}

						.content {
							flex: 1;
						}
					}

					.name {
						width: 100%;
						position: relative;
						display: flex;
						align-items: center;
						gap: 12rpx;

						.element-title {
							// width: 320rpx;
							display: inline-block;
							font-size: 16px;
							font-weight: bold;
							color: #3E73FE;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.tit {
							text-overflow: ellipsis;
							white-space: nowrap;
							flex: 1;
						}

						.more {
							font-size: 12px;
							font-weight: 300;
							color: #3E73FE;
							// position: absolute;
							// right: 0;
							display: flex;
							align-items: center;

							image {
								width: 32rpx;
								height: 24rpx;
							}
						}
					}

					.operaction {
						padding: 32rpx 0;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						gap: 16px;

						.btn {
							width: 150rpx;
							height: 64rpx;
							text-align: center;
							line-height: 64rpx;
							border-radius: 8rpx;
							margin: 0;
						}
					}
				}
			}
		}
	}
</style>