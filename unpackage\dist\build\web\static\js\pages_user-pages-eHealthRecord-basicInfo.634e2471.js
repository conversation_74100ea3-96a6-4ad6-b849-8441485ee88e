(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-basicInfo"],{17561:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-text",{staticClass:"uni-icons",class:[e.customIcons,e.customIcons?e.type:""],style:{color:e.color,"font-size":e.size+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e._onClick.apply(void 0,arguments)}}},[e._v(e._s(e.icons[e.type]))])},n=[]},"20aca":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("d802")),s={getEHealthRecordAuthList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getEHealthRecordAuthList",method:"get",data:e})},handleEHealthRecordAuth:function(e){return(0,n.default)({url:"manage/eHealthRecord/handleEHealthRecordAuth",method:"post",data:e})},getEHealthRecordBaseInfo:function(e){return(0,n.default)({url:"manage/eHealthRecord/getEHealthRecordBaseInfo",method:"get",data:e})},updateEHealthRecordBaseInfo:function(e){return(0,n.default)({url:"manage/eHealthRecord/updateEHealthRecordBaseInfo",method:"post",data:e})},getSupervisionList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getSupervisionList",method:"post",data:e})},postComplaint:function(e){return(0,n.default)({url:"manage/eHealthRecord/postComplaint",method:"post",data:e})},getLaborIsEdit:function(e){return(0,n.default)({url:"manage/eHealthRecord/getLaborIsEdit",method:"get",data:e})},addEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/addEmploymentHistory",method:"post",data:e})},editEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/editEmploymentHistory",method:"post",data:e})},deleteEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/deleteEmploymentHistory",method:"post",data:e})},getEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/getEmploymentHistory",method:"get",data:e})},getDiaList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getDiaList",method:"post",data:e})},getIdentificationList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getIdentificationList",method:"post",data:e})},findHarmFactors:function(e){return(0,n.default)({url:"manage/eHealthRecord/findHarmFactors",method:"get",data:e})}};t.default=s},2289:function(e,t,i){"use strict";(function(e){i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("d4b5"),i("c223"),i("bf0f"),i("22b6"),i("aa9c"),i("dd2b"),i("fd3c"),i("bd06"),i("8f71");var n=a(i("2634")),s=a(i("2fdc")),o=a(i("20aca")),r=a(i("00c9")),l={data:function(){return{isEditing:!1,form:{},editForm:{name:"",gender:"",age:"",IDNum:"",phoneNum:"",contactPhoneNum:"",nativePlace:"",nativePlaceAddress:"",residencePlace:"",residenceAddress:"",nation:"",maritalStatus:"",education:"",bloodType:"",smokingHistory:"",smokingAmount:"",smokingYear:"",smokingMonth:"",drinkingHistory:"",drinkingAmount:"",drinkingYear:"",exerciseHabit:"",currentChildren:"",abortion:"",stillbirth:"",premature:"",abnormalFetus:"",childrenHealth:"",menarcheAge:"",menstruationDays:"",menstruationCycle:"",menopauseAge:"",emergencyContacts:[{_id:"",name:"",relationship:"",phoneNum:""}],allergy:[{_id:"",allergySource:"",allergySymptoms:"",allergyDate:"",treatment:"",effectRecord:""}],pastHistory:[{_id:"",diseaseName:"",diagnosisDate:"",institutionName:"",treatmentProcess:"",outcomeCode:""}],familyHistory:[{_id:"",familyMember:"",diseaseName:"",diagnosisDate:"",institutionName:"",treatmentProcess:""}]},genderOptions:[{label:"男",value:"0"},{label:"女",value:"1"}],marriageOptions:["未婚","已婚","离异","丧偶"],marriageIndex:0,educationOptions:["小学","初中","高中","大专","本科","硕士","博士"],educationIndex:0,bloodTypeOptions:["A型","B型","AB型","O型","其他"],bloodTypeIndex:0,smokingOptions:["不吸烟","偶尔吸烟","经常吸烟","已戒烟"],smokingHistoryIndex:0,drinkingOptions:["不饮酒","偶尔饮酒","经常饮酒","已戒酒"],drinkingHistoryIndex:0,outcomeOptions:["治愈","好转","致残","转院","其他"],careerHistory:[],isEditingCareer:!1,canEditCareer:!1,careerForm:{historyId:"",workUnit:"",entryTime:"",leaveTime:"",workshop:"",station:"",workType:"",harmFactors:[]},medicalHistory:[],occupationalDiseaseRecords:[],postData:{status:"",idCardType:"1",idCardCode:"",pageNum:1,pageSize:100},orgTypes:[{label:"用人单位",value:"qy",checked:!0},{label:"管理单位",value:"jg",checked:!1}],appealForm:{orgType:"qy",orgName:"",content:"",superUserID:""},showUnitDropdown:!1,unitSearchResults:[],searchTimer:null,isSearching:!1,isInputFocused:!1,allergyCollapsed:!0,pastHistoryCollapsed:!0,familyHistoryCollapsed:!0,careerCollapsed:!0,diseaseCollapsed:!0,emergencyContactCollapsed:!0,harmFactorInput:"",harmFactorSearchList:[],harmFactorDropdownVisible:!1,harmFactorSearchTimer:null,isHarmFactorSearching:!1}},created:function(){this.getData(),this.getEmploymentHistory(),this.checkCanEditCareer(),this.getOccupationalDiseaseList()},methods:{getData:function(){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,o.default.getEHealthRecordBaseInfo();case 2:a=i.sent,t.form=a.data,e.log("获取的个人信息数据:",t.form);case 5:case"end":return i.stop()}}),i)})))()},checkCanEditCareer:function(){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,o.default.getLaborIsEdit({enterpriseId:uni.getStorageSync("enterpriseId")});case 3:a=i.sent,a.data&&a.data.length>0?t.canEditCareer=a.data[0].lobarIsEdit||!1:t.canEditCareer=!1,e.log("是否可以编辑职业史:",t.canEditCareer),i.next=12;break;case 8:i.prev=8,i.t0=i["catch"](0),e.error("获取职业史编辑权限失败:",i.t0),t.canEditCareer=!1;case 12:case"end":return i.stop()}}),i,null,[[0,8]])})))()},getEmploymentHistory:function(){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,o.default.getEmploymentHistory({});case 3:a=i.sent,t.careerHistory=a.data||[],e.log("获取的职业史数据:",t.careerHistory),i.next=12;break;case 8:i.prev=8,i.t0=i["catch"](0),e.error("获取职业史失败:",i.t0),t.careerHistory=[];case 12:case"end":return i.stop()}}),i,null,[[0,8]])})))()},handleAddCareer:function(){if(!this.canEditCareer)return uni.showToast({title:"您没有编辑职业史的权限",icon:"none"});this.isEditingCareer=!1,this.careerForm={historyId:"",workUnit:"",entryTime:"",leaveTime:"",workshop:"",station:"",workType:"",harmFactors:[]},this.$refs.careerPopup.open()},handleEditCareer:function(e){if(!this.canEditCareer)return uni.showToast({title:"您没有编辑职业史的权限",icon:"none"});this.isEditingCareer=!0,this.careerForm={historyId:e._id,workUnit:e.workUnit,entryTime:e.entryTime,leaveTime:e.leaveTime,workshop:e.workshop,station:e.station,workType:e.workType,harmFactors:e.harmFactors?JSON.parse(JSON.stringify(e.harmFactors)):[]},this.$refs.careerPopup.open()},handleDeleteCareer:function(t){var i=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i.canEditCareer){a.next=2;break}return a.abrupt("return",uni.showToast({title:"您没有编辑职业史的权限",icon:"none"}));case 2:uni.showModal({title:"提示",content:"确定要删除该职业史记录吗？",success:function(){var a=(0,s.default)((0,n.default)().mark((function a(s){var r;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!s.confirm){a.next=15;break}return a.prev=1,uni.showLoading({title:"删除中..."}),a.next=5,o.default.deleteEmploymentHistory({historyId:t._id});case 5:r=a.sent,uni.hideLoading(),200===r.status?(uni.showToast({title:"删除成功",icon:"success"}),i.getEmploymentHistory()):uni.showToast({title:r.msg||"删除失败",icon:"none"}),a.next=15;break;case 10:a.prev=10,a.t0=a["catch"](1),uni.hideLoading(),uni.showToast({title:"删除失败，请稍后重试",icon:"none"}),e.error("删除职业史失败:",a.t0);case 15:case"end":return a.stop()}}),a,null,[[1,10]])})));return function(e){return a.apply(this,arguments)}}()});case 3:case"end":return a.stop()}}),a)})))()},closeCareerPopup:function(){this.$refs.careerPopup.close()},handleEntryTimeChange:function(e){this.careerForm.entryTime=e.detail.value},handleLeaveTimeChange:function(e){this.careerForm.leaveTime=e.detail.value},submitCareer:function(){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t.careerForm.workUnit){i.next=2;break}return i.abrupt("return",uni.showToast({title:"请输入工作单位",icon:"none"}));case 2:if(t.careerForm.entryTime){i.next=4;break}return i.abrupt("return",uni.showToast({title:"请选择入职时间",icon:"none"}));case 4:if(i.prev=4,uni.showLoading({title:t.isEditingCareer?"更新中...":"添加中..."}),!t.isEditingCareer){i.next=12;break}return i.next=9,o.default.editEmploymentHistory({historyId:t.careerForm.historyId,workUnit:t.careerForm.workUnit,entryTime:t.careerForm.entryTime,leaveTime:t.careerForm.leaveTime,workshop:t.careerForm.workshop,station:t.careerForm.station,workType:t.careerForm.workType,harmFactors:t.careerForm.harmFactors});case 9:a=i.sent,i.next=15;break;case 12:return i.next=14,o.default.addEmploymentHistory({workUnit:t.careerForm.workUnit,entryTime:t.careerForm.entryTime,leaveTime:t.careerForm.leaveTime,workshop:t.careerForm.workshop,station:t.careerForm.station,workType:t.careerForm.workType,harmFactors:t.careerForm.harmFactors});case 14:a=i.sent;case 15:uni.hideLoading(),200===a.status?(uni.showToast({title:t.isEditingCareer?"更新成功":"添加成功",icon:"success"}),t.closeCareerPopup(),t.getEmploymentHistory()):uni.showToast({title:a.msg||(t.isEditingCareer?"更新失败":"添加失败"),icon:"none"}),i.next=24;break;case 19:i.prev=19,i.t0=i["catch"](4),uni.hideLoading(),uni.showToast({title:t.isEditingCareer?"更新失败，请稍后重试":"添加失败，请稍后重试",icon:"none"}),e.error(t.isEditingCareer?"更新职业史失败:":"添加职业史失败:",i.t0);case 24:case"end":return i.stop()}}),i,null,[[4,19]])})))()},getBasicInfoValue:function(e){return this.form&&this.form.employeeBasicInfo&&this.form.employeeBasicInfo[e]||""},getSmokeYears:function(){var e=this.getBasicInfoValue("smokingYear"),t=this.getBasicInfoValue("smokingMonth");return e||t?e&&t?"".concat(e,"年").concat(t,"月"):e?"".concat(e,"年"):t?"".concat(t,"月"):"":""},getEmergencyContacts:function(){if(!this.form||!this.form.employeeBasicInfo||!this.form.employeeBasicInfo.emergencyContact)return[];var e=Array.isArray(this.form.employeeBasicInfo.emergencyContact)?this.form.employeeBasicInfo.emergencyContact:[this.form.employeeBasicInfo.emergencyContact];return!e.length||e.every((function(e){return!e||Object.values(e).every((function(e){return!e}))}))?[]:e},addEmergencyContact:function(){this.editForm.emergencyContacts.push({_id:"",name:"",relationship:"",phoneNum:""})},removeEmergencyContact:function(e){this.editForm.emergencyContacts.splice(e,1)},handleEdit:function(){this.isEditing=!0;var e=this.form.employeeBasicInfo||{},t=e.emergencyContact||[],i=e.allergy||[],a=e.pastHistory||[],n=e.familyHistory||[];this.editForm={name:this.form.name||"",gender:this.form.gender||"",age:this.form.age||"",IDNum:this.form.IDNum||"",phoneNum:this.form.phoneNum||"",contactPhoneNum:e.contactPhoneNum||"",nativePlace:e.nativePlace||"",nativePlaceAddress:e.nativePlaceAddress||"",residencePlace:e.residencePlace||"",residenceAddress:e.residenceAddress||"",nation:e.nation||"",maritalStatus:e.maritalStatus||this.form.marriage||"",education:e.education||"",bloodType:e.bloodType||"",smokingHistory:e.smokingHistory||"",smokingAmount:e.smokingAmount||"",smokingYear:e.smokingYear||"",smokingMonth:e.smokingMonth||"",drinkingHistory:e.drinkingHistory||"",drinkingAmount:e.drinkingAmount||"",drinkingYear:e.drinkingYear||"",exerciseHabit:e.exerciseHabit||"",currentChildren:e.currentChildren||"",abortion:e.abortion||"",stillbirth:e.stillbirth||"",premature:e.premature||"",abnormalFetus:e.abnormalFetus||"",childrenHealth:e.childrenHealth||"",menarcheAge:e.menarcheAge||"",menstruationDays:e.menstruationDays||"",menstruationCycle:e.menstruationCycle||"",menopauseAge:e.menopauseAge||"",emergencyContacts:Array.isArray(t)&&t.length>0?t.map((function(e){return{_id:e._id||"",name:e.name||"",relationship:e.relationship||"",phoneNum:e.phoneNum||""}})):[{_id:"",name:"",relationship:"",phoneNum:""}],allergy:Array.isArray(i)&&i.length>0?i.map((function(e){return{_id:e._id||"",allergySource:e.allergySource||"",allergySymptoms:e.allergySymptoms||"",allergyDate:e.allergyDate||"",treatment:e.treatment||"",effectRecord:e.effectRecord||""}})):[{_id:"",allergySource:"",allergySymptoms:"",allergyDate:"",treatment:"",effectRecord:""}],pastHistory:Array.isArray(a)&&a.length>0?a.map((function(e){return{_id:e._id||"",diseaseName:e.diseaseName||"",diagnosisDate:e.diagnosisDate||"",institutionName:e.institutionName||"",treatmentProcess:e.treatmentProcess||"",outcomeCode:e.outcomeCode||""}})):[{_id:"",diseaseName:"",diagnosisDate:"",institutionName:"",treatmentProcess:"",outcomeCode:""}],familyHistory:Array.isArray(n)&&n.length>0?n.map((function(e){return{_id:e._id||"",familyMember:e.familyMember||"",diseaseName:e.diseaseName||"",diagnosisDate:e.diagnosisDate||"",institutionName:e.institutionName||"",treatmentProcess:e.treatmentProcess||""}})):[{_id:"",familyMember:"",diseaseName:"",diagnosisDate:"",institutionName:"",treatmentProcess:""}]},this.setPickerIndexes()},setPickerIndexes:function(){var e=this.editForm.maritalStatus;if(e){var t=this.marriageOptions.findIndex((function(t){return t===e}));this.marriageIndex=t>-1?t:0}var i=this.editForm.education;if(i){var a=this.educationOptions.findIndex((function(e){return e===i}));this.educationIndex=a>-1?a:0}var n=this.editForm.bloodType;if(n){var s=this.bloodTypeOptions.findIndex((function(e){return e===n}));this.bloodTypeIndex=s>-1?s:0}var o=this.editForm.smokingHistory;if(o){var r=this.smokingOptions.findIndex((function(e){return e===o}));this.smokingHistoryIndex=r>-1?r:0}var l=this.editForm.drinkingHistory;if(l){var c=this.drinkingOptions.findIndex((function(e){return e===l}));this.drinkingHistoryIndex=c>-1?c:0}},handleCancel:function(){this.isEditing=!1,this.getData()},handleSave:function(){var e,t={_id:(null===(e=this.form.employeeBasicInfo)||void 0===e?void 0:e._id)||"",contactPhoneNum:this.editForm.contactPhoneNum,nativePlace:this.editForm.nativePlace,nativePlaceAddress:this.editForm.nativePlaceAddress,residencePlace:this.editForm.residencePlace,residenceAddress:this.editForm.residenceAddress,nation:this.editForm.nation,maritalStatus:this.editForm.maritalStatus,education:this.editForm.education,bloodType:this.editForm.bloodType,smokingHistory:this.editForm.smokingHistory,smokingAmount:this.editForm.smokingAmount,smokingYear:this.editForm.smokingYear,smokingMonth:this.editForm.smokingMonth,drinkingHistory:this.editForm.drinkingHistory,drinkingAmount:this.editForm.drinkingAmount,drinkingYear:this.editForm.drinkingYear,exerciseHabit:this.editForm.exerciseHabit,currentChildren:this.editForm.currentChildren,abortion:this.editForm.abortion,stillbirth:this.editForm.stillbirth,premature:this.editForm.premature,abnormalFetus:this.editForm.abnormalFetus,childrenHealth:this.editForm.childrenHealth,menarcheAge:this.editForm.menarcheAge,menstruationDays:this.editForm.menstruationDays,menstruationCycle:this.editForm.menstruationCycle,menopauseAge:this.editForm.menopauseAge,emergencyContact:this.editForm.emergencyContacts.map((function(e){return{_id:e._id||void 0,name:e.name,relationship:e.relationship,phoneNum:e.phoneNum}})),allergy:this.editForm.allergy.map((function(e){return{_id:e._id||void 0,allergySource:e.allergySource,allergySymptoms:e.allergySymptoms,allergyDate:e.allergyDate,treatment:e.treatment,effectRecord:e.effectRecord}})),pastHistory:this.editForm.pastHistory.map((function(e){return{_id:e._id||void 0,diseaseName:e.diseaseName,diagnosisDate:e.diagnosisDate,institutionName:e.institutionName,treatmentProcess:e.treatmentProcess,outcomeCode:e.outcomeCode}})),familyHistory:this.editForm.familyHistory.map((function(e){return{_id:e._id||void 0,familyMember:e.familyMember,diseaseName:e.diseaseName,diagnosisDate:e.diagnosisDate,institutionName:e.institutionName,treatmentProcess:e.treatmentProcess}}))},i={_id:this.form._id,name:this.editForm.name,gender:this.editForm.gender,age:this.editForm.age,IDNum:this.editForm.IDNum,phoneNum:this.editForm.phoneNum,employeeBasicInfo:t};this.saveData(i)},saveData:function(t){var i=this;return(0,s.default)((0,n.default)().mark((function a(){var s;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,uni.showLoading({title:"保存中..."}),a.next=4,o.default.updateEHealthRecordBaseInfo(t);case 4:s=a.sent,uni.hideLoading(),200===s.status?(uni.showToast({title:"保存成功",icon:"success"}),i.isEditing=!1,i.getData()):uni.showToast({title:s.msg||"保存失败",icon:"none"}),a.next=14;break;case 9:a.prev=9,a.t0=a["catch"](0),uni.hideLoading(),uni.showToast({title:"保存失败，请稍后重试",icon:"none"}),e.error("保存数据失败:",a.t0);case 14:case"end":return a.stop()}}),a,null,[[0,9]])})))()},handleGenderChange:function(e){var t=e.detail.value;this.editForm.gender=this.genderOptions[t].value},handleMarriageChange:function(e){var t=e.detail.value;this.marriageIndex=t,this.editForm.maritalStatus=this.marriageOptions[t]},handleEducationChange:function(e){var t=e.detail.value;this.educationIndex=t,this.editForm.education=this.educationOptions[t]},handleBloodTypeChange:function(e){var t=e.detail.value;this.bloodTypeIndex=t,this.editForm.bloodType=this.bloodTypeOptions[t]},handleSmokingHistoryChange:function(e){var t=e.detail.value;this.smokingHistoryIndex=t,this.editForm.smokingHistory=this.smokingOptions[t]},handleDrinkingHistoryChange:function(e){var t=e.detail.value;this.drinkingHistoryIndex=t,this.editForm.drinkingHistory=this.drinkingOptions[t]},showAppealPopup:function(){this.$refs.appealPopup.open()},closeAppealPopup:function(){this.$refs.appealPopup.close()},handleTypeChange:function(e){this.appealForm.orgType=e.detail.value,this.appealForm.orgName="",this.showUnitDropdown=!1},handleUnitSearch:function(t){var i=this,a=t.detail.value;e.log("搜索关键字:",a),this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((0,s.default)((0,n.default)().mark((function t(){var s;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a){t.next=3;break}return i.showUnitDropdown=!1,t.abrupt("return");case 3:return i.isSearching=!0,i.showUnitDropdown=!0,t.prev=5,t.next=8,o.default.getSupervisionList({cname:a});case 8:s=t.sent,i.unitSearchResults=s.data,t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](5),e.error("搜索失败:",t.t0),i.unitSearchResults=[];case 16:return t.prev=16,i.isSearching=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,null,[[5,12,16,19]])}))),300)},selectUnit:function(e){this.appealForm.orgName=e.cname,"jg"===this.appealForm.orgType&&(this.appealForm.superUserID=e._id),this.showUnitDropdown=!1},submitAppeal:function(){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a,s;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if("jg"!==t.appealForm.orgType||t.appealForm.orgName){i.next=3;break}return uni.showToast({title:"请输入申诉单位名称",icon:"none"}),i.abrupt("return");case 3:if(t.appealForm.content){i.next=6;break}return uni.showToast({title:"请输入申诉内容",icon:"none"}),i.abrupt("return");case 6:if(a={orgType:t.appealForm.orgType,orgName:t.appealForm.orgName,content:t.appealForm.content},"jg"!==t.appealForm.orgType){i.next=11;break}if(a.superUserID=t.appealForm.superUserID,a.superUserID){i.next=11;break}return i.abrupt("return",uni.showToast({title:"请选择正确的监管单位",icon:"none"}));case 11:return e.log("申诉参数:",a),i.next=14,o.default.postComplaint(a);case 14:if(s=i.sent,200===s.status){i.next=18;break}return uni.showToast({title:s.msg||"申诉提交失败",icon:"error"}),i.abrupt("return");case 18:uni.showToast({title:"提交成功",icon:"success"}),t.appealForm=t.$options.data().appealForm,t.closeAppealPopup();case 21:case"end":return i.stop()}}),i)})))()},getGender:function(e){return"0"==e?"男":"1"==e?"女":"未知"},getOccupationalDiseaseList:function(){var t=this;return(0,s.default)((0,n.default)().mark((function i(){var a,s;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,t.postData.idCardCode=(null===(a=t.form)||void 0===a?void 0:a.IDNum)||uni.getStorageSync("idNo")||"",i.next=4,o.default.getDiaList(t.postData);case 4:s=i.sent,t.occupationalDiseaseRecords=s.data.filter((function(e){return!0===e.hasOccupationalDisease}))||[],e.log("获取的职业病记录数据:",t.occupationalDiseaseRecords),i.next=13;break;case 9:i.prev=9,i.t0=i["catch"](0),e.error("获取职业病记录失败:",i.t0),t.occupationalDiseaseRecords=[];case 13:case"end":return i.stop()}}),i,null,[[0,9]])})))()},formatDateYear:function(e){return e?(0,r.default)(e).format("YYYY")+"年":""},formatDateMonthDay:function(e){return e?(0,r.default)(e).format("MM")+"月"+(0,r.default)(e).format("DD")+"日":""},formatDate:function(e){return e?(0,r.default)(e).format("YYYY-MM-DD"):""},getOccupationalDiseaseNames:function(e){return e&&Array.isArray(e)&&0!==e.length?e.map((function(e){return e.name})).join("、"):""},handleInputFocus:function(){this.isInputFocused=!0,this.appealForm.orgName&&(this.showUnitDropdown=!0)},handleInputBlur:function(){var e=this;setTimeout((function(){e.isInputFocused=!1,e.showUnitDropdown=!1}),200)},getAllergyHistory:function(){if(!this.form||!this.form.employeeBasicInfo||!this.form.employeeBasicInfo.allergy)return[];var e=Array.isArray(this.form.employeeBasicInfo.allergy)?this.form.employeeBasicInfo.allergy:[this.form.employeeBasicInfo.allergy];return!e.length||e.every((function(e){return!e||Object.values(e).every((function(e){return!e}))}))?[]:e},addAllergy:function(){this.editForm.allergy.push({_id:"",allergySource:"",allergySymptoms:"",allergyDate:"",treatment:"",effectRecord:""})},removeAllergy:function(e){this.editForm.allergy.splice(e,1)},handleAllergyDateChange:function(e,t){this.editForm.allergy[t].allergyDate=e.detail.value},getPastHistory:function(){if(!this.form||!this.form.employeeBasicInfo||!this.form.employeeBasicInfo.pastHistory)return[];var e=Array.isArray(this.form.employeeBasicInfo.pastHistory)?this.form.employeeBasicInfo.pastHistory:[this.form.employeeBasicInfo.pastHistory];return!e.length||e.every((function(e){return!e||Object.values(e).every((function(e){return!e}))}))?[]:e},addPastHistory:function(){this.editForm.pastHistory.push({_id:"",diseaseName:"",diagnosisDate:"",institutionName:"",treatmentProcess:"",outcomeCode:""})},removePastHistory:function(e){this.editForm.pastHistory.splice(e,1)},handlePastHistoryDateChange:function(e,t){this.editForm.pastHistory[t].diagnosisDate=e.detail.value},getOutcomeIndex:function(e){var t=this;if(!e)return 0;var i=this.outcomeOptions.findIndex((function(i){return i===t.getOutcomeText(e)}));return i>-1?i:0},getOutcomeText:function(e){return{1:"治愈",2:"好转",3:"致残",5:"转院",99:"其他"}[e]||""},handleOutcomeChange:function(e,t){this.editForm.pastHistory[t].outcomeCode={"治愈":"1","好转":"2","致残":"3","转院":"5","其他":"99"}[this.outcomeOptions[e.detail.value]]},getFamilyHistory:function(){if(!this.form||!this.form.employeeBasicInfo||!this.form.employeeBasicInfo.familyHistory)return[];var e=Array.isArray(this.form.employeeBasicInfo.familyHistory)?this.form.employeeBasicInfo.familyHistory:[this.form.employeeBasicInfo.familyHistory];return!e.length||e.every((function(e){return!e||Object.values(e).every((function(e){return!e}))}))?[]:e},addFamilyHistory:function(){this.editForm.familyHistory.push({_id:"",familyMember:"",diseaseName:"",diagnosisDate:"",institutionName:"",treatmentProcess:""})},removeFamilyHistory:function(e){this.editForm.familyHistory.splice(e,1)},handleFamilyHistoryDateChange:function(e,t){this.editForm.familyHistory[t].diagnosisDate=e.detail.value},handleHarmFactorInput:function(e){var t,i,a=this,n=null!==(t=null===(i=e.detail)||void 0===i?void 0:i.value)&&void 0!==t?t:e;this.harmFactorInput=n,this.harmFactorSearchTimer&&clearTimeout(this.harmFactorSearchTimer),this.harmFactorSearchTimer=setTimeout((function(){a.searchHarmFactors()}),300)},handleHarmFactorFocus:function(){this.harmFactorDropdownVisible=!0,this.searchHarmFactors()},searchHarmFactors:function(){var e=this;return(0,s.default)((0,n.default)().mark((function t(){var i,a;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isHarmFactorSearching=!0,t.prev=1,t.next=4,o.default.findHarmFactors({query:{name:e.harmFactorInput},pageSize:999});case 4:i=t.sent,a=i.data&&i.data.data||[],e.harmFactorSearchList=a.map((function(e){return{_id:e._id,code:e.code,category:e.catetory,name:e.harmFactorName,otherName:e.otherName||""}})),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),e.harmFactorSearchList=[];case 12:return t.prev=12,e.isHarmFactorSearching=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[1,9,12,15]])})))()},onHarmFactorBlur:function(){var e=this;setTimeout((function(){e.harmFactorDropdownVisible=!1}),200)},selectHarmFactor:function(t){e.log("选择的危害因素:",t),this.careerForm.harmFactors.some((function(e){return e._id===t._id}))||this.careerForm.harmFactors.push({_id:t._id,code:t.code,category:t.category,name:t.name})},removeHarmFactor:function(e){this.careerForm.harmFactors.splice(e,1)}}};t.default=l}).call(this,i("ba7c")["default"])},"2cbd":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){return a}));var a={gracePage:i("3d08").default,uniIcons:i("634b").default,uniPopup:i("46c7").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[i("my-header",{attrs:{slot:"gHeader",title:"个人基本信息"},slot:"gHeader"}),i("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title"},[e._v("基础信息")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("姓名")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入姓名"},model:{value:e.editForm.name,callback:function(t){e.$set(e.editForm,"name",t)},expression:"editForm.name"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.form.name))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("性别")]),e.isEditing?i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.editForm.gender,range:e.genderOptions,"range-key":"label"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleGenderChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.getGender(e.editForm.gender)))])],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getGender(e.form.gender)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("年龄")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入年龄",type:"number"},model:{value:e.editForm.age,callback:function(t){e.$set(e.editForm,"age",t)},expression:"editForm.age"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.form.age))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("身份证号码")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入身份证号码"},model:{value:e.editForm.IDNum,callback:function(t){e.$set(e.editForm,"IDNum",t)},expression:"editForm.IDNum"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.form.IDNum))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("手机号码")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入手机号码",type:"number"},model:{value:e.editForm.phoneNum,callback:function(t){e.$set(e.editForm,"phoneNum",t)},expression:"editForm.phoneNum"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.form.phoneNum))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("联系电话")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入联系电话",type:"number"},model:{value:e.editForm.contactPhoneNum,callback:function(t){e.$set(e.editForm,"contactPhoneNum",t)},expression:"editForm.contactPhoneNum"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("contactPhoneNum")))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("户籍所在地")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入户籍所在地"},model:{value:e.editForm.nativePlace,callback:function(t){e.$set(e.editForm,"nativePlace",t)},expression:"editForm.nativePlace"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("nativePlace")))])],1),i("v-uni-view",{staticClass:"info-item full-width"},[i("v-uni-text",{staticClass:"label"},[e._v("户籍地址")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入户籍地址"},model:{value:e.editForm.nativePlaceAddress,callback:function(t){e.$set(e.editForm,"nativePlaceAddress",t)},expression:"editForm.nativePlaceAddress"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("nativePlaceAddress")))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("常住所在地")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入常住所在地"},model:{value:e.editForm.residencePlace,callback:function(t){e.$set(e.editForm,"residencePlace",t)},expression:"editForm.residencePlace"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("residencePlace")))])],1),i("v-uni-view",{staticClass:"info-item full-width"},[i("v-uni-text",{staticClass:"label"},[e._v("常住地址")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入常住地址"},model:{value:e.editForm.residenceAddress,callback:function(t){e.$set(e.editForm,"residenceAddress",t)},expression:"editForm.residenceAddress"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("residenceAddress")))])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title"},[e._v("人口学与社会经济学信息")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("民族")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入民族"},model:{value:e.editForm.nation,callback:function(t){e.$set(e.editForm,"nation",t)},expression:"editForm.nation"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("nation")||"汉族"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("婚姻状况")]),e.isEditing?i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.marriageIndex,range:e.marriageOptions},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleMarriageChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.editForm.maritalStatus||"请选择"))])],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("maritalStatus")||e.form.marriage))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("文化程度")]),e.isEditing?i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.educationIndex,range:e.educationOptions},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleEducationChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.editForm.education||"请选择"))])],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("education")||"-"))])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title-bar",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.careerCollapsed=!e.careerCollapsed}}},[i("v-uni-view",{staticClass:"section-title"},[e._v("职业史")]),i("uni-icons",{attrs:{type:e.careerCollapsed?"arrowdown":"arrowup",size:"18",color:"#2979ff"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.careerCollapsed,expression:"!careerCollapsed"}]},[e.careerHistory&&e.careerHistory.length>0?i("v-uni-view",[i("v-uni-view",{staticClass:"career-list"},e._l(e.careerHistory,(function(t,a){return i("v-uni-view",{key:a,staticClass:"career-item"},[i("v-uni-view",{staticClass:"career-header"},[i("v-uni-text",{staticClass:"company"},[e._v(e._s(t.workUnit))]),i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.entryTime)+" - "+e._s(t.leaveTime))])],1),i("v-uni-view",{staticClass:"career-content"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("车间")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.workshop))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("岗位")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.station))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("工种")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.workType))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("危害因素")]),i("v-uni-view",{staticClass:"value",staticStyle:{"flex-wrap":"wrap",display:"flex"}},e._l(t.harmFactors,(function(t){return i("v-uni-view",{key:t._id,staticClass:"tag",staticStyle:{"margin-right":"8rpx"}},[e._v(e._s(t.name))])})),1)],1)],1),e.isEditing?e._e():i("v-uni-view",{staticClass:"career-actions"},[i("v-uni-button",{staticClass:"action-btn edit",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.handleEditCareer(t)}}},[i("uni-icons",{attrs:{type:"compose",size:"16",color:"#2979ff"}}),i("v-uni-text",[e._v("编辑")])],1),i("v-uni-button",{staticClass:"action-btn delete",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.handleDeleteCareer(t)}}},[i("uni-icons",{attrs:{type:"trash",size:"16",color:"#FF4D4F"}}),i("v-uni-text",[e._v("删除")])],1)],1)],1)})),1)],1):i("v-uni-view",{staticClass:"empty-history"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无职业史记录")])],1),e.isEditing?e._e():i("v-uni-view",{staticClass:"career-add-fab",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.handleAddCareer.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"plusempty",size:"28",color:"#fff"}})],1)],1)],1),i("uni-popup",{ref:"careerPopup",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"career-popup"},[i("v-uni-view",{staticClass:"popup-title"},[e._v(e._s(e.isEditingCareer?"编辑职业史":"添加职业史"))]),i("v-uni-view",{staticClass:"popup-content"},[i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("工作单位")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入工作单位"},model:{value:e.careerForm.workUnit,callback:function(t){e.$set(e.careerForm,"workUnit",t)},expression:"careerForm.workUnit"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("入职时间")]),i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{mode:"date",value:e.careerForm.entryTime},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleEntryTimeChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.careerForm.entryTime||"请选择入职时间"))])],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("离职时间")]),i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{mode:"date",value:e.careerForm.leaveTime},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleLeaveTimeChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.careerForm.leaveTime||"请选择离职时间"))])],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("车间")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入车间"},model:{value:e.careerForm.workshop,callback:function(t){e.$set(e.careerForm,"workshop",t)},expression:"careerForm.workshop"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("岗位")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入岗位"},model:{value:e.careerForm.station,callback:function(t){e.$set(e.careerForm,"station",t)},expression:"careerForm.station"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("工种")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入工种"},model:{value:e.careerForm.workType,callback:function(t){e.$set(e.careerForm,"workType",t)},expression:"careerForm.workType"}})],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("危害因素")]),e.careerForm.harmFactors&&e.careerForm.harmFactors.length?i("v-uni-view",{staticClass:"tags-wrapper"},e._l(e.careerForm.harmFactors,(function(t,a){return i("v-uni-view",{key:t._id,staticClass:"tag"},[e._v(e._s(t.name)),i("uni-icons",{attrs:{type:"close",size:"14",color:"#FF4D4F"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.removeHarmFactor(a)}}})],1)})),1):e._e(),i("v-uni-view",{staticClass:"harm-factor-input-wrapper"},[i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入危害因素名称"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleHarmFactorInput.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.handleHarmFactorFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onHarmFactorBlur.apply(void 0,arguments)}},model:{value:e.harmFactorInput,callback:function(t){e.harmFactorInput=t},expression:"harmFactorInput"}}),e.harmFactorDropdownVisible?i("v-uni-view",{staticClass:"search-dropdown",staticStyle:{position:"relative"}},[e.isHarmFactorSearching?i("v-uni-view",{staticClass:"dropdown-loading"},[i("uni-icons",{attrs:{type:"spinner-cycle",size:"16",color:"#999999"}}),i("v-uni-text",[e._v("搜索中...")])],1):e.harmFactorSearchList&&0===e.harmFactorSearchList.length?i("v-uni-view",{staticClass:"dropdown-empty"},[i("v-uni-text",[e._v("暂无相关危害因素")])],1):i("v-uni-view",{staticClass:"dropdown-list"},e._l(e.harmFactorSearchList,(function(t){return i("v-uni-view",{key:t._id,staticClass:"dropdown-item",on:{mousedown:function(i){i.preventDefault(),arguments[0]=i=e.$handleEvent(i),e.selectHarmFactor(t)}}},[e._v(e._s(t.name)),t.otherName?i("span",{staticStyle:{color:"#999"}},[e._v("(别名:"+e._s(t.otherName)+")")]):e._e()])})),1)],1):e._e()],1)],1)],1),i("v-uni-view",{staticClass:"popup-footer"},[i("v-uni-button",{staticClass:"popup-btn cancel",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeCareerPopup.apply(void 0,arguments)}}},[e._v("取消")]),i("v-uni-button",{staticClass:"popup-btn submit",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitCareer.apply(void 0,arguments)}}},[e._v("确定")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title-bar",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.diseaseCollapsed=!e.diseaseCollapsed}}},[i("v-uni-view",{staticClass:"section-title"},[e._v("职业病发生情况信息")]),i("uni-icons",{attrs:{type:e.diseaseCollapsed?"arrowdown":"arrowup",size:"18",color:"#2979ff"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.diseaseCollapsed,expression:"!diseaseCollapsed"}]},[e.occupationalDiseaseRecords&&e.occupationalDiseaseRecords.length>0?i("v-uni-view",[i("v-uni-view",{staticClass:"career-list"},e._l(e.occupationalDiseaseRecords,(function(t,a){return i("v-uni-view",{key:a,staticClass:"career-item"},[i("v-uni-view",{staticClass:"career-content"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断日期")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.formatDate(t.diagnosisDate)))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断结论")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.diagnosisConclusionDescription))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断机构")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.diagnosisInstitution))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("职业病")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getOccupationalDiseaseNames(t.occupationalDisease)))])],1)],1)],1)})),1)],1):i("v-uni-view",{staticClass:"empty-history"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无职业病记录")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title-bar",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.allergyCollapsed=!e.allergyCollapsed}}},[i("v-uni-view",{staticClass:"section-title"},[e._v("过敏史")]),i("uni-icons",{attrs:{type:e.allergyCollapsed?"arrowdown":"arrowup",size:"18",color:"#2979ff"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.allergyCollapsed,expression:"!allergyCollapsed"}]},[e.isEditing?[i("v-uni-view",{staticClass:"history-form"},[e._l(e.editForm.allergy,(function(t,a){return i("v-uni-view",{key:a,staticClass:"history-form-item"},[i("v-uni-view",{staticClass:"history-form-header"},[i("v-uni-text",{staticClass:"history-form-title"},[e._v("过敏史 "+e._s(a+1))]),e.editForm.allergy.length>1?i("v-uni-button",{staticClass:"delete-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.removeAllergy(a)}}},[i("uni-icons",{attrs:{type:"trash",size:"16",color:"#FF4D4F"}})],1):e._e()],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("过敏原")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入过敏原"},model:{value:t.allergySource,callback:function(i){e.$set(t,"allergySource",i)},expression:"item.allergySource"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("过敏症状")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入过敏症状"},model:{value:t.allergySymptoms,callback:function(i){e.$set(t,"allergySymptoms",i)},expression:"item.allergySymptoms"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("过敏日期")]),i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{mode:"date",value:t.allergyDate},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.handleAllergyDateChange(t,a)}.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(t.allergyDate||"请选择过敏日期"))])],1)],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("处理措施")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入处理措施"},model:{value:t.treatment,callback:function(i){e.$set(t,"treatment",i)},expression:"item.treatment"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("效果记录")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入效果记录"},model:{value:t.effectRecord,callback:function(i){e.$set(t,"effectRecord",i)},expression:"item.effectRecord"}})],1)],1)})),i("v-uni-button",{staticClass:"add-history-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.addAllergy.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"plusempty",size:"16",color:"#2979ff"}}),i("v-uni-text",[e._v("添加过敏史")])],1)],2)]:[e.getAllergyHistory().length>0?i("v-uni-view",{staticClass:"history-list"},e._l(e.getAllergyHistory(),(function(t,a){return i("v-uni-view",{key:a,staticClass:"history-item"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("过敏原")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.allergySource))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("过敏症状")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.allergySymptoms))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("过敏日期")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.allergyDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("处理措施")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.treatment))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("效果记录")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.effectRecord))])],1)],1)})),1):i("v-uni-view",{staticClass:"empty-history"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无过敏史记录")])],1)]],2)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title-bar",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.pastHistoryCollapsed=!e.pastHistoryCollapsed}}},[i("v-uni-view",{staticClass:"section-title"},[e._v("既往史")]),i("uni-icons",{attrs:{type:e.pastHistoryCollapsed?"arrowdown":"arrowup",size:"18",color:"#2979ff"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.pastHistoryCollapsed,expression:"!pastHistoryCollapsed"}]},[e.isEditing?[i("v-uni-view",{staticClass:"history-form"},[e._l(e.editForm.pastHistory,(function(t,a){return i("v-uni-view",{key:a,staticClass:"history-form-item"},[i("v-uni-view",{staticClass:"history-form-header"},[i("v-uni-text",{staticClass:"history-form-title"},[e._v("既往史 "+e._s(a+1))]),e.editForm.pastHistory.length>1?i("v-uni-button",{staticClass:"delete-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.removePastHistory(a)}}},[i("uni-icons",{attrs:{type:"trash",size:"16",color:"#FF4D4F"}})],1):e._e()],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("疾病名称")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入疾病名称"},model:{value:t.diseaseName,callback:function(i){e.$set(t,"diseaseName",i)},expression:"item.diseaseName"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断日期")]),i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{mode:"date",value:t.diagnosisDate},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.handlePastHistoryDateChange(t,a)}.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(t.diagnosisDate||"请选择诊断日期"))])],1)],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("机构名称")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入机构名称"},model:{value:t.institutionName,callback:function(i){e.$set(t,"institutionName",i)},expression:"item.institutionName"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("治疗经过")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入治疗经过"},model:{value:t.treatmentProcess,callback:function(i){e.$set(t,"treatmentProcess",i)},expression:"item.treatmentProcess"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("转归情况")]),i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.getOutcomeIndex(t.outcomeCode),range:e.outcomeOptions},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.handleOutcomeChange(t,a)}.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.getOutcomeText(t.outcomeCode)||"请选择转归情况"))])],1)],1)],1)})),i("v-uni-button",{staticClass:"add-history-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.addPastHistory.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"plusempty",size:"16",color:"#2979ff"}}),i("v-uni-text",[e._v("添加既往史")])],1)],2)]:[e.getPastHistory().length>0?i("v-uni-view",{staticClass:"history-list"},e._l(e.getPastHistory(),(function(t,a){return i("v-uni-view",{key:a,staticClass:"history-item"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("疾病名称")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.diseaseName))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断日期")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.diagnosisDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("机构名称")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.institutionName))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("治疗经过")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.treatmentProcess))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("转归情况")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getOutcomeText(t.outcomeCode)))])],1)],1)})),1):i("v-uni-view",{staticClass:"empty-history"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无既往史记录")])],1)]],2)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title-bar",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.familyHistoryCollapsed=!e.familyHistoryCollapsed}}},[i("v-uni-view",{staticClass:"section-title"},[e._v("家族史")]),i("uni-icons",{attrs:{type:e.familyHistoryCollapsed?"arrowdown":"arrowup",size:"18",color:"#2979ff"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.familyHistoryCollapsed,expression:"!familyHistoryCollapsed"}]},[e.isEditing?[i("v-uni-view",{staticClass:"history-form"},[e._l(e.editForm.familyHistory,(function(t,a){return i("v-uni-view",{key:a,staticClass:"history-form-item"},[i("v-uni-view",{staticClass:"history-form-header"},[i("v-uni-text",{staticClass:"history-form-title"},[e._v("家族史 "+e._s(a+1))]),e.editForm.familyHistory.length>1?i("v-uni-button",{staticClass:"delete-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.removeFamilyHistory(a)}}},[i("uni-icons",{attrs:{type:"trash",size:"16",color:"#FF4D4F"}})],1):e._e()],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("家族成员")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入家族成员"},model:{value:t.familyMember,callback:function(i){e.$set(t,"familyMember",i)},expression:"item.familyMember"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("疾病名称")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入疾病名称"},model:{value:t.diseaseName,callback:function(i){e.$set(t,"diseaseName",i)},expression:"item.diseaseName"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断日期")]),i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{mode:"date",value:t.diagnosisDate},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.handleFamilyHistoryDateChange(t,a)}.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(t.diagnosisDate||"请选择诊断日期"))])],1)],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("机构名称")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入机构名称"},model:{value:t.institutionName,callback:function(i){e.$set(t,"institutionName",i)},expression:"item.institutionName"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("治疗经过")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入治疗经过"},model:{value:t.treatmentProcess,callback:function(i){e.$set(t,"treatmentProcess",i)},expression:"item.treatmentProcess"}})],1)],1)})),i("v-uni-button",{staticClass:"add-history-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.addFamilyHistory.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"plusempty",size:"16",color:"#2979ff"}}),i("v-uni-text",[e._v("添加家族史")])],1)],2)]:[e.getFamilyHistory().length>0?i("v-uni-view",{staticClass:"history-list"},e._l(e.getFamilyHistory(),(function(t,a){return i("v-uni-view",{key:a,staticClass:"history-item"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("家族成员")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.familyMember))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("疾病名称")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.diseaseName))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断日期")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.diagnosisDate))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("机构名称")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.institutionName))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("治疗经过")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(t.treatmentProcess))])],1)],1)})),1):i("v-uni-view",{staticClass:"empty-history"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无家族史记录")])],1)]],2)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title"},[e._v("健康相关信息")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label",staticStyle:{"margin-right":"1em"}},[e._v("血型")]),e.isEditing?i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.bloodTypeIndex,range:e.bloodTypeOptions},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBloodTypeChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.editForm.bloodType||"请选择"))])],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("bloodType")||"-"))])],1)],1),i("v-uni-view",{staticClass:"sub-section"},[i("v-uni-view",{staticClass:"sub-title"},[e._v("吸烟史")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("吸烟情况")]),e.isEditing?i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.smokingHistoryIndex,range:e.smokingOptions},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleSmokingHistoryChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.editForm.smokingHistory||"请选择"))])],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("smokingHistory")))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("吸烟数量")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入吸烟数量"},model:{value:e.editForm.smokingAmount,callback:function(t){e.$set(e.editForm,"smokingAmount",t)},expression:"editForm.smokingAmount"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("smokingAmount")))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("吸烟年限")]),e.isEditing?i("v-uni-view",{staticClass:"form-input-group"},[i("v-uni-input",{staticClass:"form-input form-input-half",attrs:{placeholder:"年",type:"number"},model:{value:e.editForm.smokingYear,callback:function(t){e.$set(e.editForm,"smokingYear",t)},expression:"editForm.smokingYear"}}),i("v-uni-input",{staticClass:"form-input form-input-half",attrs:{placeholder:"月",type:"number"},model:{value:e.editForm.smokingMonth,callback:function(t){e.$set(e.editForm,"smokingMonth",t)},expression:"editForm.smokingMonth"}})],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getSmokeYears()))])],1)],1)],1),i("v-uni-view",{staticClass:"sub-section"},[i("v-uni-view",{staticClass:"sub-title"},[e._v("饮酒史")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("饮酒情况")]),e.isEditing?i("v-uni-picker",{staticClass:"picker-wrapper",attrs:{value:e.drinkingHistoryIndex,range:e.drinkingOptions},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleDrinkingHistoryChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input"},[e._v(e._s(e.editForm.drinkingHistory||"请选择"))])],1):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("drinkingHistory")))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("饮酒量")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入饮酒量"},model:{value:e.editForm.drinkingAmount,callback:function(t){e.$set(e.editForm,"drinkingAmount",t)},expression:"editForm.drinkingAmount"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("drinkingAmount")))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("饮酒年限")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入饮酒年限"},model:{value:e.editForm.drinkingYear,callback:function(t){e.$set(e.editForm,"drinkingYear",t)},expression:"editForm.drinkingYear"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("drinkingYear")))])],1)],1)],1),"1"==e.form.gender?i("v-uni-view",{staticClass:"sub-section"},[i("v-uni-view",{staticClass:"sub-title"},[e._v("生育史")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("现有子女数")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入现有子女数",type:"number"},model:{value:e.editForm.currentChildren,callback:function(t){e.$set(e.editForm,"currentChildren",t)},expression:"editForm.currentChildren"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("currentChildren")||"0"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("流产数")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入流产数",type:"number"},model:{value:e.editForm.abortion,callback:function(t){e.$set(e.editForm,"abortion",t)},expression:"editForm.abortion"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("abortion")||"0"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("死产数")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入死产数",type:"number"},model:{value:e.editForm.stillbirth,callback:function(t){e.$set(e.editForm,"stillbirth",t)},expression:"editForm.stillbirth"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("stillbirth")||"0"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("早产数")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入早产数",type:"number"},model:{value:e.editForm.premature,callback:function(t){e.$set(e.editForm,"premature",t)},expression:"editForm.premature"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("premature")||"0"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("异常胎数")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入异常胎数",type:"number"},model:{value:e.editForm.abnormalFetus,callback:function(t){e.$set(e.editForm,"abnormalFetus",t)},expression:"editForm.abnormalFetus"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("abnormalFetus")||"0"))])],1),i("v-uni-view",{staticClass:"info-item full-width"},[i("v-uni-text",{staticClass:"label"},[e._v("子女健康状况")]),e.isEditing?i("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入子女健康状况"},model:{value:e.editForm.childrenHealth,callback:function(t){e.$set(e.editForm,"childrenHealth",t)},expression:"editForm.childrenHealth"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("childrenHealth")||"暂无记录"))])],1)],1)],1):e._e(),"1"==e.form.gender?i("v-uni-view",{staticClass:"sub-section"},[i("v-uni-view",{staticClass:"sub-title"},[e._v("月经史")]),i("v-uni-view",{staticClass:"info-group"},[i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("初潮年龄")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入初潮年龄",type:"number"},model:{value:e.editForm.menarcheAge,callback:function(t){e.$set(e.editForm,"menarcheAge",t)},expression:"editForm.menarcheAge"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("menarcheAge")||"暂无记录"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("经期（天）")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入经期天数",type:"number"},model:{value:e.editForm.menstruationDays,callback:function(t){e.$set(e.editForm,"menstruationDays",t)},expression:"editForm.menstruationDays"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("menstruationDays")||"暂无记录"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("周期（天）")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入周期天数",type:"number"},model:{value:e.editForm.menstruationCycle,callback:function(t){e.$set(e.editForm,"menstruationCycle",t)},expression:"editForm.menstruationCycle"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("menstruationCycle")||"暂无记录"))])],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("绝经年龄")]),e.isEditing?i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入绝经年龄",type:"number"},model:{value:e.editForm.menopauseAge,callback:function(t){e.$set(e.editForm,"menopauseAge",t)},expression:"editForm.menopauseAge"}}):i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.getBasicInfoValue("menopauseAge")||"暂无记录"))])],1)],1)],1):e._e(),i("v-uni-view",{staticClass:"sub-section"},[i("v-uni-view",{staticClass:"sub-title"},[e._v("运动习惯")]),i("v-uni-view",{staticClass:"info-item full-width"},[e.isEditing?i("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入您的运动习惯描述，如频率、时长、运动类型等"},model:{value:e.editForm.exerciseHabit,callback:function(t){e.$set(e.editForm,"exerciseHabit",t)},expression:"editForm.exerciseHabit"}}):i("v-uni-text",{staticClass:"value",staticStyle:{"text-align":"left"}},[e._v(e._s(e.getBasicInfoValue("exerciseHabit")||"暂无运动习惯记录"))])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-title-bar",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.emergencyContactCollapsed=!e.emergencyContactCollapsed}}},[i("v-uni-view",{staticClass:"section-title"},[e._v("紧急联系人")]),i("uni-icons",{attrs:{type:e.emergencyContactCollapsed?"arrowdown":"arrowup",size:"18",color:"#2979ff"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.emergencyContactCollapsed,expression:"!emergencyContactCollapsed"}]},[e.isEditing?[i("v-uni-view",{staticClass:"emergency-contact-form"},[e._l(e.editForm.emergencyContacts,(function(t,a){return i("v-uni-view",{key:a,staticClass:"contact-form-item"},[i("v-uni-view",{staticClass:"contact-form-header"},[i("v-uni-text",{staticClass:"contact-form-title"},[e._v("联系人 "+e._s(a+1))]),e.editForm.emergencyContacts.length>1?i("v-uni-button",{staticClass:"delete-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.removeEmergencyContact(a)}}},[i("uni-icons",{attrs:{type:"trash",size:"16",color:"#FF4D4F"}})],1):e._e()],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("姓名")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入联系人姓名"},model:{value:t.name,callback:function(i){e.$set(t,"name",i)},expression:"contact.name"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("关系")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入与联系人的关系"},model:{value:t.relationship,callback:function(i){e.$set(t,"relationship",i)},expression:"contact.relationship"}})],1),i("v-uni-view",{staticClass:"info-item"},[i("v-uni-text",{staticClass:"label"},[e._v("电话")]),i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入联系人电话",type:"number"},model:{value:t.phoneNum,callback:function(i){e.$set(t,"phoneNum",i)},expression:"contact.phoneNum"}})],1)],1)})),i("v-uni-button",{staticClass:"add-contact-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addEmergencyContact.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"plusempty",size:"16",color:"#2979ff"}}),i("v-uni-text",[e._v("添加联系人")])],1)],2)]:[e.getEmergencyContacts().length>0?i("v-uni-view",[i("v-uni-view",{staticClass:"contact-list"},e._l(e.getEmergencyContacts(),(function(t,a){return i("v-uni-view",{key:a,staticClass:"contact-item"},[i("v-uni-view",{staticClass:"contact-header"},[i("v-uni-text",{staticClass:"contact-name"},[e._v(e._s(t.name))]),i("v-uni-text",{staticClass:"contact-relation"},[e._v(e._s(t.relationship))])],1),i("v-uni-text",{staticClass:"contact-phone"},[e._v(e._s(t.phoneNum))])],1)})),1)],1):i("v-uni-view",{staticClass:"empty-history"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无紧急联系人信息")])],1)]],2)],1),i("v-uni-view",{staticClass:"empty"}),i("v-uni-view",{staticClass:"footer"},[e.isEditing?[i("v-uni-button",{staticClass:"btn cancel-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCancel.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"close",size:"16",color:"#FFFFFF"}}),i("v-uni-text",[e._v("取消")])],1),i("v-uni-button",{staticClass:"btn save-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleSave.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"checkmarkempty",size:"16",color:"#FFFFFF"}}),i("v-uni-text",[e._v("保存")])],1)]:[i("v-uni-button",{staticClass:"btn edit-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleEdit.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"compose",size:"16",color:"#FFFFFF"}}),i("v-uni-text",[e._v("编辑")])],1),i("v-uni-button",{staticClass:"btn appeal-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showAppealPopup.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"help",size:"16",color:"#FFFFFF"}}),i("v-uni-text",[e._v("申诉")])],1)]],2),i("uni-popup",{ref:"appealPopup",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"appeal-popup"},[i("v-uni-view",{staticClass:"popup-title"},[e._v("信息申诉")]),i("v-uni-view",{staticClass:"popup-content"},[i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("申诉单位类型")]),i("v-uni-radio-group",{staticClass:"radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleTypeChange.apply(void 0,arguments)}}},e._l(e.orgTypes,(function(t,a){return i("v-uni-label",{key:a,staticClass:"radio"},[i("v-uni-radio",{attrs:{value:t.value,checked:t.checked,color:"#2979ff"}}),i("v-uni-text",[e._v(e._s(t.label))])],1)})),1)],1),"jg"===e.appealForm.orgType?i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("申诉单位名称")]),i("v-uni-view",{staticClass:"search-wrapper"},[i("v-uni-input",{staticClass:"form-input",attrs:{placeholder:"请输入申诉单位名称"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUnitSearch.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInputFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInputBlur.apply(void 0,arguments)}},model:{value:e.appealForm.orgName,callback:function(t){e.$set(e.appealForm,"orgName",t)},expression:"appealForm.orgName"}}),e.showUnitDropdown&&e.isInputFocused?i("v-uni-view",{staticClass:"search-dropdown"},[e.isSearching?i("v-uni-view",{staticClass:"dropdown-loading"},[i("uni-icons",{attrs:{type:"spinner-cycle",size:"16",color:"#999999"}}),i("v-uni-text",[e._v("搜索中...")])],1):0===e.unitSearchResults.length?i("v-uni-view",{staticClass:"dropdown-empty"},[i("v-uni-text",[e._v("暂无相关单位")])],1):i("v-uni-view",{staticClass:"dropdown-list"},e._l(e.unitSearchResults,(function(t,a){return i("v-uni-view",{key:a,staticClass:"dropdown-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectUnit(t)}}},[e._v(e._s(t.cname))])})),1)],1):e._e()],1)],1):e._e(),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-text",{staticClass:"form-label"},[e._v("申诉内容")]),i("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入申诉内容"},model:{value:e.appealForm.content,callback:function(t){e.$set(e.appealForm,"content",t)},expression:"appealForm.content"}})],1)],1),i("v-uni-view",{staticClass:"popup-footer"},[i("v-uni-button",{staticClass:"popup-btn cancel",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeAppealPopup.apply(void 0,arguments)}}},[e._v("取消")]),i("v-uni-button",{staticClass:"popup-btn submit",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitAppeal.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)],1)],1)],1)},s=[]},"434f":function(e,t,i){"use strict";i.r(t);var a=i("8591"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(s);t["default"]=n.a},"634b":function(e,t,i){"use strict";i.r(t);var a=i("17561"),n=i("434f");for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);i("74b8");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"5c0264f4",null,!1,a["a"],void 0);t["default"]=r.exports},"6e78":function(e,t,i){"use strict";i.r(t);var a=i("2289"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(s);t["default"]=n.a},"72ae":function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'uni-page-body[data-v-d104cece]{height:100%}.container[data-v-d104cece]{display:flex;flex-direction:column;height:100%;background-color:#f5f5f5}.content[data-v-d104cece]{flex:1;overflow:auto;padding:%?20?%}.section[data-v-d104cece]{background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-bottom:%?20?%;margin-top:%?20?%}.section-title-container[data-v-d104cece]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?30?%;position:relative}.section-title[data-v-d104cece]{font-size:16px;font-weight:700;color:#333;position:relative;padding-left:%?20?%}.section-title[data-v-d104cece]::before{content:"";position:absolute;left:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?6?%;height:%?24?%;background-color:#2979ff;border-radius:%?3?%}.add-career-btn[data-v-d104cece]{display:flex;align-items:center;justify-content:center;background-color:#f0f7ff;border:1px solid #d6e9ff;border-radius:%?30?%;padding:%?6?% %?20?%;font-size:12px;color:#2979ff;line-height:1.5;position:absolute;right:0}.add-career-btn uni-text[data-v-d104cece]{margin-left:%?4?%}.info-group[data-v-d104cece]{display:flex;flex-wrap:wrap}.info-item[data-v-d104cece]{width:100%;margin-bottom:%?20?%;padding-right:%?20?%;box-sizing:border-box;display:flex;align-items:center}.info-item.full-width[data-v-d104cece]{width:100%}.label[data-v-d104cece]{font-size:14px;color:#666;margin-right:%?20?%;min-width:%?160?%;flex-shrink:0}.value[data-v-d104cece]{font-size:14px;color:#333;flex:1;text-align:right}.sub-section[data-v-d104cece]{margin-top:%?30?%}.sub-title[data-v-d104cece]{font-size:14px;color:#666;margin-bottom:%?20?%}.career-item[data-v-d104cece]{border:1px solid #eaeaea;border-radius:%?8?%;padding:%?20?%;margin-bottom:%?20?%;background-color:#f9fafb;position:relative}.career-actions[data-v-d104cece]{display:flex;justify-content:space-between;margin-top:%?16?%;padding-top:%?16?%;border-top:1px dashed #eaeaea}.action-btn[data-v-d104cece]{display:flex;align-items:center;justify-content:center;background-color:#f5f5f5;border:1px solid #eaeaea;border-radius:%?8?%;font-size:14px;padding:%?12?% 0;width:48%;line-height:1.5}.action-btn.edit[data-v-d104cece]{color:#2979ff;background-color:#f0f7ff;border-color:#d6e9ff}.action-btn.delete[data-v-d104cece]{color:#ff4d4f;background-color:#fff0f0;border-color:#ffd6d6}.action-btn uni-text[data-v-d104cece]{margin-left:%?8?%}.empty-career[data-v-d104cece]{padding:%?40?% 0;text-align:center}.empty-text[data-v-d104cece]{font-size:%?28?%;color:#909399}.career-popup[data-v-d104cece]{width:%?600?%;max-height:80vh;background-color:#fff;border-radius:%?12?%;overflow:auto}.career-header[data-v-d104cece]{display:flex;justify-content:space-between;margin-bottom:%?20?%}.company[data-v-d104cece]{font-size:14px;color:#333;font-weight:700}.time[data-v-d104cece]{font-size:12px;color:#999}.medical-item[data-v-d104cece]{border:1px solid #eaeaea;border-radius:%?8?%;padding:%?20?%;margin-bottom:%?20?%}.empty[data-v-d104cece]{height:%?130?%}.footer[data-v-d104cece]{display:flex;position:fixed;bottom:0;width:100%;left:0;padding:%?20?%;box-sizing:border-box;background-color:#fff;box-shadow:0 %?-2?% %?10?% rgba(0,0,0,.05)}@media screen and (min-width:960px){.footer[data-v-d104cece]{width:23.5rem;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}}.btn[data-v-d104cece]{flex:1;height:%?80?%;display:flex;align-items:center;justify-content:center;border-radius:%?40?%;margin:0 %?10?%}.btn uni-text[data-v-d104cece]{margin-left:%?10?%}.edit-btn[data-v-d104cece]{background-color:#2979ff;color:#fff}.appeal-btn[data-v-d104cece]{background-color:#ff9500;color:#fff}.appeal-popup[data-v-d104cece]{width:%?600?%;background-color:#fff;border-radius:%?12?%;overflow:hidden}.popup-title[data-v-d104cece]{font-size:16px;font-weight:700;text-align:center;padding:%?30?%;border-bottom:1px solid #eaeaea}.popup-content[data-v-d104cece]{padding:%?30?%}.form-item[data-v-d104cece]{margin-bottom:%?30?%}.form-label[data-v-d104cece]{display:block;font-size:14px;color:#333;margin-bottom:%?20?%}.radio-group[data-v-d104cece]{display:flex}.radio[data-v-d104cece]{margin-right:%?40?%;font-size:14px}.picker-wrapper[data-v-d104cece]{flex:1}.form-input[data-v-d104cece]{flex:1;height:%?70?%;border:1px solid #eaeaea;border-radius:%?8?%;padding:0 %?20?%;box-sizing:border-box;font-size:14px;display:flex;align-items:center;background-color:#fff}\n\n/* 添加焦点效果 */uni-input.form-input[data-v-d104cece]{background-color:#fff}uni-input.form-input[data-v-d104cece]:focus{border-color:#2979ff}.form-textarea[data-v-d104cece]{width:100%;height:%?150?%;border:1px solid #eaeaea;border-radius:%?8?%;padding:%?20?%;box-sizing:border-box;font-size:14px;background-color:#fff}.popup-footer[data-v-d104cece]{display:flex;border-top:1px solid #eaeaea}.popup-btn[data-v-d104cece]{flex:1;height:%?88?%;display:flex;align-items:center;justify-content:center;font-size:14px}.popup-btn.cancel[data-v-d104cece]{background-color:#f5f5f5;color:#666}.popup-btn.submit[data-v-d104cece]{background-color:#2979ff;color:#fff}\n\n/* 联系人 */.contact-item[data-v-d104cece]{background-color:#f9fafb;padding:%?24?%;border-radius:%?12?%;margin-bottom:%?24?%}.contact-header[data-v-d104cece]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?16?%}.contact-name[data-v-d104cece]{font-size:%?28?%;font-weight:500}.contact-relation[data-v-d104cece]{font-size:%?24?%;color:#6b7280}.contact-phone[data-v-d104cece]{color:#3176ff;font-size:%?28?%}.cancel-btn[data-v-d104cece]{background-color:#999;color:#fff}.save-btn[data-v-d104cece]{background-color:#2979ff;color:#fff}.form-input-group[data-v-d104cece]{flex:1;display:flex;gap:%?10?%}.form-input-half[data-v-d104cece]{flex:1}.empty-contact[data-v-d104cece]{padding:%?40?% 0;text-align:center}.empty-text[data-v-d104cece]{color:#999;font-size:14px}.emergency-contact-form[data-v-d104cece]{background-color:#f9fafb;border-radius:%?12?%;padding:%?24?%}.contact-form-item[data-v-d104cece]{background-color:#f9fafb;border-radius:%?12?%;padding:%?24?%;margin-bottom:%?24?%}.contact-form-header[data-v-d104cece]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?20?%}.contact-form-title[data-v-d104cece]{font-size:14px;color:#333;font-weight:500}.delete-btn[data-v-d104cece]{background:none;border:none;padding:0;margin:0;line-height:1}.add-contact-btn[data-v-d104cece]{display:flex;align-items:center;justify-content:center;width:100%;height:%?80?%;background-color:#f5f5f5;border:1px dashed #d9d9d9;border-radius:%?8?%;margin-top:%?20?%}.add-contact-btn uni-text[data-v-d104cece]{margin-left:%?10?%;color:#2979ff;font-size:14px}.search-wrapper[data-v-d104cece]{position:relative}.search-dropdown[data-v-d104cece]{position:absolute;top:100%;left:0;width:100%;background-color:#fff;border:1px solid #eaeaea;border-radius:%?8?%;max-height:%?240?%;overflow:auto;z-index:1000;margin-top:%?4?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.15)}.dropdown-item[data-v-d104cece]{padding:%?16?% %?20?%;font-size:14px;color:#333;cursor:pointer}.dropdown-item[data-v-d104cece]:hover{background-color:#f5f5f5}.dropdown-loading[data-v-d104cece],\n.dropdown-empty[data-v-d104cece]{padding:%?20?%;text-align:center;color:#999;font-size:14px;display:flex;align-items:center;justify-content:center}.dropdown-loading uni-icons[data-v-d104cece]{margin-right:%?8?%}.history-list[data-v-d104cece]{margin-bottom:%?20?%}.history-item[data-v-d104cece]{background-color:#f9fafb;border-radius:%?12?%;padding:%?24?%;margin-bottom:%?24?%}.history-header[data-v-d104cece]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?16?%}.history-title[data-v-d104cece]{font-size:%?28?%;font-weight:500;color:#333}.history-date[data-v-d104cece]{font-size:%?24?%;color:#6b7280}.history-content[data-v-d104cece]{margin-bottom:%?16?%}.history-actions[data-v-d104cece]{display:flex;justify-content:space-between;margin-top:%?16?%;padding-top:%?16?%;border-top:1px dashed #eaeaea}.history-form[data-v-d104cece]{background-color:#f9fafb;border-radius:%?12?%;padding:%?24?%}.history-form-item[data-v-d104cece]{background-color:#fff;border-radius:%?12?%;padding:%?24?%;margin-bottom:%?24?%}.history-form-header[data-v-d104cece]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?20?%}.history-form-title[data-v-d104cece]{font-size:14px;color:#333;font-weight:500}.add-history-btn[data-v-d104cece]{display:flex;align-items:center;justify-content:center;width:100%;height:%?80?%;background-color:#f5f5f5;border:1px dashed #d9d9d9;border-radius:%?8?%;margin-top:%?20?%}.add-history-btn uni-text[data-v-d104cece]{margin-left:%?10?%;color:#2979ff;font-size:14px}.empty-history[data-v-d104cece]{padding:%?40?% 0;text-align:center}.empty-text[data-v-d104cece]{color:#999;font-size:14px}.section-title-bar[data-v-d104cece]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?10?%;cursor:pointer}.section-title-bar uni-icons[data-v-d104cece]{margin-left:%?10?%}.career-add-fab[data-v-d104cece]{position:relative;margin:%?24?% auto 0 auto;width:%?56?%;height:%?56?%;background:#2ecc71;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 %?2?% %?8?% rgba(46,204,113,.15);cursor:pointer}.career-add-fab uni-icons[data-v-d104cece]{display:block}.tags-wrapper[data-v-d104cece]{display:flex;flex-wrap:wrap;margin-bottom:%?8?%}.tag[data-v-d104cece]{background:#e6f7ff;color:#1890ff;border-radius:%?4?%;padding:%?4?% %?12?%;margin-right:%?8?%;margin-bottom:%?8?%;display:flex;align-items:center;font-size:12px}.tag uni-icons[data-v-d104cece]{margin-left:%?4?%;cursor:pointer}.harm-factor-input-wrapper[data-v-d104cece]{position:relative;width:100%}.search-dropdown[data-v-d104cece]{position:absolute;top:100%;left:0;width:100%;background-color:#fff;border:1px solid #eaeaea;border-radius:%?8?%;max-height:%?240?%;overflow:auto;z-index:1000;margin-top:%?4?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.15)}.dropdown-loading[data-v-d104cece],\n.dropdown-empty[data-v-d104cece]{padding:%?20?%;text-align:center;color:#999;font-size:14px;display:flex;align-items:center;justify-content:center}.dropdown-loading uni-icons[data-v-d104cece]{margin-right:%?8?%}.dropdown-item[data-v-d104cece]{padding:%?16?% %?20?%;font-size:14px;color:#333;cursor:pointer}.dropdown-item[data-v-d104cece]:hover{background-color:#f5f5f5}',""]),e.exports=t},"74b8":function(e,t,i){"use strict";var a=i("becb"),n=i.n(a);n.a},"77aa7":function(e,t,i){"use strict";i.r(t);var a=i("2cbd"),n=i("6e78");for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);i("f47b");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"d104cece",null,!1,a["a"],void 0);t["default"]=r.exports},8591:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var n=a(i("a467")),s={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:n.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=s},a467:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},becb:function(e,t,i){var a=i("eba0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("72b88c0b",a,!0,{sourceMap:!1,shadowMode:!1})},e206:function(e,t,i){e.exports=i.p+"assets/uni.75745d34.ttf"},e37f:function(e,t,i){var a=i("72ae");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("230997a2",a,!0,{sourceMap:!1,shadowMode:!1})},eba0:function(e,t,i){var a=i("c86c"),n=i("2ec5"),s=i("e206");t=a(!1);var o=n(s);t.push([e.i,"@font-face{font-family:uniicons;src:url("+o+') format("truetype")}.uni-icons[data-v-5c0264f4]{font-family:uniicons;text-decoration:none;text-align:center}',""]),e.exports=t},f47b:function(e,t,i){"use strict";var a=i("e37f"),n=i.n(a);n.a}}]);