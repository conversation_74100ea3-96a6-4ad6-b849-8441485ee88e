<template>
	<view class="add">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					工伤认定
				</view>
			</block>
		</uni-nav-bar>
		<view class="add_body">
			<view class="add_content">
				<u-tabs :list="tabList" @change="handleTabChange"></u-tabs>
				<!-- 基础表单 -->
				<view class="basicFrom" v-if="curTab ==='基本信息'">
					<u--form labelPosition="left" labelWidth="auto" :model="formData" :rules="basicRules" ref="basicForm">
						<u-form-item label="姓名" prop="employee_name" borderBottom>
							<u--input v-model="formData.employee_name" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="性别" prop="gender" borderBottom>
							<u-radio-group v-model="formData.gender" placement="row">
								<u-radio label="男" name="男"></u-radio>
								<u-radio label="女" name="女"></u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="出生日期" prop="birthday" borderBottom>
							<uni-datetime-picker type="date" v-model="formData.birthday" :border="false" />
						</u-form-item>
						<u-form-item label="身份证号码" prop="id_code" borderBottom>
							<u--input v-model="formData.id_code" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="联系电话" prop="employee_phone" borderBottom>
							<u--input v-model="formData.employee_phone" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="家庭地址" prop="employee_address" borderBottom>
							<u--input v-model="formData.employee_address" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="邮政编码" prop="employee_postcode" borderBottom>
							<u--input v-model="formData.employee_postcode" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="所在单位名称" prop="enterprise_name" borderBottom>
							<u--input v-model="formData.enterprise_name" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="单位联系电话" prop="enterprise_phone" borderBottom>
							<u--input v-model="formData.enterprise_phone" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="单位地址" prop="enterprise_address" borderBottom>
							<u--input v-model="formData.enterprise_address" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="单位邮政编码" prop="enterprise_postcode" borderBottom>
							<u--input v-model="formData.enterprise_postcode" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="职业、工种或工作岗位" prop="enterprise_postcode" borderBottom>
							<u--input v-model="formData.employee_job" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="参加工作时间" prop="employee_work_date" borderBottom>
							<uni-datetime-picker type="date" v-model="formData.employee_work_date" placeholder="请选择"
								:border="false" />
						</u-form-item>
						<u-form-item label="事故时间" prop="accident_time" borderBottom>
							<uni-datetime-picker type="date" v-model="formData.accident_time" placeholder="请选择" :border="false" />
						</u-form-item>
						<u-form-item label="事故地点" prop="accident_place" borderBottom>
							<u--input v-model="formData.accident_place" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="事故主要原因" prop="main_reason" borderBottom>
							<u--input v-model="formData.main_reason" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="诊断时间" prop="diagnosis_time" borderBottom>
							<uni-datetime-picker type="date" v-model="formData.diagnosis_time" placeholder="请选择" :border="false" />
						</u-form-item>
						<u-form-item label="受伤害部位" prop="injury_part" borderBottom>
							<u--input v-model="formData.injury_part" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="职业病名称" prop="disease_name" borderBottom>
							<u--input v-model="formData.disease_name" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="接触职业病危害岗位" prop="exposure_post" borderBottom>
							<u--input v-model="formData.exposure_post" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="接触职业病危害时间" prop="exposure_time" borderBottom>
							<u--input v-model="formData.exposure_time" placeholder="请输入" border="none"></u--input>
						</u-form-item>
						<u-form-item label="受伤害经过简述" prop="description" borderBottom>
							<u--textarea v-model="formData.description" placeholder="请输入内容" border="bottom"></u--textarea>
						</u-form-item>
						<u-form-item label="申请事项" prop="apply_matter" borderBottom>
							<u--input v-model="formData.apply_matter" placeholder="请输入" border="none"></u--input>
						</u-form-item>
					</u--form>
					<view class="operator">
						<u-button size="small" @click="handleCancel">取消</u-button>
						<u-button type="primary" size="small" @click="submitBasicForm">暂存</u-button>
						<u-button type="success" size="small" v-show="isSubmit" @click="submitBasicForm('提交')">保存并上报</u-button>
					</view>
				</view>
				<!-- 工伤附件 -->
				<view class="annexFile" v-else-if="curTab === '工伤附件'">
					<u--form labelPosition="top" labelWidth="auto" :model="formData" :rules="annxeFileRules" ref="annexFileForm">
						<u-row customStyle="margin-bottom: 10px">
							<u-col span="7">
								<u-form-item label="工伤申请认定表" prop="annex_application" borderBottom>
									<uni-file-picker v-model="annexFile.annex_application" ref="annex_application" fileMediatype="all"
										:auto-upload="false" @select="handleUAASelect"
										@delete="(e) =>{handleDeleteFile(e, 'annex_application')}" />
								</u-form-item>
							</u-col>
							<u-col span="5">
								<u--text type="primary" text="下载工伤认定申请模版"
									@click="handleDownloadWorkInjuryFile(formData._id, 'annex_application')"></u--text>
							</u-col>
						</u-row>
						<u-row customStyle="margin-bottom: 10px">
							<u-col span="7">
								<u-form-item label="劳动关系证明" prop="annex_labor_contract" borderBottom>
									<uni-file-picker v-model="annexFile.annex_labor_contract" ref="annex_labor_contract"
										fileMediatype="all" :auto-upload="false" @select="handleUALCSelect"
										@delete="(e) =>{handleDeleteFile(e, 'annex_labor_contract')}" />
								</u-form-item>
							</u-col>
							<u-col span="5">
								<u--text type="primary" text="下载劳动关系证明模版"
									@click="handleDownloadWorkInjuryFile(formData._id, 'annex_labor_contract')"></u--text>
							</u-col>
						</u-row>
						<u-row customStyle="margin-bottom: 10px">
							<u-col span="7">
								<u-form-item label="诊断证明书" prop="annex_certificate" borderBottom>
									<uni-file-picker v-model="annexFile.annex_certificate" ref="annex_certificate" fileMediatype="all"
										:auto-upload="false" @select="handleUACSelect"
										@delete="(e) =>{handleDeleteFile(e, 'annex_certificate')}" />
								</u-form-item>
							</u-col>
							<u-col span="5"></u-col>
						</u-row>
						<u-row customStyle="margin-bottom: 10px">
							<u-col span="7">
								<u-form-item label="身份证复件" prop="annex_id" borderBottom>
									<uni-file-picker v-model="annexFile.annex_id" ref="annex_id" fileMediatype="all" :auto-upload="false"
										@select="handleUAISelect" @delete="(e) =>{handleDeleteFile(e, 'annex_id')}" />
								</u-form-item>
							</u-col>
							<u-col span="5"></u-col>
						</u-row>
						<u-row customStyle="margin-bottom: 10px">
							<u-col span="7">
								<u-form-item label="一寸照片" prop="annex_photo" borderBottom>
									<uni-file-picker v-model="annexFile.annex_photo" ref="annex_photo" fileMediatype="all"
										:auto-upload="false" @select="handleUAPSelect"
										@delete="(e) =>{handleDeleteFile(e, 'annex_photo')}" />
								</u-form-item>
							</u-col>
							<u-col span="5"></u-col>
						</u-row>
						<u-row customStyle="margin-bottom: 10px">
							<u-col span="7">
								<u-form-item label="调查报告" prop="annex_investigation" borderBottom>
									<uni-file-picker v-model="annexFile.annex_investigation" ref="annex_investigation" fileMediatype="all"
										:auto-upload="false" @select="handleUAInSelect"
										@delete="(e) =>{handleDeleteFile(e, 'annex_investigation')}" />
								</u-form-item>
							</u-col>
							<u-col span="5">
								<u--text type="primary" text="下载调查报告模版"
									@click="handleDownloadWorkInjuryFile(formData._id, 'annex_investigation')"></u--text>
							</u-col>
						</u-row>
					</u--form>
					<view class="operator">
						<u-button size="small" @click="handleCancel">取消</u-button>
						<u-button type="primary" size="small" @click="submitAnnexForm">暂存</u-button>
						<u-button type="success" size="small" @click="submitAnnexForm('提交')">保存并上报</u-button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import config from '@/common.js';

	import {
		addOrEditWorkInjuryRecognition,
		getWorkInjuryRecognitionDetail,
		downloadTemplateFile,
		uploadFile,
		deleteFile
	} from '../../api/workInjuryRecognition.js'

	import {
		downloadFile
	} from "./utils.js"

	export default {
		name: 'WIRAdd',
		data() {
			return {
				tabList: [{
						name: '基本信息'
					},
					{
						name: '工伤附件'
					},
				],
				curTab: '基本信息',
				// 表单
				formData: {
					employee_name: "",
					gender: "",
					birthday: "",
					id_code: "",
					employee_phone: "",
					employee_address: "",
					employee_postcode: "",
					enterprise_name: "",
					enterprise_phone: "",
					enterprise_address: "",
					enterprise_postcode: "",
					employee_job: "",
					employee_work_date: "",
					accident_time: "",
					accident_place: "",
					main_reason: "",
					diagnosis_time: "",
					injury_part: "",
					disease_name: "",
					exposure_post: "",
					exposure_time: "",
					description: "",
					apply_matter: "",
					annex_application: [],
					annex_labor_contract: [],
					annex_certificate: [],
					annex_id: [],
					annex_photo: [],
					annex_investigation: [],
				},
				basicRules: {
					apply_name: [{
							required: true,
							message: '请输入申请人姓名',
							trigger: 'blur'
						},
						{
							min: 2,
							max: 30,
							message: '长度在 2 到 30 个字符',
							trigger: 'blur'
						}
					],
					gender: [{
						required: true,
						message: '请选择性别',
						trigger: 'blur'
					}, ],
					birthday: [{
						required: true,
						message: '请选填写出生日期',
						trigger: 'change'
					}, ],
					id_code: [{
						required: true,
						message: '请填写身份证号',
						trigger: 'blur'
					}, ],
					employee_phone: [{
						required: true,
						message: '请填写联系电话',
						trigger: 'blur'
					}],
					employee_address: [{
						required: true,
						message: '请填写家庭地址',
						trigger: 'blur'
					}],
					enterprise_name: [{
						required: true,
						message: '请填写工作单位',
						trigger: 'blur'
					}],
					enterprise_phone: [{
						required: true,
						message: '请填写工作单位联系电话',
						trigger: 'blur'
					}],
					enterprise_address: [{
						required: true,
						message: '请填写工作单位地址',
						trigger: 'blur'
					}],
					employee_job: [{
						required: true,
						message: '请填写职业、工种或工作岗位',
						trigger: 'blur'
					}],
					employee_work_date: [{
						required: true,
						message: '请填写参加工作时间',
						trigger: 'change'
					}],
					main_reason: [{
						required: true,
						message: '请填写事故主要原因',
						trigger: 'blur'
					}],
					diagnosis_time: [{
						required: true,
						message: '请填写诊断时间',
						trigger: 'change'
					}],
					injury_part: [{
						required: true,
						message: '请填写受伤部位',
						trigger: 'blur'
					}],
					disease_name: [{
						required: true,
						message: '请填写职业病名称',
						trigger: 'blur'
					}],
					exposure_post: [{
						required: true,
						message: '请填写接触职业病危害岗位',
						trigger: 'blur'
					}],
					exposure_time: [{
						required: true,
						message: '请填写接触职业病危害时间',
						trigger: 'change'
					}],
					description: [{
						required: true,
						message: '请填写受伤害经过简述',
						trigger: 'blur'
					}],
					apply_matter: [{
						required: true,
						message: '请填写申请事项',
						trigger: 'blur'
					}],
				},
				annxeFileRules: {
					annex_application: [{
						type: 'array',
						required: true,
						message: '请上传工伤认定申请书',
						trigger: 'change'
					}, ],
					annex_labor_contract: [{
						type: 'array',
						required: true,
						message: '请上传劳动关系证明',
						trigger: 'change'
					}, ],
					annex_certificate: [{
						type: 'array',
						required: true,
						message: '请上传诊断证明书',
						trigger: 'change'
					}, ],
					annex_id: [{
						type: 'array',
						required: true,
						message: '请上传身份证复件',
						trigger: 'change'
					}, ],
					annex_photo: [{
						type: 'array',
						required: true,
						message: '请上传一寸照片',
						trigger: 'change'
					}, ],
				},
				annexFile: {
					annex_application: [],
					annex_labor_contract: [],
					annex_certificate: [],
					annex_id: [],
					annex_photo: [],
					annex_investigation: [],
				},
			}
		},
		computed: {
			isSubmit() {
				return this.formData.annex_application && this.formData.annex_application.length > 0
			}
		},
		async onLoad(options) {
			const {
				_id
			} = options
			if (_id) { // 编辑
				const res = await getWorkInjuryRecognitionDetail({
					_id
				})
				this.formData = res.data
				// 附件数据回显
				Object.keys(this.annexFile)
					.forEach(key => {
						const target = JSON.parse(JSON.stringify(this.formData[key]));
						if (!target && target.length === 0) return;
						this.annexFile[key] = target.map(item => {
							const name = item?.split('/')
								.pop();
							return {
								name,
								url: item,
							};
						});
					});
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			// tab 切换
			handleTabChange({
				name
			}) {
				this.curTab = name

			},
			// 取消
			handleCancel() {
				uni.navigateTo({
					url: `/pages/workInjuryRecognition/index`,
				})
			},
			// 提交基本信息
			submitBasicForm(type = "暂存") {
				switch (type) {
					case '暂存':
						this.formData.status = '0';
						break;
					case '提交':
						this.formData.status = '1';
						console.log(2)
						break;
				}
				// 校验表单
				this.$refs.basicForm.validate().then(async (res) => {
					try {
						await addOrEditWorkInjuryRecognition({...this.formData, apply_name: this.formData.employee_name, apply_relation: '本人'})
						uni.$u.toast('申请成功')
						uni.navigateTo({
							url: `/pages/workInjuryRecognition/index`
						})
					} catch (error) {
						//TODO handle the exception
						uni.$u.toast(error.message)
					}
				}).catch(errors => {
					uni.$u.toast('请将表单填写完整')
				})
			},
			// 提交附件
			submitAnnexForm(type = "暂存") {
				switch (type) {
					case '暂存':
						this.formData.status = '0';
						break;
					case '提交':
						this.formData.status = '1';
						console.log(2)
						break;
				}
				// 校验表单
				this.$refs.annexFileForm.validate().then(async (res) => {
					try {
						await addOrEditWorkInjuryRecognition({...this.formData, apply_name: this.formData.employee_name, apply_relation: '本人'})
						uni.$u.toast('申请成功')
						uni.navigateTo({
							url: `/pages/workInjuryRecognition/index`
						})
					} catch (error) {
						//TODO handle the exception
						uni.$u.toast(error.message)
					}
				}).catch(errors => {
					uni.$u.toast('请将表单填写完整')
				})
			},
			// 校验文件格式
			isFileValid(file) {
				// 最大允许 5MB
				const MAX_SIZE_MB = 5;
				const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;

				// 文件大小校验
				if (file.size > MAX_SIZE_BYTES) {
					return false;
				}

				// 允许的扩展名列表（小写）
				const allowedExtensions = [
					'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', // 图片
					'pdf', // PDF
					'doc', 'docx' // Word
				];

				// 允许的 MIME 类型列表（小写）
				const allowedMimeTypes = [
					'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', // 图片
					'application/pdf', // PDF
					'application/msword', // DOC
					'application/vnd.openxmlformats-officedocument.wordprocessingml.document' // DOCX
				];

				// 提取文件扩展名和 MIME 类型
				const extension = file.name.split('.')
					.pop()
					.toLowerCase();
				const mimeType = file.type.toLowerCase();

				// 检查扩展名或 MIME 类型是否合法
				const isExtensionValid = allowedExtensions.includes(extension);
				const isMimeTypeValid = allowedMimeTypes.includes(mimeType);

				// 满足任一条件即可（扩展名或 MIME 类型合法）
				return isExtensionValid || isMimeTypeValid;
			},
			// 下载工伤申请模版
			async handleDownloadWorkInjuryFile(_id, fileType) {
				const template = {
					annex_application: '工伤申请认定表',
					annex_labor_contract: '劳动关系证明',
					annex_investigation: '调查报告',
				}
				try {
					const {
						data
					} = await downloadTemplateFile({
						_id,
						file_category: fileType
					});
					console.log(template[fileType])
					downloadFile(template[fileType], config.apiServer + data.url.replace(/^\//, ''));
				} catch (e) {
					uni.showToast({
						icon: 'error',
						title: `${template[fileType]}下载失败`,
						duration: 3000
					})
				}
			},
			// 上传文件
			async handleUploadFile(file, fileType) { // fileType 为 annexFile 中的属性名
				uni.uploadFile({
					url: config.apiServer + 'app/file', //仅为示例，非真实的接口地址
					filePath: file.url,
					name: 'file',
					// formData: {
					// 	'file': ''
					// },
					success: (res) => {
						const data = JSON.parse(res.data)
						this.formData[fileType].push(data.data.url)
						file.url = data.data.url
					},
					fail: (err) => {
						uni.$u.toast(`${file.name} 上传失败`)
						this.annexFile[fileType] = this.annexFile[fileType].filter(item => {
							return item.uuid !== file.uuid
						})
						const index = this.annexFile[fileType]?.findIndex((item) => {
							return item.uuid === file.uuid
						})
						if (index !== -1) {
							this.$refs[fileType].clearFiles(index);
						}
					}
				})
			},
			// 删除文件
			async handleDeleteFile({
				tempFile
			}, fileType) {
				try {
					await deleteFile({
						filePath: tempFile.url.replace(/\\/g, '/')
					});

					// 删除表单中的数据
					this.formData[fileType] = this.formData[fileType].filter(url => {
						return url !== tempFile.url
					})
				} catch (e) {
					uni.$u.toast(`${tempFile.name} 删除失败`)
				}
			},
			handleUAASelect(e) {
				let {
					tempFiles
				} = e;

				const invalidIndices = []; // 格式校验失败文件索引

				tempFiles.forEach((element, index) => {
					let {
						name,
						url,
						uuid,
						file
					} = element;
					// 校验格式
					const validRes = this.isFileValid(file);
					console.log(validRes)
					if (validRes) {
						this.annexFile.annex_application.push({
							name,
							url,
							uuid,
							file,
						})
						// this.$refs.annex_application.clearFiles();
					} else {
						uni.$u.toast(`${file.name} 文件格式不支持`)
						invalidIndices.push(index)
					}
				});
				console.log("annex_application", this.annexFile.annex_application)
				// 清除格式校验不通过文件
				invalidIndices.reverse().forEach(index => {
					this.$refs.annex_application.clearFiles(index);
				});
				// 上传文件
				this.annexFile.annex_application.forEach(file => {
					this.handleUploadFile(file, 'annex_application')
				})
			},
			handleUALCSelect(e) {
				let {
					tempFiles
				} = e;

				const invalidIndices = []; // 格式校验失败文件索引

				tempFiles.forEach((element, index) => {
					let {
						name,
						url,
						uuid,
						file
					} = element;
					// 校验格式
					const validRes = this.isFileValid(file);
					console.log(validRes)
					if (validRes) {
						this.annexFile.annex_labor_contract.push({
							name,
							url,
							uuid,
							file,
						})
					} else {
						uni.$u.toast(`${file.name} 文件格式不支持`)
						invalidIndices.push(index)
					}
				});
				// 清除格式校验不通过文件
				invalidIndices.reverse().forEach(index => {
					this.$refs.annex_labor_contract.clearFiles(index);
				});
				// 上传文件
				this.annexFile.annex_labor_contract.forEach(file => {
					this.handleUploadFile(file, 'annex_labor_contract')
				})
			},
			handleUACSelect(e) {
				let {
					tempFiles
				} = e;

				const invalidIndices = []; // 格式校验失败文件索引

				tempFiles.forEach((element, index) => {
					let {
						name,
						url,
						uuid,
						file
					} = element;
					// 校验格式
					const validRes = this.isFileValid(file);
					console.log(validRes)
					if (validRes) {
						this.annexFile.annex_certificate.push({
							name,
							url,
							uuid,
							file,
						})
					} else {
						uni.$u.toast(`${file.name} 文件格式不支持`)
						invalidIndices.push(index)
					}
				});
				// 清除格式校验不通过文件
				invalidIndices.reverse().forEach(index => {
					this.$refs.annex_certificate.clearFiles(index);
				});
				// 上传文件
				this.annexFile.annex_certificate.forEach(file => {
					this.handleUploadFile(file, 'annex_certificate')
				})
			},
			handleUAISelect(e) {
				let {
					tempFiles
				} = e;

				const invalidIndices = []; // 格式校验失败文件索引

				tempFiles.forEach((element, index) => {
					let {
						name,
						url,
						uuid,
						file
					} = element;
					// 校验格式
					const validRes = this.isFileValid(file);
					console.log(validRes)
					if (validRes) {
						this.annexFile.annex_id.push({
							name,
							url,
							uuid,
							file,
						})
					} else {
						uni.$u.toast(`${file.name} 文件格式不支持`)
						invalidIndices.push(index)
					}
				});
				// 清除格式校验不通过文件
				invalidIndices.reverse().forEach(index => {
					this.$refs.annex_id.clearFiles(index);
				});
				// 上传文件
				this.annexFile.annex_id.forEach(file => {
					this.handleUploadFile(file, 'annex_id')
				})
			},
			handleUAPSelect(e) {
				let {
					tempFiles
				} = e;

				const invalidIndices = []; // 格式校验失败文件索引

				tempFiles.forEach((element, index) => {
					let {
						name,
						url,
						uuid,
						file
					} = element;
					// 校验格式
					const validRes = this.isFileValid(file);
					console.log(validRes)
					if (validRes) {
						this.annexFile.annex_photo.push({
							name,
							url,
							uuid,
							file,
						})
					} else {
						uni.$u.toast(`${file.name} 文件格式不支持`)
						invalidIndices.push(index)
					}
				});
				// 清除格式校验不通过文件
				invalidIndices.reverse().forEach(index => {
					this.$refs.annex_photo.clearFiles(index);
				});
				// 上传文件
				this.annexFile.annex_photo.forEach(file => {
					this.handleUploadFile(file, 'annex_photo')
				})
			},
			handleUAInSelect(e) {
				let {
					tempFiles
				} = e;

				const invalidIndices = []; // 格式校验失败文件索引

				tempFiles.forEach((element, index) => {
					let {
						name,
						url,
						uuid,
						file
					} = element;
					// 校验格式
					const validRes = this.isFileValid(file);
					console.log(validRes)
					if (validRes) {
						this.annexFile.annex_investigation.push({
							name,
							url,
							uuid,
							file,
						})
					} else {
						uni.$u.toast(`${file.name} 文件格式不支持`)
						invalidIndices.push(index)
					}
				});
				// 清除格式校验不通过文件
				invalidIndices.reverse().forEach(index => {
					this.$refs.annex_investigation.clearFiles(index);
				});
				// 上传文件
				this.annexFile.annex_investigation.forEach(file => {
					this.handleUploadFile(file, 'annex_investigation')
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.add {
		width: 100%;
		padding: 0 30rpx;
		box-sizing: border-box;

		.nav-left {
			display: flex;
			align-items: center;
			width: auto;
			color: #fff;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		&_body {

			box-sizing: border-box;
			width: 100%;
			height: 100vh;
			// background-color: #f6f6f6;
			display: flex;
			flex-direction: column;
			align-items: center;

			.add_content {

				width: 720rpx;
				height: 100%;

				// 日期选择框 placeholder 样式
				::v-deep .uni-date__x-input {
					// color: rgb(192, 196, 204);
					height: fit-content;
					line-height: 24px;
				}

				::v-deep .u-form-item__body__left {
					display: flex;
					align-items: flex-start;
				}

				::v-deep .u-textarea {
					padding: 0;
				}

				.operator {
					display: flex;
					padding: 16px 0;
					gap: 16px;
				}
			}

		}
	}
</style>