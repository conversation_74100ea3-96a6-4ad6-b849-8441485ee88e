(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_lifeCycle-pages-treatmentService-treatmentServiceInfo"],{"1a4f":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".grace-body[data-v-2fdc375e]{min-height:calc(100vh - %?120?%);padding-top:%?30?%;background-color:#f6f6f6;position:relative}.grace-body .treatmentServiceInfo[data-v-2fdc375e]{margin-bottom:%?20?%}.grace-body .treatmentServiceInfo .content[data-v-2fdc375e]{margin-top:%?20?%;background-color:#fff;border-radius:%?10?%;padding:%?20?% %?30?%}.grace-body .treatmentServiceInfo .content .item[data-v-2fdc375e]{display:flex;align-items:center;margin-bottom:%?10?%}.grace-body .treatmentServiceInfo .content .item .label[data-v-2fdc375e]{font-size:%?28?%;margin-right:%?10?%}.grace-body .treatmentServiceInfo .content .item .value[data-v-2fdc375e]{font-size:%?28?%}@media screen and (max-width:960px){.btn[data-v-2fdc375e]{bottom:3%;position:fixed;width:100%;left:0}.btn uni-button[data-v-2fdc375e]{width:90%;margin:0 auto;font-size:%?28?%;background-color:#008aff}}@media screen and (min-width:960px){.btn[data-v-2fdc375e]{bottom:3%;position:fixed;width:24rem;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.btn uni-button[data-v-2fdc375e]{width:90%;margin:0 auto;font-size:%?28?%;background-color:#008aff}}",""]),e.exports=t},2792:function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("e40e")),i={followupRecordList:function(e){return(0,r.default)({url:"manage/rehab/followupRecordList",method:"get",data:e})},treatmentInformationList:function(e){return(0,r.default)({url:"manage/rehab/treatmentInformationList",method:"get",data:e})},treatmentInformationDetail:function(e){return(0,r.default)({url:"manage/rehab/treatmentInformationDetail",method:"get",data:e})},medicationGuidanceList:function(e){return(0,r.default)({url:"manage/rehab/medicationGuidanceList",method:"get",data:e})},medicationGuidanceDetail:function(e){return(0,r.default)({url:"manage/rehab/medicationGuidanceDetail",method:"get",data:e})},recoveryInfo:function(e){return(0,r.default)({url:"manage/rehab/recoveryInfo",method:"get",data:e})},recoveryInfoUpload:function(e){return(0,r.default)({url:"manage/rehab/recoveryInfoUpload",method:"get",data:e})},personnel:function(e){return(0,r.default)({url:"manage/rehab/personnel",method:"get",data:e})},station:function(e){return(0,r.default)({url:"manage/rehab/station",method:"get",data:e})},appointment:function(e){return(0,r.default)({url:"manage/rehab/appointment",method:"get",data:e})},createAppointment:function(e){return(0,r.default)({url:"manage/rehab/createAppointment",method:"post",data:e})},createRehabGuideApplication:function(e){return(0,r.default)({url:"manage/rehab/createRehabGuideApplication",method:"post",data:e})}},o=i;t.default=o},4634:function(e,t,a){"use strict";a.r(t);var n=a("e576"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},b03d:function(e,t,a){"use strict";a.r(t);var n=a("d319"),r=a("4634");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("eb8c");var o=a("828b"),c=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"2fdc375e",null,!1,n["a"],void 0);t["default"]=c.exports},d2ec:function(e,t,a){var n=a("1a4f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("967d").default;r("5644eb1c",n,!0,{sourceMap:!1,shadowMode:!1})},d319:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={gracePage:a("93fe").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF"}},[a("my-header",{attrs:{slot:"gHeader",title:"诊疗详情"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"treatmentServiceInfo"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗信息")]),a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗时间:")]),a("v-uni-view",{staticClass:"value"},[e._v(e._s(e.currentRecord.treatmentDate))])],1),a("v-uni-view",{staticClass:"item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗机构:")]),a("v-uni-view",{staticClass:"value"},[e._v(e._s(e.currentRecord.stationName))])],1),a("v-uni-view",{staticClass:"item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗医师:")]),a("v-uni-view",{staticClass:"value"},[e._v(e._s(e.currentRecord.doctorName))])],1),a("v-uni-view",{staticClass:"item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗结果:")]),a("v-uni-view",{staticClass:"value"},[e._v(e._s(e.currentRecord.treatmentResult))])],1),a("v-uni-view",{staticClass:"item"},[a("v-uni-view",{staticClass:"label"},[e._v("职业病类别:")]),a("v-uni-view",{staticClass:"value"},[e._v(e._s(e.currentRecord.diseaseCategory))])],1)],1),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.download.apply(void 0,arguments)}}},[e._v("下载")])],1)],1)],1)],1)},i=[]},e576:function(e,t,a){"use strict";(function(e){a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2634")),i=n(a("2fdc"));a("e966");var o=n(a("2792")),c=n(a("20f8")),d={data:function(){return{currentRecord:null,recordId:null}},onLoad:function(e){this.recordId=parseInt(e.id)||0,this.fetchServiceDetail()},methods:{fetchServiceDetail:function(){var t=this;return(0,i.default)((0,r.default)().mark((function a(){var n;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.recordId){a.next=3;break}return t.errorMsg="参数错误，无法获取诊疗详情",a.abrupt("return");case 3:return a.prev=3,a.next=6,o.default.treatmentInformationDetail({id:t.recordId});case 6:n=a.sent,t.currentRecord=n.data,a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](3),e.error("请求失败",a.t0);case 13:case"end":return a.stop()}}),a,null,[[3,10]])})))()},download:function(){if(this.currentRecord&&this.currentRecord.url){var t=c.default.apiServer.substr(0,c.default.apiServer.length-1),a=t+this.currentRecord.url;e.log(a,"url"),window.open(a,"_blank")}else uni.showToast({title:"暂无数据",icon:"none"})}}};t.default=d}).call(this,a("ba7c")["default"])},eb8c:function(e,t,a){"use strict";var n=a("d2ec"),r=a.n(n);r.a}}]);