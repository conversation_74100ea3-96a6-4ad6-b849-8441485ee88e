.grace-header-content {
	padding-left: 12px;
}

.bg {
	position: relative;
	.box {
		position: absolute;
		left: 21px;
		top: 6px;
		height: 68px;
		
		
		.title {
			color: #FFFFFF;
			text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
			font-family: PangMenZhengDao;
			font-size: 30px;
			text-align: left;
		}
	}
}

.modules-box {
	width: 100%;
	position: relative;
	z-index: 1;
	margin-top: -100px;
	padding: 0 15px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	gap: 12px;
	
	.modules {
		width: 100%;
		border-radius: 5px;
		background: #FFFFFF;
		box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.0372);
		padding: 15px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 12px;
		
		.modules_title {
			font-family: Source Han Sans;
			font-size: 14px;
			font-weight: 700;
			color: #3D3D3D;
		}
		
		.module {
			display: grid;
			grid-template-columns: repeat(4, 1fr); /* 创建四列，每列占据等分空间 */
			text-align: center;
			gap: 12px;
			
			&_item {
				
				.icon-box {
					.icon {
						width: 40rpx;
						height: 40rpx;
					}
				}
				
				&_title {
					font-family: Source Han Sans;
					font-size: 12px;
					text-align: center;
					color: #000000;
				}
			}
		}
	}
}