<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="异常提醒" />

    <view slot="gBody" class="grace-body container">

      <graceNavBar :items="tabs" :currentIndex="currentIndex" @change="navChange" textAlign="center" :isCenter="true"
        :size='160' lineHeight="70rpx" activeColor="#3688FF" padding="30rpx" activeLineWidth="100%"></graceNavBar>


      <view class="infoCard grace-box-shadow" v-if="currentIndex === 0">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText"><text>筛选条件</text></view>
        </view>
        <view class="cardItem">
          <view style="margin-right: 1em;">
            检查类型
          </view>
          <picker mode=selector @change="examTypeChange" range-key="label" :value="examTypeIndex"
            :range="examTypeOption">
            <view class="uni-input">{{ examTypeLabel }}</view>
          </picker>
        </view>
      </view>

      <view class="card-section" v-if="currentIndex === 0">
        <view v-if="appointList.length === 0">
          <view class="empty">暂无数据</view>
        </view>
        <view class="card" v-for="item in appointList">
          <view class="title">
            <text></text>
            {{ item.desc }}
          </view>
          <view class="name">
            <text class="label">体检机构：</text>
            <text class="des">{{ item.physicalExamOrgName }}</text>
          </view>
          <view class="name">
            <text class="label">预约状态：</text>
            <text class="des" v-if="item.reservationStatu === 0">待预约</text>
            <text class="des" v-if="item.reservationStatu === 1">待审核</text>
            <text class="des" v-if="item.reservationStatu === 2">已通过</text>
            <text class="des" v-if="item.reservationStatu === 3">已拒绝</text>
          </view>
          <view class="name">
            <text class="label">体检时间：</text>
            <text class="des">{{ item.reservationDate || '尚未预约' }}</text>
          </view>
          <!-- <view class="name">
            <text class="label">登记状态：</text>
            <text class="des">{{ item.registerStatus }}</text>
          </view> -->
          <view class="name">
            <text class="label">体检类型：</text>
            <text class="des" v-if="item.examType === 0">离岗</text>
            <text class="des" v-if="item.examType === 1">岗前</text>
            <text class="des" v-if="item.examType === 2">在岗</text>
          </view>
        </view>
      </view>

      <view v-if="currentIndex === 1">
        <view class="health-check-item" v-for="(item, index) in warningList" :key="index">
          <view class="item-header">
            <view class="org-name">{{ item.projectName }}</view>
            <view class="date">{{ item.checkTime }}</view>
          </view>

          <view class="item-content">
            <view class="check-info">
              <!-- <view class="info-item">
              <text class="label">检查结果：</text>
              <view class="value-group">
                <text class="value warning">{{ item.result }}</text>
                <text class="unit">{{ item.unit }}</text>
              </view>
            </view> -->
              <view class="info-item">
                <text class="label">检查结果：</text>
                <text class="value">{{ item.result }}</text>
              </view>
              <view class="info-item">
                <text class="label">单位：</text>
                <text class="value">{{ item.unit }}</text>
              </view>
              <view class="info-item">
                <text class="label">参考范围：</text>
                <text class="value">{{ item.referenceRange }}</text>
              </view>
              <view class="info-item">
                <text class="label">异常描述：</text>
                <text class="value warning">{{ item.warningType }}</text>
              </view>
            </view>
          </view>
        </view>

        <view v-if="warningList.length === 0" class="empty-state">
          <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无异常数据</text>
          <text class="empty-subtext">您的体检指标都在正常范围内</text>
        </view>
      </view>
    </view>
  </gracePage>
</template>

<script>
import healthApi from '@/api/health.js'
import userApi from '@/api/user.js' //导入接口
import moment from 'moment';

export default {
  data() {
    return {
      tabs: ['体检提醒', '异常提醒'],
      currentIndex: 0,
      // 异常提醒
      reportList: [],
      warningList: [],
      // 体检提醒 
      appointList: [],
      // 检查类型筛选
      examTypeOption: [
        { value: '', label: '全部' },
        { value: '1', label: '岗前' },
        { value: '2', label: '在岗' },
        { value: '0', label: '离岗' },
      ],
      examTypeLabel: '全部',
      examTypeIndex: 0,
    }
  },
  created() {
    // 预约
    this.examTypeLabel = this.examTypeOption[this.examTypeIndex].label;
    this.getAppointList();
    // 报告
    // this.getReportList()
  },

  watch: {
    currentIndex(newVal) {
      if (newVal === 0) {
        this.getAppointList();
      } else if (newVal === 1) {
        this.getReportList();
      }
    }
  },

  methods: {
    // 获取体检预约列表
    async getAppointList() {
      const res = await userApi.getHcAppointmentList();
      if (res.status === 200) {
        const today = moment();
        let list = res.data
          .filter(item => {
            return item.reservationStatu == 0 || item.reservationStatu == 3 || (item.reservationStatu == 2 && !item.registerStatus)
          });
        // 检查类型筛选
        const examTypeValue = this.examTypeOption[this.examTypeIndex].value;
        if (examTypeValue !== '') {
          list = list.filter(item => String(item.examType) === examTypeValue);
        }
        this.appointList = list.map(item => {
          let desc = '';
          if (!item.reservationDate) {
            desc = '请预约体检日期';
          } else if (item.reservationStatu === 0) {
            desc = '请预约体检日期';
          } else if (item.reservationStatu === 3) {
            desc = '体检预约已被拒绝，请重新预约';
          } else if (moment(item.reservationDate).isBefore(today, 'day')) {
            desc = '预约日期已过，请重新预约';
          } else if (moment(item.reservationDate).isSame(today, 'day')) {
            desc = '体检日期已到期，请及时前往';
          } else if (today.isBefore(moment(item.reservationDate).add(7, 'days'), 'day')) { //体检日期在7天之内
            desc = '体检日期临近，请记得准时前往';
          } else { // 体检日期临近
            desc = '体检已预约，请记得准时前往';
          }

          return {
            ...item,
            desc,
            reservationDate: item.reservationDate && moment(item.reservationDate).format("YYYY-MM-DD")
          }
        });
      }
    },
    // 检查类型切换
    examTypeChange(e) {
      this.examTypeIndex = e.detail.value;
      this.examTypeLabel = this.examTypeOption[this.examTypeIndex].label;
      this.getAppointList();
    },

    async getReportList() {
      try {
        const res = await healthApi.reportList();
        if (!res.data || res.data.length === 0) {
          uni.showToast({
            title: '暂无体检记录',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        this.reportList = res.data;
        this.reportList.forEach(report => {
          this.processWarningItems(report);
        });
      } catch (error) {
        console.error('获取体检记录失败:', error);
        uni.showToast({
          title: '获取体检记录失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    processWarningItems(report) {
      const warnings = [];
      this.warningList = [];
      const checkTime = new Date(report.registerTime).toLocaleDateString();
      report.checkDepartments.forEach(dept => {
        dept.checkProjects.forEach(project => {
          project.checkItems.forEach(item => {
            const result = parseFloat(item.result);
            const min = parseFloat(item.itemId.standardValueMin);
            const max = parseFloat(item.itemId.standardValueMax);
            // 检查是否超出参考范围
            if (result < min || result > max) {
              warnings.push({
                projectName: item.itemId.projectName,
                result: item.result,
                unit: item.itemId.msrunt,
                referenceRange: `${min} - ${max}`,
                // warningType: result < min ? '偏低' : '偏高',
                warningType: item.conclusion || `${item.itemId.projectName}${result < min ? '偏低' : '偏高'}`,
                checkTime: checkTime
              });
            }
          });
        });
      });

      this.warningList.push(...warnings);
    },
    // 导航切换
    navChange: function (e) {
      this.currentIndex = e;
    },
  }
}
</script>

<style lang="scss" scoped>
.health-check-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);

  &:first-child {
    margin-top: 20rpx;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #f0f0f0;

    .date {
      font-size: 14px;
      color: #666666;
    }

    .org-name {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333333;
      font-weight: 500;

      text {
        margin-left: 10rpx;
      }
    }
  }

  .item-content {
    padding: 20rpx 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .check-info {
    margin-top: 20rpx;

    .info-item {
      margin-bottom: 16rpx;
      font-size: 14px;
      color: #666666;

      .label {
        color: #999999;
      }

      .value-group {
        display: flex;
        align-items: center;
        gap: 4rpx;

        .value {
          &.warning {
            color: #f5222d;
            font-weight: 500;
          }
        }

        .unit {
          color: #999999;
          font-size: 24rpx;
        }
      }
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 16rpx;
  }

  .empty-subtext {
    font-size: 28rpx;
    color: #999999;
  }
}

.empty {
  text-align: center;
  color: #999;
  font-size: 14px;
  margin-top: 20px;
}

.card-section {
  .card {
    width: 100%;
    border-radius: 5px;
    background: #FFFFFF;
    margin-top: 12px;
    padding: 14px;
    box-sizing: border-box;
    box-shadow: 2px 2px 10px 2px rgba(0, 0, 0, 0.1);

    .title {
      font-family: PingFangSC;
      font-size: 14px;
      color: #555555;
      display: flex;
      align-items: center;
      margin-left: -14px;
      margin-bottom: 15px;

      text {
        display: inline-block;
        width: 6px;
        height: 20px;
        border-radius: 3px 3px 0px 3px;
        background: #FE3E3E;
        margin-right: 12px;
      }
    }

    .name {
      font-size: 14px;
      margin-bottom: 6px;

      .label {
        margin-right: 24px;
        color: #000;
      }

      .des {
        color: #555555;
      }
    }

    .operaction {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 24px;

      view {
        text-align: center;
        line-height: 33px;
        width: 81px;
        height: 33px;
        border-radius: 3px;
      }

      .cancel-btn {
        color: #3E73FE;
        border: 1px solid #3E73FE;
        text-align: center;
        line-height: 33px;
      }

      .edit-btn {
        background: #3E73FE;
        border: 1px solid #3E73FE;
        color: #fff;
        margin-left: 12px;
      }
    }
  }
}

.infoCard {
  padding: 15px;
  border-radius: 10px;
  margin-top: 12.5px;
  position: relative;

  .cardTitle {
    margin-bottom: 16px;

    .titleText {
      font-weight: 600;
      font-size: 28rpx;
      line-height: 28rpx;
      letter-spacing: 0px;
      color: #555555;

      display: flex;
      justify-content: space-between;
    }

    .titlePoint {
      position: absolute;
      left: 0px;
      top: 13px;
      width: 6px;
      height: 20px;
      border-radius: 3px 3px 0px 3px;
      opacity: 1;

      background: #3E73FE;
    }
  }

  .cardItem {
    display: flex;
    margin-bottom: 12px;
    justify-content: space-between;

    font-size: 24rpx;
    font-weight: normal;
    line-height: 28rpx;
    letter-spacing: 0px;
    color: #555555;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

picker {
  .uni-input {
    color: #A8ABB2;
  }

  .uni-input::after {
    content: '>';
    display: inline-block;
    transform: rotate(90deg) scaleY(1.5) translate(0, -0.25em);
    margin-right: 1em;
  }
}
</style>
