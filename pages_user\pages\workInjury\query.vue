<template>
	<view class="diagnosis-result">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="@/static/leftArrow.svg" mode=""></image>
					工伤查询
				</view>
			</block>
		</uni-nav-bar>
		<view class="search-wrap">
			<uni-datetime-picker type="date" :value="form.singleTime" @change="handleSearch" />
		</view>

		<view class="diagnosis-list">
			<u-empty v-if="list.length == 0" mode="data" icon="/static/empty.png" text="暂无记录">
			</u-empty>
			<u-list>
				<u-list-item v-for="(item, index) in list" :key="index">
					<view class="list-item">
						<view class="item-header">
							<text class="time">{{item.payTime}}</text>
							<u-tag :text="item.status" :type="statusTypeMap[item.status]" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<u-row>
								<u-col span="4">
									<view class="label">发放金额</view>
									<view class="value price">¥{{item.amount}}</view>
								</u-col>
								<u-col span="4">
									<view class="label">发放方式</view>
									<view class="value">{{item.payMethod}}</view>
								</u-col>
								<u-col span="4">
									<view class="label">发放进度</view>
									<view class="value">{{item.progress}}%</view>
								</u-col>
							</u-row>
						</view>
						<view class="item-footer">
							<text class="remark">备注：{{item.remark}}</text>
						</view>
					</view>
				</u-list-item>
			</u-list>
		</view>
	</view>
</template>

<script>
	import diagnosisApi from '@/api/diagnosis'
	import {
		mapGetters
	} from 'vuex'
	import moment from 'moment'
	export default {
		data() {
			return {
				form: {
					singleTime: '',
				},
				timeTypes: [{
						name: '时间段查询',
						value: 'range'
					},
					{
						name: '单日查询',
						value: 'single'
					}
				],
				statusTypeMap: {
					'已发放': 'success',
					'发放中': 'warning',
					'未发放': 'error'
				},
				list: [{
						payTime: '2025-03-15',
						amount: '8200.00',
						payMethod: '社保卡发放',
						progress: 60,
						status: '发放中',
						remark: '工伤医疗补助金'
					}, {
						payTime: '2025-01-15',
						amount: '8560.00',
						payMethod: '银行转账',
						progress: 100,
						status: '已发放',
						remark: '工伤保险定期待遇发放'
					},
					{
						payTime: '2024-07-15',
						amount: '8420.00',
						payMethod: '社保卡发放',
						progress: 80,
						status: '发放中',
						remark: '工伤医疗补助金'
					},

				]
			}
		},
		created() {},
		mounted() {},
		computed: {
			...mapGetters(["userInfo"]),
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			// 查询处理
			handleSearch(val) {
				if (val) {
					this.list = this.list.filter(item => item.payTime == val)
				} else {
					this.loadData()
				}
			},

			// 重置表单
			resetForm() {
				this.form = {
					singleTime: '',
				}
				this.loadData()
			},

			// 模拟加载数据
			loadData() {
				// 实际应调用接口获取数据
				this.list = [{
						payTime: '2025-03-15',
						amount: '8200.00',
						payMethod: '社保卡发放',
						progress: 60,
						status: '发放中',
						remark: '工伤医疗补助金'
					}, {
						payTime: '2025-01-15',
						amount: '8560.00',
						payMethod: '银行转账',
						progress: 100,
						status: '已发放',
						remark: '工伤保险定期待遇发放'
					},
					{
						payTime: '2024-07-15',
						amount: '8420.00',
						payMethod: '社保卡发放',
						progress: 80,
						status: '发放中',
						remark: '工伤医疗补助金'
					}
				]
			}
		}
	}
</script>

<style lang="scss" scoped>
	.diagnosis-result {
		padding: 0 30rpx;

		.nav-left {
			display: flex;
			align-items: center;
			color: #fff;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}

		.diagnosis-list {
			margin-top: 20rpx;

			.list-item {
				background: #fff;
				margin: 20rpx;
				padding: 30rpx;
				border-radius: 12rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;

					.time {
						font-size: 28rpx;
						color: #606266;
					}
				}

				.item-body {
					.label {
						font-size: 24rpx;
						color: #909399;
						margin-bottom: 10rpx;
					}

					.value {
						font-size: 28rpx;
						color: #303133;
					}

					.price {
						color: #e4393c;
						font-weight: bold;
					}
				}

				.item-footer {
					margin-top: 20rpx;
					padding-top: 20rpx;
					border-top: 1rpx solid #eee;

					.remark {
						font-size: 24rpx;
						color: #909399;
					}

				}
			}
		}
	}

	.search-wrap {
		margin-top: 10px;
		height: 70rpx;
		border-radius: 30rpx;
	}
</style>