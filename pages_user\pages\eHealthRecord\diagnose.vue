<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="诊断医疗记录" />
    <view slot="gBody" class="grace-body container">
      <!-- 顶部标签页 -->
      <view class="tabs">
        <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: currentTab === index }"
          @tap="switchTab(index)">
          <text>{{ tab }}</text>
        </view>
      </view>
      <!-- 内容区域 -->
      <scroll-view class="content-area" scroll-y>
        <!-- 诊断记录 -->
        <view v-if="currentTab === 0" class="record-list">
          <view v-if="diagnosisRecords.length === 0" class="empty-data">
            <view class="empty-icon">
              <uni-icons type="info" size="60" color="#c0c4cc"></uni-icons>
            </view>
            <view class="empty-text">暂无诊断记录</view>
          </view>
          <view v-for="(item, index) in diagnosisRecords" :key="index" class="record-card">
            <view class="time-line">
              <view class="date-box">
                <text class="date-year">{{ formatDateYear(item.diagnosisDate) }}</text>
                <text class="date">{{ formatDateMonthDay(item.diagnosisDate) }}</text>
              </view>
              <view class="line"></view>
            </view>
            <view class="card-content">
              <view class="info-item">
                <text class="label">用人单位名称：</text>
                <text class="value">{{ item.employerName }}</text>
              </view>
              <view class="info-item">
                <text class="label">诊断结论：</text>
                <text class="value highlight">{{ getDiagnosisResult(item) }}</text>
              </view>
              <view class="info-item">
                <text class="label">处理意见：</text>
                <text class="value">{{ item.treatmentOpinion }}</text>
              </view>
              <view class="info-item">
                <text class="label">诊断机构：</text>
                <text class="value">{{ item.diagnosisInstitution }}</text>
              </view>
              <view class="info-item">
                <u-button style="flex: 1;" type="primary" size="mini" @click="downloadCertificate(item)">点击下载诊断证明书
                </u-button>
              </view>

            </view>
          </view>
        </view>
        <!-- 鉴定记录 -->
        <view v-if="currentTab === 1" class="record-list">
          <view v-if="identificationRecords.length === 0" class="empty-data">
            <view class="empty-icon">
              <uni-icons type="info" size="60" color="#c0c4cc"></uni-icons>
            </view>
            <view class="empty-text">暂无鉴定记录</view>
          </view>
          <view v-for="(item, index) in identificationRecords" :key="index" class="record-card">
            <view class="time-line">
              <view class="date-box">
                <text class="date-year">{{ formatDateYear(item.determinationDate) }}</text>
                <text class="date">{{ formatDateMonthDay(item.determinationDate) }}</text>
              </view>
              <view class="line"></view>
            </view>
            <view class="card-content">
              <view class="info-item">
                <text class="label">鉴定类别：</text>
                <text class="value">{{ item.determinationCategory === '2' ? '再鉴定' : '首次鉴定' }}</text>
              </view>
              <view class="info-item">
                <text class="label">用人单位名称：</text>
                <text class="value">{{ item.employerName }}</text>
              </view>
              <view class="info-item">
                <text class="label">申请鉴定主要理由：</text>
                <text class="value">{{ item.applicationReason }}</text>
              </view>
              <view class="info-item">
                <text class="label">鉴定依据：</text>
                <text class="value">{{ item.determinationBasis }}</text>
              </view>
              <view class="info-item">
                <text class="label">鉴定结论：</text>
                <text class="value highlight">{{ getDeterminationResult(item) }}</text>
              </view>
              <view class="info-item">
                <text class="label">诊断鉴定委员会：</text>
                <text class="value">{{ item.determinationCommittee }}</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 就诊记录 -->
        <view v-if="currentTab === 2" class="record-list">
          <view v-if="medicalRecords.length === 0" class="empty-data">
            <view class="empty-icon">
              <uni-icons type="info" size="60" color="#c0c4cc"></uni-icons>
            </view>
            <view class="empty-text">暂无就诊记录</view>
          </view>
          <view v-for="(item, index) in medicalRecords" :key="index" class="record-card">
            <view class="time-line">
              <view class="date-box">
                <text class="date-year">{{
                  item.date ? item.date.substring(0, 4) + "年" : ""
                }}</text>
                <text class="date">{{
                  item.date
                    ? item.date.substring(5, 7) +
                    "月" +
                    item.date.substring(8, 10) +
                    "日"
                    : ""
                }}</text>
              </view>
              <view class="line"></view>
            </view>
            <view class="card-content">
              <view class="header">
                <text class="hospital">{{ item.hospital }}</text>
                <text class="department">{{ item.department }}</text>
              </view>
              <view class="detail-content" :class="{ expanded: item.expanded }">
                <view class="info-item">
                  <text class="label">主诉：</text>
                  <text class="value">{{ item.complaint }}</text>
                </view>
                <view class="info-item">
                  <text class="label">诊断结果：</text>
                  <text class="value highlight">{{ item.diagnosis }}</text>
                </view>
                <view class="info-item">
                  <text class="label">治疗措施：</text>
                  <text class="value">{{ item.treatment }}</text>
                </view>
              </view>
              <view class="expand-btn" @tap="toggleExpand(index)">
                <text>{{ item.expanded ? "收起" : "展开" }}</text>
                <uni-icons :type="item.expanded ? 'top' : 'bottom'" size="14" />
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </gracePage>
</template>
<script>
import recordApi from "@/api/record.js";
import { mapGetters } from "vuex";
import moment from "moment";
export default {
  data() {
    return {
      tabs: ["诊断记录", "鉴定记录", "就诊记录"],
      currentTab: 0,
      isRefreshing: false,
      // 诊断记录
      diagnosisRecords: [],
      // 鉴定记录
      identificationRecords: [],
      // 就诊记录
      medicalRecords: [],
      // 查询参数
      postData: {
        status: "",
        idCardType: "1",
        idCardCode: "",
        pageNum: 1,
        pageSize: 100,
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.postData.idCardCode = this.userInfo?.idNo || "";
    this.getDiagnosisList();
    this.getIdentificationList();
  },
  methods: {

    // 下载诊断证明书
    downloadCertificate(item) {
      if (!item.certificateUrl) {
        uni.showToast({
          title: "当前诊断记录暂无诊断证明书",
          icon: "none"
        });
        return;
      }
      window.open(item.certificateUrl, '_blank');
    },

    // 获取诊断记录
    async getDiagnosisList() {
      const res = await recordApi.getDiaList(this.postData);
      this.diagnosisRecords = res.data;
    },

    // 获取鉴定记录
    async getIdentificationList() {
      const res = await recordApi.getIdentificationList(this.postData);
      this.identificationRecords = res.data;
    },

    switchTab(index) {
      this.currentTab = index;
    },
    toggleExpand(index) {
      this.medicalRecords[index].expanded =
        !this.medicalRecords[index].expanded;
    },

    // 格式化日期
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD");
    },

    // 格式化日期年份
    formatDateYear(date) {
      return moment(date).format("YYYY") + "年";
    },

    // 格式化日期月日
    formatDateMonthDay(date) {
      return (
        moment(date).format("MM") + "月" + moment(date).format("DD") + "日"
      );
    },

    // 获取状态样式
    getStatusClass(status) {
      const statusMap = {
        "-1": "status-danger", // 将负数值转换为字符串
        0: "status-warning",
        1: "status-success",
        2: "status-info",
        4: "status-warning",
        99: "status-success",
      };
      return statusMap[status] || "";
    },

    getDiagnosisResult(row) {
      if (row.diagnosisConclusionDescription) {
        return row.diagnosisConclusionDescription;
      } else {
        return row.hasOccupationalDisease ? '患有职业病' : '未患有职业病';
      }
    },

    getDeterminationResult(row) {
      if (row.determinationConclusionDescription) {
        return row.determinationConclusionDescription;
      } else {
        return row.hasOccupationalDisease ? '患有职业病' : '未患有职业病';
      }
    },
  },
};
</script>
<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.tabs {
  display: flex;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;

  width: 107%;
  position: relative;
  left: -12px;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #606266;
  position: relative;
}

.tab-item.active {
  color: #409eff;
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #409eff;
  border-radius: 2rpx;
}

.content-area {
  flex: 1;
  overflow: auto;
}

.record-list {
  padding: 20rpx;
}

.record-card {
  display: flex;
  margin-bottom: 30rpx;
}

.time-line {
  width: 138rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.date-box {
  background-color: #409eff;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100rpx;
}

.date-year {
  color: #ffffff;
  font-size: 12px;
  margin-bottom: 4rpx;
}

.date {
  color: #ffffff;
  font-size: 12px;
}

.line {
  width: 2rpx;
  height: 100%;
  background-color: #dcdfe6;
  margin-top: 20rpx;
}

.card-content {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  margin-bottom: 20rpx;
}

.label {
  color: #909399;
  font-size: 14px;
}

.value {
  color: #303133;
  font-size: 14px;
}

.highlight {
  color: #409eff;
  font-weight: 500;
}

.type-tag {
  display: inline-block;
  padding: 6rpx 20rpx;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4rpx;
  font-size: 12px;
  margin-bottom: 20rpx;
}

.status-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-bottom: 20rpx;

  &.status-success {
    background: #e1f3d8;
    color: #67c23a;
  }

  &.status-warning {
    background: #fdf6ec;
    color: #e6a23c;
  }

  &.status-info {
    background: #ecf5ff;
    color: #409eff;
  }

  &.status-danger {
    background: #fef0f0;
    color: #f56c6c;
  }
}

.first-identification .type-tag {
  background-color: #f0f9eb;
  color: #67c23a;
}

.header {
  margin-bottom: 20rpx;
}

.hospital {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
  margin-right: 20rpx;
}

.department {
  font-size: 14px;
  color: #909399;
}

.detail-content {
  max-height: 104rpx;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.detail-content.expanded {
  max-height: 800rpx;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  color: #909399;
  font-size: 14px;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #909399;
  }
}
</style>