"use strict";
import req from '@/utils/http.js' //导入 封装的请求

export default {
	// 获取体检机构列表
	getCheckHealthList: (data) => {
		return req({
			url: `manage/btHealthCheck/checkOrgList`,
			method: 'post',
			data
		});
	},
	// 查看单个体检机构的详细信息
	getOneDetail: data => {
		return req({
			url: `manage/btHealthCheck/getOneDetail`,
			method: 'get',
			data: {
				id: data.id
			}
		});
	},
	// 查看体检预约记录
	getappointmentRecord: data => {
		return req({
			url: `manage/btHealthCheck/appointmentRecord`,
			method: 'get',
			data
		});
	},
	// 体检预约
	getappointment: data => {
		return req({
			url: `manage/btHealthCheck/appointment`,
			method: 'post',
			data
		});
	},
	// 取消体检预约
	cancelAppointment: data => {
		return req({
			url: `manage/btHealthCheck/cancelAppointment`,
			method: 'post',
			data
		});
	},
	// 编辑体检预约
	updateAppointment: data => {
		return req({
			url: `manage/btHealthCheck/updateAppointment`,
			method: 'post',
			data
		});
	},
	// 职业健康检查报告查询
	checkReport: data => {
		return req({
			url: `manage/btHealthCheck/checkReport`,
			method: 'get',
			data
		});
	},

	// 获取报告列表
	reportList: data => {
		return req({
			url: `manage/btHealthCheck/reportList`,
			method: 'get',
			data
		});
	},

	// 撤回体检预约
	cancelReservation: data => {
		return req({
			url: `manage/btHealthCheck/cancelReservation`,
			method: 'get',
			data
		});
	},
	// 获取体检报告授权列表
	getHCReportAuthList: data => {
		return req({
			url: `manage/btHealthCheck/getHCReportAuthList`,
			method: 'get',
			data
		});
	},

	// 更新体检报告授权状态
	updateHCReportAuthStatus: data => {
		return req({
			url: `manage/btHealthCheck/updateHCReportAuthStatus`,
			method: 'post',
			data
		});
	},

}