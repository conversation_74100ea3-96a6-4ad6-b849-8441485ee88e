<template>
	<view class="lifeCycleItem">
		<image :src="$props.info.icon" mode="widthFix"></image>
		<view class="text">
			<view class="title">
				{{$props.info.title}}
			</view>
			<view class="label">
				{{$props.info.label}}
			</view>
		</view>
		<view class="btn" @click="goto($props.info.path)">{{$props.info.btntext}}</view>
	</view>
</template>

<script>
	export default {
		props: {
			info: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {

			};
		},
		methods: {
			goto(path) {
				console.log(path);
				uni.navigateTo({
					url: path
				})
			}
		},
		mounted() {}
	}
</script>

<style scoped>
	.lifeCycleItem {
		background-color: #fff;
		display: flex;
		align-items: center;
		padding: 24rpx;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
		font-family: Source Han <PERSON>;
		font-variation-settings: "opsz" auto;
	}

	.lifeCycleItem image {
		width: 102rpx;
		height: 102rpx;

	}

	.lifeCycleItem .text {
		flex: 1;
		margin: 0 24rpx;
	}

	.lifeCycleItem .title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 12rpx;
	}

	.lifeCycleItem .label {
		font-size: 24rpx;
		color: #666666;
		width: 85%;
	}

	.lifeCycleItem .btn {
		width: 120rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background: #F0F9EB;
		border-radius: 28000rpx;
		font-size: 24rpx;
		color: #67C23A;

		border: 1px solid #B3E09C;
	}
</style>