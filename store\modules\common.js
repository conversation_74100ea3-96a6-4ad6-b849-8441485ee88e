import { getDict } from '@/api/system/dict/data'

const state = {
  areaList: [],
  processedAreaList: []
}

const mutations = {
  SET_AREA_LIST: (state, areaList) => {
    state.areaList = areaList
    // Process the area list for uni-data-picker
    state.processedAreaList = processAreaData(areaList)
  }
}

const actions = {
  // Get area list
  getAreaList({ commit }) {
    return new Promise((resolve, reject) => {
      getDict('sys_area').then(response => {
        const areaList = response.data
        commit('SET_AREA_LIST', areaList)
        resolve(areaList)
      }).catch(error => {
        reject(error)
      })
    })
  }
}

// Helper function to process area data
function processAreaData(data) {
  if (!data) return []
  return data.map(item => ({
    text: item.dictLabel,
    value: item.dictCode,
    children: item.children ? processAreaData(item.children) : null
  }))
}

const getters = {
  areaList: state => state.processedAreaList
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 