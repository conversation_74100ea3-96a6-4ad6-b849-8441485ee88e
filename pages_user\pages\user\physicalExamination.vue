<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="体检信息" />
		<!-- <graceShade background='@/static/chat.png'></graceShade> -->
		<!-- 页面主体 -->
		<view slot="gBody" class="grace-body">
			<graceDialog title='体检确认' closeBtnColor='#999' :show='showConfirm' ref="confirmDialog"
				@closeDialog="showConfirm = false">
				<view class="" slot="content">
					<view class="signatrue">
						<image class="signatrueImg" :src="apiServer + signatrue"></image>
					</view>
				</view>
				<view slot="btns" class="grace-space-between">
					<text class="grace-dialog-buttons" style="border-right: 1px solid #ccc;"
						@tap="showConfirm = false">关闭</text>
					<text class="grace-dialog-buttons grace-blue" @tap="confirmCheck">确认</text>
				</view>
			</graceDialog>
			<view class="grace-accordion grace-margin-top">
				<!-- 项目2 -->
				<graceNavBar :items="tabs" :currentIndex="currentIndex" @change="navChange" textAlign="center"
					:isCenter="true" :size='160' lineHeight="70rpx" activeColor="#3688FF" padding="30rpx"
					activeLineWidth="100%"></graceNavBar>
				<!-- 体检结果 -->
				<view style="margin-top: 10px;" v-if="currentIndex === 0">
					<view class="card grace-box-shadow infoCardBg" style="position: relative;padding: 5px 10px;">
						<image :src="infoCardImg" style="width: 100%;height:165px;"></image>
						<view style="position: absolute;top: 21px;">
							<view class="infoCard">
								<text class="nameStyle">{{ userInfo.name }}</text>
								<text class="sexStyle" v-if="userInfo.gender === '0'">男{{ userInfo.age }}岁</text>
								<text class="sexStyle" v-else>女{{ userInfo.age || '' }}岁</text>
								<text class="sexStyle">工龄{{ userInfo.workYears || '' }}</text>
							</view>
							<view class="personInfo infoCard nameColor">{{ userInfo.company || '' }}</view>
							<view class="personInfo infoCard nameColor">{{ userInfo.workType || '' }}</view>
							<view class="personInfo infoCard grace-ellipsis nameColor">职业病危害因素：{{ harmFactors || '无' }}
							</view>
							<view class="personInfo infoCard grace-ellipsis nameColor">可能导致的职业病：{{ illnessInfo || '无' }}
							</view>
						</view>
					</view>
					<view class="card grace-box-shadow" v-for="(item, index) in physicalBefore" :key="index">
						<view class="cardTitle grace-black">
							<view class="cardTitleText"
								:class="{ confirmed: item.confirmStatus, unconfirmed: !item.confirmStatus }">
								<view>{{ item.createdAt.substring(0, 4) }}年体检</view>
							</view>
						</view>
						<view>
							<text class="grace-tags margin"
								style="background: #daecff;color: #3e73fe;font-size: 12px;">{{
									calculateMedicalType(item) }}</text>
							<text class="grace-tags margin"
								style="background: #daecff;color: #3e73fe;font-size: 12px;">{{
									item.createdAt }}</text>
							<text class="grace-tags margin grace-icons icon-time"
								style="background: #fff7e6;color: #fe930c;font-size: 12px;">{{ item.isSubmitGuideForm ?
									'' :
								'请提交引导单' }}</text>
						</view>
						<view class="cellItem" v-if="!item.confirmStatus">
							<view class="label grace-black">截止日期</view>
							<view class="value grace-black6">{{ getEndDate(item.createdAt) }}</view>
						</view>
						<view class="cellItem">
							<view class="label grace-black">体检医院</view>
							<view class="value grace-black6" v-if="item.hospitalConfirm.orgName">{{
								item.hospitalConfirm.orgName }}
							</view>
							<zg-data-select v-else placeholder="请选择体检医院" v-model="selectValue"
								:localdata="item.hospitals" @on-select="selectHospital"></zg-data-select>
							<graceDialog :show="showHospitalConfirm" closeBtnColor="#FFFFFF" ref="graceDialog2"
								title="选择体检医院" v-on:closeDialog="closeDialog2">
								<view class="content2" slot="content" v-if="selectHospitalData">
									<text>确认选择{{ selectHospitalData.item.text }}</text>
								</view>
								<view slot="btns" class="grace-space-between">
									<text class="grace-dialog-buttons" @tap="closeDialog2">关闭</text>
									<text class="grace-dialog-buttons grace-blue" @tap="confirm2">确认</text>
								</view>
							</graceDialog>
						</view>
						<view class="cellItem" v-if="item.hospitalConfirm.orgName">
							<view class="label grace-black">引导单</view>
							<view class="value abnormal" @click="selectUpload(item._id)">
								<graceSelectImg :maxFileNumber="1" @removeImg="removeImg(e, item)" @change="imgsChange"
									:items="item.previewPic ? [`${item.previewPic}`] : []" btnName="上传引导单">
								</graceSelectImg>
							</view>
						</view>
						<view class="btnStyle" v-else>
							<button type="primary" v-if="getEndDate(item.createdAt) !== '已过期'" class="grace-button"
								@click="confirmSelectHospital(item)" size="mini">确认选择</button>
						</view>
					</view>
					<view class="card grace-box-shadow" v-for="(item, index) in physicalResults" :key="index">
						<view class="cardTitle grace-black">
							<view class="cardTitleText"
								:class="{ confirmed: item.confirmStatus, unconfirmed: !item.confirmStatus }">
								<view>{{ item.checkDate.substring(0, 4) }}年体检</view>
							</view>
						</view>
						<view>
							<text class="grace-tags margin"
								style="background: #daecff;color: #3e73fe;font-size: 12px;">{{
									item.checkType }}</text>
							<text class="grace-tags margin"
								style="background: #daecff;color: #3e73fe;font-size: 12px;">{{
									item.checkDate }}</text>
							<text class="grace-tags margin grace-icons icon-time"
								style="background: #fff7e6;color: #fe930c;font-size: 12px;">体检报告请确认签字</text>
						</view>
						<view class="cellItem">
							<view class="label grace-black">体检机构</view>
							<view class="value grace-black6">{{ item.organization }}</view>
						</view>
						<view class="cellItem">
							<view class="label grace-black">体检结论</view>
							<view class="value abnormal">{{ item.CwithO }}</view>
						</view>
						<view class="cellItem">
							<view class="label grace-black">医学建议</view>
							<view class="value grace-green grace-ellipsis">{{ item.dedicalAdvice || '无' }}</view>
						</view>
						<view class="cellItem">
							<view class="label grace-black">确认结果</view>
							<view class="value grace-green" v-if="item.confirmStatus === true">已确认</view>
							<view class="value abnormal" v-else>未确认</view>
						</view>
						<view class="cellItem">
							<view class="label grace-black">体检报告</view>
							<view class="value grace-blue" v-if="item.caseCard && item.caseCard.staticName"
								@click="goToPreview(item)">{{ item.caseCard.originName }}</view>
							<view class="value" v-else style="background: #fff7e6;color: #fe930c;">未上传报告</view>
						</view>
						<view class="btnStyle" v-if="item.caseCard && item.caseCard.staticName">
							<button v-if="item.confirmStatus" type="primary" class="grace-button"
								@click="goToPreview(item)" size="mini">查看报告</button>
							<button v-else type="primary" class="grace-button" size="mini"
								@click="confirmSign(item)">确认签字</button>
						</view>
						<view class="btnStyle" v-if="item.caseCard && !item.caseCard.staticName">
							<button type="primary" class="grace-button" @click="goToDetail(item)"
								size="mini">查看详情</button>
						</view>
					</view>
				</view>
				<!-- 体检提醒 -->
				<view v-else-if="currentIndex === 1" class="noticeMain">
					<view class="card grace-box-shadow" v-if="physicalExaminationNotice.type">
						<view class="cardTitle color-black">
							<view class="cardTitleText">
								<svg class="titleIcon icon" aria-hidden="true">
									<use xlink:href="#icon-MBEfenggeduosetubiao-tixing"></use>
								</svg>
								<view>{{ physicalExaminationNotice.type }}体检通知</view>
							</view>
							<text class="grace-tags grace-tbr grace-bg-red">未完成</text>
						</view>
						<view class="cardContent"><text class="noticePerson orange">{{ userInfo.name
						}}</text>：<rich-text class="contentText" :nodes="noticeContent"></rich-text></view>
						<view class="order" @click="open">预约</view>
					</view>
					<!-- xjbt -->
					<view class="card-section">
						<view v-if="appointList.length === 0">
							<view class="empty">暂无数据</view>
						</view>
						<view class="card" v-for="item in appointList">
							<view class="title">
								<text></text>
								{{ item.desc }}
							</view>
							<view class="name">
								<text class="label">体检机构：</text>
								<text class="des">{{ item.physicalExamOrgName }}</text>
							</view>
							<view class="name">
								<text class="label">预约状态：</text>
								<text class="des" v-if="item.reservationStatu === 0">待预约</text>
								<text class="des" v-if="item.reservationStatu === 1">待审核</text>
								<text class="des" v-if="item.reservationStatu === 2">已通过</text>
								<text class="des" v-if="item.reservationStatu === 3">已拒绝</text>
							</view>
							<view class="name">
								<text class="label">体检时间：</text>
								<text class="des">{{ item.reservationDate || '尚未预约' }}</text>
							</view>
							<view class="name">
								<text class="label">体检类型：</text>
								<text class="des" v-if="item.examType === 0">离岗</text>
								<text class="des" v-if="item.examType === 1">岗前</text>
								<text class="des" v-if="item.examType === 2">在岗</text>
							</view>
						</view>
					</view>
					<!-- xjbtend -->
				</view>
				<!-- 体检预约 -->
				<view v-else-if="currentIndex === 2">
					<tjAppointment></tjAppointment>
				</view>
				<uni-popup ref="popup" type="bottom" background-color="#fff" class="popBox">
					<view class="popHeader">选择医院</view>
					<view class="popBody">
						<scroll-view scroll-y="true">
							<view v-for="item in hospitals" :key="item._id" @click="chooseHospital(item)" class="box"
								:class="item._id === hospitalId ? 'active' : ''">
								<view class="title">{{ item.name }}</view>
								<view>联系电话：{{ (item.managers && item.managers[0] && item.managers[0].phoneNum) ?
									item.managers[0].phoneNum : '暂无' }}</view>
								<view>联系人：{{ item.corp ? item.corp : '暂无' }}</view>
							</view>
						</scroll-view>
					</view>
					<view class="popFooter">
						<view class="btn" @click="hidePop">取消</view>
						<view class="btn" @click="openDialog">确认</view>
					</view>
				</uni-popup>
				<graceDialog ref="confirmDialog" :isTitle="false" v-on:closeDialog="closeDialog" :isCloseBtn="false">
					<view class="content" slot="content">
						<text>是否确认预约{{ hospitalName }}</text>
					</view>
					<view slot="btns" class="grace-space-between">
						<text class="grace-dialog-buttons" @tap="closeDialog">取消</text>
						<text class="grace-dialog-buttons grace-blue" @tap="confirmHospital">确认</text>
					</view>
				</graceDialog>
			</view>
		</view>
	</gracePage>
</template>
<script>
import config from '@/common.js';
import moment from 'moment';
import userApi from '@/api/user.js' //导入接口
import graceSelectImg from "@/graceUI/components/graceSelectImg.vue"
import graceSelectMenu from "@/graceUI/components/graceSelectMenu.vue";
import graceDialog from '@/graceUI/components/graceDialog.vue';
import { mapGetters } from 'vuex'
import { apiServer, imgPath } from '../../../common';
const infoCardImg = require('@/static/编组@3x.png');
import tjAppointment from './tjAppointment.vue';
export default {
	data() {
		return {

			selectValue: '',
			selectHospitalData: null,
			showHospitalConfirm: false,
			selectPhysicalId: '',
			apiServer: '',
			confirmId: '',
			signatrue: '',
			showConfirm: false,
			infoCardImg: infoCardImg,
			noticeContent: '',
			physicalExaminationNotice: {},
			harmFactors: '',
			color: {
				// linear-gradient(-45deg,#E6A23C,white)
				warning: "orange",
				danger: "color-red",
				primary: "blue",
				success: "green",
				info: "gray",
			},
			currentIndex: 0,
			// 分类数据
			tabsAll: [{
				id: 0,
				name: '体检结果'
			}, {
				id: 1,
				name: '体检提醒'
			}, {
				id: 2,
				name: '体检预约'
			}],
			tabs: [],
			accordionActive: "",
			physicalResults: [],
			physicalBefore: [],
			isOss: false,
			guideId: '',
			defaultItems: [],
			accordionIndex: '',
			boolean: false,
			EnterpriseID: '',
			hospitals: [], // 可预约的医院
			hospitalId: '', // 要预约的医院
			hospitalName: '', // 要预约的医院
			medicalExamTypesOption: [
				{
					value: '1',
					label: '离岗',
				},
				{
					value: '2',
					label: '上岗前',
				},
				{
					value: '3',
					label: '在岗',
				},
				{
					value: '4',
					label: '周期体检',
				},
			],
			// xjbt
			appointList: [],
		};
	},
	watch: {
		accordionActive: function () {
			if (this.accordionActive = this.accordionIndex) {
				this.boolean = true;
			} else {
				this.boolean = false;
			}
		}
	},
	onShow() {
		this.get()
	},
	mounted() {
		this.apiServer = config.apiServer;
		this.getNotice();
		this.get();
		// xjbt
		this.getAppointList();
	},
	computed: {
		...mapGetters({
			'userInfo': 'userInfo'
		}),
		harmFactorsInfo() {
			return this.physicalResults
				.filter(item => item.harmFactors)
				.map(item => item.harmFactors)
				.join('、');
		},
		illnessInfo() {
			return this.physicalResults
				.filter(item => item.illness)
				.map(item => item.illness)
				.join('、');
		},
		enterpriseName() {
			return this.userInfo.company;
		},
	},
	components: {
		graceSelectImg,
		graceSelectMenu,
		graceDialog,
		tjAppointment
	},
	filters: {
		imgPath: (img) => imgPath(img),
	},
	methods: {
		//#region xjbt
		async getAppointList() {
			const res = await userApi.getHcAppointmentList();
			if (res.status === 200) {
				this.appointList = res.data
					.filter(item => {
						// 只显示未过期的预约记录
						if (!item.reservationDate) {
							return item.reservationStatu === 0; // 未预约的记录
						}
						return moment(item.reservationDate).isSameOrAfter(moment(), 'day');
					})
					.map(item => {
						let desc = '';
						if (!item.reservationDate) {
							desc = '请预约体检日期';
						} else if (moment(item.reservationDate).isSame(moment(), 'day')) {
							desc = '体检日期已到期，请及时前往';
						} else if (moment(item.reservationDate).isBefore(moment().add(7, 'days'), 'day')) { //体检日期在7天之内
							desc = '体检日期临近，请记得准时前往';	
						} else { // 体检日期临近
							desc = '体检已预约，请记得准时前往';
						}

						return {
							...item,
							desc,
							reservationDate: item.reservationDate && moment(item.reservationDate).format("YYYY-MM-DD")
						}
					});
			}
		},
		//#endregion

		calculateMedicalType(row) {
			const medicalInfo = row.medicalExamInfo;

			// 获取所有 medicalExamType 的标签
			const medicalType = medicalInfo.map((info) => {
				return this.medicalExamTypesOption.find(
					(option) => option.value === info.medicalExamType
				).label;
			});

			// 去重处理
			const uniqueMedicalType = [...new Set(medicalType)];

			// 返回逗号分隔的字符串
			return uniqueMedicalType.join(', ');
		},
		goToDetail(item) {
			const bhkSubList = item.bhkSubList
			let paramsString = JSON.stringify(bhkSubList);
			uni.navigateTo({
				url: `./checkDetail?bhkSubList=${encodeURIComponent(paramsString)}`,
			})
		},
		getEndDate(createdAt) {
			const createdAtDate = new Date(createdAt);
			const endDate = new Date(createdAtDate.getTime() + 5 * 24 * 60 * 60 * 1000); // 5天的毫秒数
			const currentDate = new Date();

			if (currentDate > endDate) {
				return "已过期";
			} else {
				const year = endDate.getFullYear();
				const month = (endDate.getMonth() + 1).toString().padStart(2, '0');
				const day = endDate.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			}
		},
		selectHospital(e) {
			this.selectHospitalData = e;
		},
		formattedHarmFactors(stationInfo) {
			const uniqueHarmFactors = new Set(); // 使用 Set 去重
			// 遍历 stationInfo 数组，将 harmFactors 加入 uniqueHarmFactors 集合
			stationInfo.forEach(item => {
				item.harmFactors.forEach(factor => {
					uniqueHarmFactors.add(factor);
				});
			});
			// 使用 Array.from 将 Set 转为数组，然后使用 join 方法拼接为字符串
			return Array.from(uniqueHarmFactors).join('、');
		},
		confirmSelectHospital(item) {
			if (!this.selectValue) {
				uni.showToast({
					title: '请选择医院',
					icon: 'none'
				})
				return;
			}
			this.showHospitalConfirm = true;
			this.selectPhysicalId = item._id;
		},
		showDialog2: function () { this.$refs.graceDialog2.open(); },
		closeDialog2: function () { this.showHospitalConfirm = false; },
		confirm2: async function () {
			const res = await userApi.confirmSelectHospital({
				_id: this.selectPhysicalId,
				confirmStatus: true,
				hospitalConfirm: {
					orgId: this.selectHospitalData.item.value,
					orgName: this.selectHospitalData.item.text,
				}
			});
			this.showHospitalConfirm = false;
			console.log(res, 999);
			if (res.status === 200) {
				uni.showToast({
					title: '选择医院成功',
					icon: 'success'
				})
				this.selectPhysicalId = '';
				uni.redirectTo({
					url: '/pages_user/pages/user/physicalExamination'
				})
			} else {
				uni.showToast({
					title: '选择医院失败',
					icon: 'none'
				})
			}
		},
		imgsChange: function (imgs) {
			console.log('选择的图片数据变化了');
			console.log(imgs, this.guideId);
			// 数据格式
			// { progress : 0/100, url:图片地址 }
			// progress : 100 代表图片来自默认值不需要上传
			if (imgs.length > 0) {
				uni.uploadFile({
					url: config.apiServer + 'manage/user/uploadGuideForm',
					filePath: imgs[0].url,
					name: 'iFile',
					header: {
						Authorization: uni.getStorageSync('userToken')
					},
					formData: {
						_id: this.guideId,
						idNo: this.userInfo.idNo,
						EnterpriseID: this.userInfo.companyId[0],
					},
					success: (uploadFileRes) => {
						let url = JSON.parse(uploadFileRes.data)
						console.log(`${config.apiServer}${url.data.previewPic}`, '上传的图片', this.physicalBefore);
						let index = this.physicalBefore.findIndex(item => item._id === this.guideId);
						this.physicalBefore[index].previewPic = imgPath(url.data.previewPic)
						console.log(11121312321, imgPath(url.data.previewPic))
						uni.showToast({
							title: '上传成功',
							icon: 'success'
						})
					}
				});
			}
		},
		removeImg: function (e, item) {
			console.log('被删除的图片信息id', item);
			userApi.deleteGuideForm({
				_id: item._id,
				EnterpriseID: item.EnterpriseID,
				guideFormUrl: item.guideForm.staticName,
			}).then(res => {
				console.log(res, '删除图片');
				if (res.status === 200) {
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				}
			})
		},
		selectUpload(id) {
			console.log('点击了', id);
			this.guideId = id;
			// this.uploadImage();
		},
		goToPreview(item) {
			uni.downloadFile({
				url: imgPath(item.caseCard.previewUrl),
				// filePath: wx.env.USER_DATA_PATH  +'/'+ name,//下载到自定义的文件夹里并命名,这里是一个临时的文件地址，name是你的文件名，.pdf 是文件保存的类型，这里我保存为PDF格式
				success: res => {
					console.log('url', res);
					if (res.statusCode === 200) {
						// this.saveFile(res.tempFilePath, pdfUrl);
						// 预览pdf文件
						uni.openDocument({
							filePath: res.tempFilePath,
							fileType: 'pdf',
							showMenu: true, // 右上角菜单，可以进行分享保存pdf
							success: function (file) {
								console.log('file-success', file);
							}
						});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '加载失败'
						});
					}
				},
				fail: () => {
					uni.hideLoading()
				}
			});
		},
		confirmSign(item) {
			uni.navigateTo({
				url: `./signature?_id=${item._id}&staticName=${item.caseCard.staticName}`,
			})
		},
		getctxt() {
			return '<a>a标签</a>'
		},
		getNotice() {
			// console.log(this.userInfo, 'this.userInfo====')
			// this.physicalExaminationNotice = uni.getStorageSync('physicalExaminationNotice');
			// console.log("this.physicalExaminationNotice:", this.physicalExaminationNotice)
			// if (!this.physicalExaminationNotice) return;
			// this.getHospital(); // 获取医院
			// if (this.physicalExaminationNotice.type === '将到期') {
			// 	this.noticeContent =
			// 		`<span>您所在的岗位接触到职业病危害因素为<span class="color-red">${this.physicalExaminationNotice.harmFactors}</span>，上次体检时间为<span class="color-red">${this.physicalExaminationNotice.lastCheckTime}</span>，请提前安排好工作，根据公司规定进行职业健康检查</span>`
			// } else if (this.physicalExaminationNotice.type === '新上岗') {
			// 	this.noticeContent = `<span>感谢你加入${this.userInfo.company || ''}，请您在30日内进行上岗前职业健康检查</span>`
			// } else if (this.physicalExaminationNotice.type === '转岗') {
			// 	this.noticeContent = `<span>您已经进行了岗位变动，请于30日内进行职业健康检查</span>`
			// } else if (this.physicalExaminationNotice.type === '离岗') {
			// 	this.noticeContent = `<span>你已经申请离职，感谢您为公司的发展作出的贡献，请于30日内进行离岗时职业健康检查</span>`
			// } else if (this.physicalExaminationNotice.type === '已过期') {
			// 	this.noticeContent = `<span>您的体检已过期</span>`
			// } else if (this.physicalExaminationNotice.type === '已体检') {
			// 	this.noticeContent = `<span>您已经进行了职业健康检查，请查看报告进行签名，感谢您的配合</span>`
			// }
			// 初始化新闻列表数组 元素数量与分类匹配
			for (var i = 0; i < this.tabsAll.length; i++) {
				// this.newsAll.push([]);
				this.tabs.push(this.tabsAll[i].name);
				// this.pages.push(1);
				// this.loadingTypes.push(3);
				// this.scrollTops.push(0);
			}
		},
		confirmBtn(id) {
			// if(this.signatrue){
			// 	this.showConfirm=true;
			// 	this.confirmId=id
			// }else{
			// 	uni.navigateTo({
			// 		url:'./signature?_id='+id,
			// 	})
			// }
			this.confirmId = id
			uni.navigateTo({
				url: './signature?_id=' + id,
			})

		},
		async confirmCheck() { // 确认
			let res = await userApi.confirmCheck({
				_id: this.confirmId
			})
			if (res.status === 200) {
				uni.showToast({
					title: '体检确认成功',
					icon: 'success'
				})
				this.physicalResults.filter(item => item._id === this.confirmId)[0].confirmStatus = true
			}
			this.showConfirm = false
		},
		// 导航切换
		navChange: function (e) {
			this.currentIndex = e;
		},
		showDialog1: function () {
			this.$refs.graceDialog1.open();
		},
		closeDialog1() {
			this.$refs.noticeDialog.hide();
		},

		// 获取医院
		async getHospital() {
			let res = await userApi.findAllReservation({ EnterpriseID: this.EnterpriseID, current: '1', pageSize: '50' })
			if (res.status === 200) {
				this.hospitals = res.data.docs;
			}
		},

		// 预约体检
		async postReservation(params) {
			let res = await userApi.createReservation({ EnterpriseID: this.EnterpriseID, ...params });
			if (res.status === 200) {
				uni.showToast({
					title: '体检预约成功',
					icon: 'success'
				})
			}
		},

		async get() {
			const userInfo = this.userInfo;
			if (!userInfo.companyId || userInfo.companyId.length === 0) return;
			this.EnterpriseID = Array.isArray(this.userInfo.companyId) ? this.userInfo.companyId[this.userInfo.companyId.length - 1] : this.userInfo.companyId;
			const res = await userApi.physicalExamination({
				idNo: userInfo.idNo,
				EnterpriseID: this.EnterpriseID,
			});
			for (let i = 0; i < res.data.physical.length; i++) {
				res.data.physical[i].checkDate = moment(res.data.physical[i].checkDate).format('YYYY-MM-DD');
			}
			for (let i = 0; i < res.data.physicalBefore.length; i++) {
				res.data.physicalBefore[i].createdAt = moment(res.data.physicalBefore[i].createdAt).format('YYYY-MM-DD');
				res.data.physicalBefore[i].previewPic = imgPath(res.data.physicalBefore[i].previewPic);
			}
			this.physicalResults = res.data.physical;
			this.physicalBefore = res.data.physicalBefore;
			this.signatrue = res.data.signatrue
			// this.harmFactors = this.formattedHarmFactors(res.data.physicalBefore[0].stationInfo);
		},
		changeAccordion: function (e) {
			this.accordionIndex = e.currentTarget.id;
			// console.log(accordionIndex, 'accordionIndexm');
			if (this.accordionActive == this.accordionIndex) {
				this.accordionIndex = '';
			}
			this.accordionActive = this.accordionIndex;
		},

		// 打开底部弹窗
		open() {
			this.$refs.popup.open('bottom')
		},
		// 关闭底部弹窗
		hidePop() {
			this.$refs.popup.close()
		},

		// 选择医院
		chooseHospital(row) {
			this.hospitalId = row._id;
			this.hospitalName = row.name;
		},

		// 打开确认框
		openDialog() {
			this.$refs.confirmDialog.open();
		},

		// 关闭确认对话框
		closeDialog() {
			this.$refs.confirmDialog.hide();
		},

		// 确认预约
		confirmHospital() {
			this.closeDialog();
			this.hidePop();
			const params = {
				id: this.hospitalId,
				uid: this.userInfo._id,
				phoneNum: this.userInfo.phoneNum,
				company: this.userInfo.company,
				name: this.userInfo.name
			}
			this.postReservation(params);
		},
	},
}
</script>
<style lang="scss" scoped>
/* @import '@/static/icon/notice/iconfont.css'; */
@import "@/graceUI/animate.css";

.signatrue {
	height: 10%;
	width: 10%;
	text-align: center;
}

/* .signatrueImg{
		width: 100%;
		height: 100%;
		object-fit: cover;
	} */
uni-button[size=mini] {
	margin: 0;
	line-height: 2;
	padding: 0 .8em;
}

.infoCardBg {
	background-size: 100% 100%;
	box-shadow: 0px 0px 0px #fff;
}

.cardTitleText::before {
	content: "";
	display: inline-block;
	height: 55rpx;
	width: 15rpx;
	border-radius: 10rpx;
	margin-right: 16px;
	vertical-align: middle;
}

.cardTitleText.confirmed::before {
	background-color: #3688FF;
}

.cardTitleText.unconfirmed::before {
	background-color: red;
}

.nameStyle {
	width: 51px;
	height: 20px;
	font-size: 20px;
	font-family: PingFang SC, PingFang SC-Semibold;
	font-weight: 600;
	text-align: left;
	color: #fff;
	line-height: 20px;
	margin-bottom: 15px;
}

.sexStyle {
	width: 39px;
	height: 14px;
	color: #fff;
	font-size: 14px;
	line-height: 14px;
	margin-left: 16px;
}

.personInfo {
	height: 14px;
	font-size: 14px;
	font-weight: 300;
	text-align: left;
	color: #000;
	line-height: 14px;
	margin-top: 15px;
}

.cellItem {
	display: flex;
	align-items: center;
	padding: 7px;
}

.cellItem .label {
	font-size: 14px;
	margin-right: 24px;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.cellItem .value {
	font-size: 14px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.infoCard {
	margin-left: 15px;
}

.abnormal {
	color: #FE930C;
}

.btnStyle {
	text-align: right;
}

.margin {
	margin: 10rpx 10rpx 0 10rpx;
}

.titleIcon {
	width: 37px;
	height: 37px;
}

.cardTitleText {
	display: flex;
	align-items: center;
}

/* .grace-body {
		background-color: #EEEEEE;
	} */

.noticeMain {
	margin-top: 10px;
	margin-bottom: 10px;
}

.card {
	border-radius: 10px;
	padding: 25px 10px;
	margin-bottom: 20px;
}

.contentText {
	font-size: 13px;
}

.cardTitle {
	/* padding-bottom: 10px; */
	/* border-bottom: 1px dotted #ccc; */
	margin-bottom: 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.cardContent {
	text-indent: 2em;
	font-size: 14px;
	line-height: 30px;
	color: #929292;
}

.noticeTitile {
	text-align: center;
	font-size: 18px;
	margin-bottom: 10px;
}

.noticeText {
	padding: 7px;
	font-size: 14px;
	color: #929292;
	line-height: 27px;
	text-indent: 2em;
}

.noticePerson {
	font-size: 15px;
}

.noticeContent {
	margin-top: 64px;
	margin-bottom: 20px;

}

.notice {
	position: absolute;
	top: -84px;
	transform: skewY(19deg);
}

.grace-accordion-show {
	height: auto;
	animation: fadeIn 300ms linear;
}

.grace-accordion-hide {
	height: 0;
	animation: fadeOut 300ms linear;
}

.grace-accordion-title {
	color: #323232;
	background: #F8F8F8;
}

.grace-accordion-title .grace-icons:before {
	margin-right: 15rpx;
	font-size: 36rpx !important;
}

.noData {
	color: gray;
	width: 100%;
	margin-top: 330rpx;
	text-align: center;
}

.order {
	width: 80%;
	margin: 10px auto;
	text-align: center;
	border-radius: 6px;
	line-height: 32px !important;
	background-color: #517EED;
	color: #fff;
}

.grace-list {
	margin: 0 20px;
}

.popBox {
	position: relative;
	height: 75vh;
}

.popHeader {
	height: 7vh;
	line-height: 7vh;
	width: 100%;
	position: fixed;
	top: -10px;
	background-color: #517EED;
	color: #ffffff;
	font-size: 18px;
	font-weight: 700;
	text-align: center;
	border-radius: 10px 10px 0 0;
}

.popBody {
	height: 60vh;
	margin-top: 7vh;
	overflow: auto;
}

.box {
	width: 90%;
	height: 5rem;
	padding: .5rem;
	background-color: #F2F2F2;
	color: #323232;
	margin: 10px auto;
	font-size: .9rem;
	line-height: 1.6rem;
	border-radius: 10px;
}

.box .title {
	font-weight: 700;
	font-size: 1rem;
}

.active {
	background-color: #517EED;
	color: #ffffff;
}

.popFooter {
	height: 6vh;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn {
	width: 5rem;
	height: 2rem;
	line-height: 2rem;
	text-align: center;
	border-radius: 4px;
}

.btn:nth-child(1) {
	color: #999;
	border: 1px solid #999;
	margin-right: 2rem;
}

.btn:nth-child(2) {
	color: #fff;
	background-color: #3D91F7;
	border: 1px solid #3D91F7;
}

.content {
	padding: 1.5rem;
	line-height: 1.5rem;
	font-size: 1rem;
}

::v-deep .grace-add-list-btn-text {
	display: none;
}

.content2 {
	text-align: center;
}

.nameColor {
	color: #fff;
}

.empty {
	text-align: center;
	color: #999;
	font-size: 14px;
	margin-top: 20px;
}

.card-section {
	.card {
		width: 100%;
		border-radius: 5px;
		background: #FFFFFF;
		margin-top: 12px;
		padding: 14px;
		box-sizing: border-box;
		box-shadow: 2px 2px 10px 2px rgba(0, 0, 0, 0.1);

		.title {
			font-family: PingFangSC;
			font-size: 14px;
			color: #555555;
			display: flex;
			align-items: center;
			margin-left: -14px;
			margin-bottom: 15px;

			text {
				display: inline-block;
				width: 6px;
				height: 20px;
				border-radius: 3px 3px 0px 3px;
				background: #FE3E3E;
				margin-right: 12px;
			}
		}

		.name {
			font-size: 14px;
			margin-bottom: 6px;

			.label {
				margin-right: 24px;
				color: #000;
			}

			.des {
				color: #555555;
			}
		}

		.operaction {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			margin-top: 24px;

			view {
				text-align: center;
				line-height: 33px;
				width: 81px;
				height: 33px;
				border-radius: 3px;
			}

			.cancel-btn {
				color: #3E73FE;
				border: 1px solid #3E73FE;
				text-align: center;
				line-height: 33px;
			}

			.edit-btn {
				background: #3E73FE;
				border: 1px solid #3E73FE;
				color: #fff;
				margin-left: 12px;
			}
		}
	}
}
</style>
