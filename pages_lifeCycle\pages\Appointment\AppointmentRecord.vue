<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="预约记录" />
    <view class="grace-body container" slot="gBody">
      <view class="search">
        <view class="searchInfo" @click="openSearchPopup">
          <uni-icons type="search" size="20" color="dodgerblue"></uni-icons>
          <text>查询</text>
        </view>
      </view>
      <view class="search_content">
        <view
          class="records"
          v-for="(item, index) in appointmentRecords"
          :key="item.id"
        >
          <view class="records_left">
            <view class="text">
              <view class="label"> 预约时间: </view>
              <view class="content"> {{ item.apptDate }} </view>
            </view>
            <view class="text">
              <view class="label"> 机构名称: </view>
              <u-tooltip 
                :text="item.stationName" 
                direction="bottom"
                :showCopy="false"
              >
                <view class="content institution">{{ item.stationName }}</view>
              </u-tooltip>
            </view>
            <view class="text">
              <view class="label"> 诊疗医师: </view>
              <view class="content"> {{ item.dockerName }} </view>
            </view>
          </view>
          <view class="right">
            <view class="tag tag_green">
              {{statusMap[item.status] || item.status}}
            </view>
          </view>
        </view>
      </view>

      <uni-popup
        ref="popup"
        background-color="#fff"
        @change="change"
        style="z-index: 90"
        mask-background-color="#0000000"
      >
        <view class="popup-content">
          <view class="forms">
            <view class="forms_item">
              <view class="label"> 预约诊疗时间 </view>
              <uni-datetime-picker
                v-model="formDate.range"
                type="daterange"
                rangeSeparator="至"
              />
            </view>
            <view class="forms_item">
              <view class="label"> 预约诊疗机构 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.mechanism"
                placeholder="请输入预约诊疗机构"
              ></uni-easyinput>
            </view>
            <view class="forms_item">
              <view class="label"> 预约诊疗医师 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.physician"
                placeholder="请输入预约诊疗医师"
              ></uni-easyinput>
            </view>
            <view class="forms_btn">
              <button
                type="default"
                plain="true"
                class="reset_btn"
                @click="reset"
              >
                重置
              </button>
              <button type="primary" @click="search" class="search_btn">
                查询
              </button>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </gracePage>
</template>

<script>
import stationApi from '@/api/rehabStation.js'
export default {
  name: "followUp",

  data() {
    return {
      pageParams: {
        pageNum: 1,
        pageSize: 9999,
      },
      formDate: {
        range: "",
        mechanism: "",
        physician: "",
      },
      statusMap: {
        'booked': '已预约',
        'completed': '已就诊',
        'canceled': '已取消'
      },
      appointmentRecords: []
    };
  },
  onLoad() {
    this.getRecordsList()
  },
  methods: {
    async getRecordsList() {
      try {
        // 构建查询参数
        const params = {
          ...this.pageParams,
          doctorName: this.formDate.physician || undefined,
          stationName: this.formDate.mechanism || undefined,
          appointStartTime: this.formDate.range?.[0] || undefined,
          appointEndTime: this.formDate.range?.[1] || undefined
        }
        
        const data = await stationApi.appointment(params)
        if (data.status === 200) {
          this.appointmentRecords = data.data.list
        } 
      } catch (error) {
        console.error('获取预约记录列表失败:', error)
        uni.showToast({
          title: '获取预约记录失败',
          icon: 'none'
        })
      }
    },
    change(e) {
      console.log(e);
    },
    reset() {
      this.formDate = {
        range: "",
        mechanism: "",
        physician: "",
      };
    },
    search() {
      this.getRecordsList();
      this.$refs.popup.close();
    },
    openSearchPopup() {
      this.$refs.popup.open("right");
    },
  },
};
</script>

<style scoped lang="less">
.tag {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  white-space: nowrap;
}
.tag_green {
  background-color: #21bd9f30;

  color: #21bd9f;
  border: 1px solid #21bd9f;
}
.tag_black {
  background-color: #5b5b5b30;

  color: #5b5b5b;
  border: 1px solid #5b5b5b;
}
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
}

.grace-body .search {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: column;
  color: #169bd5;
  margin-bottom: 30rpx;
}

.grace-body .search .searchInfo {
  display: flex;
  align-items: center;
}

.records {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  .records_left {
    flex: 1;
    margin-right: 30rpx;
    max-width: 75%;
  }
  .right {
    min-width: 100rpx;
    display: flex;
    justify-content: flex-end;
  }
}

.records .text {
  display: flex;
  margin-bottom: 6rpx;
  align-items: center;
}

.records .text .label {
  font-size: 28rpx;
  margin-right: 10rpx;
  white-space: nowrap;
  min-width: 120rpx;
}

.records .text .content {
  font-size: 28rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 60%;
}

.records .text .content.institution {
  color: #169bd5;
  position: relative;
  padding-right: 30rpx;
}

.popup-content {
  position: relative;
  width: 70vw;
  height: 88vh;
  padding: 40rpx;
  padding-top: 120rpx;
}

.forms_item {
  margin-bottom: 20rpx;
}

.forms_item .label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.forms_item input {
  border: 1px solid #f6f6f6;
  border-radius: 5rpx;
}

.forms_btn {
  position: absolute;
  bottom: 5%;
  width: 100%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.forms_btn button {
  margin: 0;
  font-size: 28rpx;
  padding: 0 80rpx;
}
.reset_btn {
  background-color: #5b5b5b;
}
.search_btn {
  background-color: #169bd5;
}
</style>