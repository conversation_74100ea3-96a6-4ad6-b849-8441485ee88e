const getters = {
  hasLogin: (state) => state.user.hasLogin,
  userInfo: (state) => state.user.userInfo,
  isManager: (state) => state.user.isManager,
  weixinUserInfo: (state) => state.user.weixinUserInfo,
  navbar: (state) => state.user.navbar,
  userPower: (state) => state.user.userPower,
  trainingPower: (state) => state.user.trainingPower,
  notices: (state) => state.user.notices,
  messages: (state) => state.user.messages,
	homeNav: (state) =>state.user.homeNav,

  courseID: (state) => state.training.courseID,
  personalTrainingId: (state) => state.training.personalTrainingId,
  document: (state) => state.training.document,
  curTest: (state) => state.training.curTest,

  windowHeight: (state) => state.user.windowHeight,
  // jhw
  aboutTransfer: (state) => state.user.aboutTransfer,
  path: (state) => state.user.path,
  // 当前要浏览的文章
  curArticle: (state) => state.training.curArticle,
  // 文章类别的id
  applicationArr: (state) => state.user.applicationArr,
  categories: (state) => state.training.categories,
  searchResBack: (state) => state.user.searchResBack,
  diseasesOverhaulObj: (state) => state.diseasesOverhaul.diseasesOverhaulObj,
  // 体检预约信息
  tjPlanInfo: (state) => state.user.tjPlanInfo,
  appointmentInfo: (state) => state.user.appointmentInfo,
  appointmentDetailInfo: (state) => state.user.appointmentDetailInfo,
  optionalItemsSelected: (state) => state.user.optionalItemsSelected,
  optionalItems: (state) => state.user.optionalItems,
  // 职业病鉴定、诊断数据字典
  areaList:(state)=>state.diagnosis.areaList,
  diseasesList:(state)=>state.diagnosis.diseasesList,
  personGender:(state)=>state.diagnosis.personGender,
  idcardType:(state)=>state.diagnosis.idcardType,
  hazard:(state)=>state.diagnosis.hazard,
  diagnosisStatus:(state)=>state.diagnosis.diagnosisStatus,
  identifyStatus:(state)=>state.diagnosis.identifyStatus,
};
export default getters;
