<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="其他医疗卫生服务记录" />
    <view slot="gBody" class="grace-body container">
      <!-- 转诊记录 -->
      <view class="section" style="margin-top: 24rpx;">
        <view class="section-header" @tap="toggleSection('referral')">
          <text class="section-title">转诊记录 ({{ referralRecords.length }})</text>
          <text class="grace-grids-icon grace-icons" :class="referralExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
        </view>
        <view class="record-list" v-show="referralExpanded">
          <view v-if="referralRecords && referralRecords.length > 0">
            <view v-for="(item, index) in referralRecords" :key="index" class="record-card">
              <view class="card-header">
                <!-- <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons> -->
                <text class="time">{{ item.time }}</text>
              </view>
              <view class="hospital-info">
                <view class="from-hospital">
                  <text class="label">转出医院：</text>
                  <text>{{ item.fromHospital }}</text>
                </view>
                <view class="to-hospital">
                  <text class="label">转入医院：</text>
                  <text>{{ item.toHospital }}</text>
                </view>
              </view>
              <view v-show="item.isExpanded">
                <view class="detail-item">
                  <text class="label">转诊原因：</text>
                  <text>{{ item.reason }}</text>
                </view>
                <view class="detail-item">
                  <text class="label">病情变化及处理措施：</text>
                  <text>{{ item.treatment }}</text>
                </view>
              </view>
              <view class="show-more" @tap="toggleRecord(index, 'referral')">
                <text>{{ item.isExpanded ? '收起' : '查看更多' }}</text>
                <text class="grace-grids-icon grace-icons" :class="item.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
                  style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无转诊记录</text>
          </view>
        </view>
      </view>
      <!-- 会诊记录 -->
      <view class="section">
        <view class="section-header" @tap="toggleSection('consultation')">
          <text class="section-title">会诊记录 ({{ consultationRecords.length }})</text>
          <!-- <uni-icons :type="consultationExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
          <text class="grace-grids-icon grace-icons" :class="consultationExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
        </view>
        <view class="record-list" v-show="consultationExpanded">
          <view v-if="consultationRecords && consultationRecords.length > 0">
            <view v-for="(item, index) in consultationRecords" :key="index" class="record-card">
              <view class="card-header">
                <!-- <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons> -->
                <text class="time">{{ item.time }}</text>
              </view>
              <view class="consultation-info">
                <text class="department">{{ item.department }}</text>
                <view class="doctors">
                  <text class="label">参与医生：</text>
                  <text>{{ item.doctors.join('、') }}</text>
                </view>
              </view>
              <view v-show="item.isExpanded">
                <view class="detail-item">
                  <text class="label">会诊意见：</text>
                  <text>{{ item.opinion }}</text>
                </view>
                <view class="detail-item">
                  <text class="label">后续处理措施：</text>
                  <text>{{ item.followUp }}</text>
                </view>
              </view>
              <view class="show-more" @tap="toggleRecord(index, 'consultation')">
                <text>{{ item.isExpanded ? '收起' : '查看更多' }}</text>
                <!-- <uni-icons :type="item.isExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
                <text class="grace-grids-icon grace-icons" :class="item.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
                  style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无会诊记录</text>
          </view>
        </view>
      </view>

      <!-- 医学报告记录 -->
      <view class="section">
        <view class="section-header" @tap="toggleSection('medicalReport')">
          <text class="section-title">医学报告记录 ({{ medicalReports.length }})</text>
          <!-- <uni-icons :type="medicalReportExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
          <text class="grace-grids-icon grace-icons"
            :class="medicalReportExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
        </view>
        <view class="record-list" v-show="medicalReportExpanded">
          <view v-if="medicalReports && medicalReports.length > 0">
            <view v-for="(item, index) in medicalReports" :key="index" class="record-card">
              <view class="card-header">
                <!-- <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons> -->
                <text class="time">{{ item.time }}</text>
              </view>
              <view class="report-info">
                <text class="report-type">{{ item.type }}</text>
                <text class="department">{{ item.department }}</text>
              </view>
              <view v-show="item.isExpanded">
                <view class="detail-item">
                  <text class="label">检查结果：</text>
                  <text>{{ item.result }}</text>
                </view>
                <view class="detail-item">
                  <text class="label">医生建议：</text>
                  <text>{{ item.suggestion }}</text>
                </view>
              </view>
              <view class="show-more" @tap="toggleRecord(index, 'medicalReport')">
                <text>{{ item.isExpanded ? '收起' : '查看更多' }}</text>
                <!-- <uni-icons :type="item.isExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
                <text class="grace-grids-icon grace-icons" :class="item.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
                  style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无医学报告记录</text>
          </view>
        </view>
      </view>
      <!-- 公共卫生记录 -->
      <view class="section">
        <view class="section-header" @tap="toggleSection('publicHealth')">
          <text class="section-title">公共卫生记录 ({{ publicHealthRecords.length }})</text>
          <!-- <uni-icons :type="publicHealthExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
          <text class="grace-grids-icon grace-icons" :class="publicHealthExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
        </view>
        <view class="record-list" v-show="publicHealthExpanded">
          <view v-if="publicHealthRecords && publicHealthRecords.length > 0">
            <view v-for="(item, index) in publicHealthRecords" :key="index" class="record-card">
              <view class="card-header">
                <!-- <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons> -->
                <text class="time">{{ item.time }}</text>
              </view>
              <view class="health-info">
                <text class="service-type">{{ item.serviceType }}</text>
              </view>
              <view v-show="item.isExpanded">
                <view class="detail-item">
                  <text class="label">随访内容：</text>
                  <text>{{ item.content }}</text>
                </view>
                <view class="detail-item">
                  <text class="label">健康指导：</text>
                  <text>{{ item.guidance }}</text>
                </view>
              </view>
              <view class="show-more" @tap="toggleRecord(index, 'publicHealth')">
                <text>{{ item.isExpanded ? '收起' : '查看更多' }}</text>
                <text class="grace-grids-icon grace-icons" :class="item.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
                  style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无公共卫生记录</text>
          </view>
        </view>
      </view>
      <!-- 中医药服务记录 -->
      <view class="section">
        <view class="section-header" @tap="toggleSection('tcm')">
          <text class="section-title">中医药服务记录 ({{ tcmRecords.length }})</text>
          <!-- <uni-icons :type="tcmExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
          <text class="grace-grids-icon grace-icons" :class="tcmExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
        </view>
        <view class="record-list" v-show="tcmExpanded">
          <view v-if="tcmRecords && tcmRecords.length > 0">
            <view v-for="(item, index) in tcmRecords" :key="index" class="record-card">
              <view class="card-header">
                <!-- <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons> -->
                <text class="time">{{ item.time }}</text>
              </view>
              <view class="tcm-info">
                <text class="service-type">{{ item.serviceType }}</text>
              </view>
              <view v-show="item.isExpanded">
                <view class="detail-item">
                  <text class="label">诊断结果：</text>
                  <text>{{ item.diagnosis }}</text>
                </view>
                <view class="detail-item">
                  <text class="label">治疗方案：</text>
                  <text>{{ item.treatment }}</text>
                </view>
              </view>
              <view class="show-more" @tap="toggleRecord(index, 'tcm')">
                <text>{{ item.isExpanded ? '收起' : '查看更多' }}</text>
                <!-- <uni-icons :type="item.isExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
                <text class="grace-grids-icon grace-icons" :class="item.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
                  style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无中医药服务记录</text>
          </view>
        </view>
      </view>
      <!-- 化学中毒救治记录 -->
      <view class="section">
        <view class="section-header" @tap="toggleSection('poisoning')">
          <text class="section-title">化学中毒救治记录 ({{ poisoningRecords.length }})</text>
          <!-- <uni-icons :type="poisoningExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
          <text class="grace-grids-icon grace-icons" :class="poisoningExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
        </view>
        <view class="record-list" v-show="poisoningExpanded">
          <view v-if="poisoningRecords && poisoningRecords.length > 0">
            <view v-for="(item, index) in poisoningRecords" :key="index" class="record-card">
              <view class="card-header">
                <!-- <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons> -->
                <text class="time">{{ item.time }}</text>
              </view>
              <view class="poisoning-info">
                <text class="poisoning-type">{{ item.poisonType }}</text>
              </view>
              <view v-show="item.isExpanded">
                <view class="detail-item">
                  <text class="label">治疗措施：</text>
                  <text>{{ item.measures }}</text>
                </view>
                <view class="detail-item">
                  <text class="label">转归情况：</text>
                  <text>{{ item.outcome }}</text>
                </view>
              </view>
              <view class="show-more" @tap="toggleRecord(index, 'poisoning')">
                <text>{{ item.isExpanded ? '收起' : '查看更多' }}</text>
                <!-- <uni-icons :type="item.isExpanded ? 'top' : 'bottom'" size="16" color="#666666"></uni-icons> -->
                <text class="grace-grids-icon grace-icons" :class="item.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
                  style="font-size: 1em;line-height: 1em;width: 1em;height: 1em;"></text>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无化学中毒救治记录</text>
          </view>
        </view>
      </view>
    </view>
  </gracePage>
</template>
<script>
export default {
  data() {
    return {
      referralExpanded: false,
      consultationExpanded: false,
      medicalReportExpanded: false,
      publicHealthExpanded: false,
      tcmExpanded: false,
      poisoningExpanded: false,
      referralRecords: [],
      consultationRecords: [],
      medicalReports: [],
      publicHealthRecords: [],
      tcmRecords: [],
      poisoningRecords: []
    }
  },
  methods: {
    toggleSection(section) {
      if (section === 'referral') {
        this.referralExpanded = !this.referralExpanded
      } else if (section === 'consultation') {
        this.consultationExpanded = !this.consultationExpanded
      } else if (section === 'medicalReport') {
        this.medicalReportExpanded = !this.medicalReportExpanded
      } else if (section === 'publicHealth') {
        this.publicHealthExpanded = !this.publicHealthExpanded
      } else if (section === 'tcm') {
        this.tcmExpanded = !this.tcmExpanded
      } else if (section === 'poisoning') {
        this.poisoningExpanded = !this.poisoningExpanded
      }
    },
    toggleRecord(index, type) {
      if (type === 'referral') {
        this.referralRecords[index].isExpanded = !this.referralRecords[index].isExpanded
      } else if (type === 'consultation') {
        this.consultationRecords[index].isExpanded = !this.consultationRecords[index].isExpanded
      } else if (type === 'medicalReport') {
        this.medicalReports[index].isExpanded = !this.medicalReports[index].isExpanded
      } else if (type === 'publicHealth') {
        this.publicHealthRecords[index].isExpanded = !this.publicHealthRecords[index].isExpanded
      } else if (type === 'tcm') {
        this.tcmRecords[index].isExpanded = !this.tcmRecords[index].isExpanded
      } else if (type === 'poisoning') {
        this.poisoningRecords[index].isExpanded = !this.poisoningRecords[index].isExpanded
      }
    },
    toggleDetail(index, type) {
      if (type === 'reason' || type === 'treatment') {
        const record = this.referralRecords[index]
        if (type === 'reason') {
          record.reasonExpanded = !record.reasonExpanded
        } else {
          record.treatmentExpanded = !record.treatmentExpanded
        }
      } else if (type === 'opinion' || type === 'followUp') {
        const record = this.consultationRecords[index]
        if (type === 'opinion') {
          record.opinionExpanded = !record.opinionExpanded
        } else {
          record.followUpExpanded = !record.followUpExpanded
        }
      }
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.header {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.content {
  flex: 1;
  overflow: auto;
}

.section {
  margin-bottom: 20rpx;
  background-color: #ffffff;
}

.section-header {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  border-bottom: 1px solid #eee;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.record-list {
  padding: 20rpx;
}

.record-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.time {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #2979ff;
}

.hospital-info {
  margin-bottom: 20rpx;
}

.from-hospital,
.to-hospital {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.label {
  color: #666666;
}

.consultation-info,
.report-info,
.health-info,
.tcm-info,
.poisoning-info {
  margin-bottom: 20rpx;
}

.report-type,
.service-type,
.poisoning-type {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.department {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.doctors {
  font-size: 28rpx;
  color: #333333;
}

.show-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: #2979ff;
  font-size: 28rpx;
}

.detail-item {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.loading,
.no-more {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 24rpx;
}

.loading text {
  margin-left: 12rpx;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.empty-text {
  color: #909399;
  font-size: 28rpx;
  text-align: center;
}
</style>