<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="诊断记录" />
		<view class="grace-body container" slot="gBody">
			<view class="diagnosis-result">
				<view class="search-wrap">
					<u--input style="width: 100%;" v-model="selectedStatusText" placeholder="请选择受理状态"
						@focus="showStatusPicker = true">
					</u--input>
					<u-picker :show="showStatusPicker" :columns="[diagnosisStatus]" @confirm="onStatusConfirm"
						@cancel="showStatusPicker = false"></u-picker>
				</view>
				<view class="diagnosis-list">
					<u-empty v-if="diagnosisList.length == 0" mode="data" icon="/static/empty.png" text="暂无诊断记录">
					</u-empty>
					<u-list>
						<u-list-item v-for="(item, index) in diagnosisList" :key="index">
							<view class="diagnosis-item">
								<view class="item-header">
									<text class="time">申请时间：{{formatDate(item.applicationDate)}}</text>
									<u-tag v-for="el in getTags(item)" :key="el.value" :type="el.type"
										:text="el.text"></u-tag>
								</view>
								<view class="item-content">
									<view class="info-row">
										<text class="label">劳动者姓名：</text>
										<text class="value">{{item.workerName}}</text>
									</view>
									<view class="info-row">
										<text class="label">用人单位名称：</text>
										<text class="value">{{item.empName}}</text>
									</view>
									<view class="info-row">
										<text class="label">用工单位名称：</text>
										<text class="value">{{item.workEmpName}}</text>
									</view>
									<!-- <view class="info-row">
								<text class="label">鉴定机构：</text>
								<text class="value">{{item.institution}}</text>
							</view>
							<view class="info-row">
								<text class="label">申请病种：</text>
								<text class="value">{{item.diseaseType}}</text>
							</view> -->
									<!-- <view class="info-row" v-if="item.status == '-1'">
								<text class="label">不受理原因：</text>
								<text class="value">{{item.rejectedReason}}</text>
							</view> -->
								</view>
								<view class="item-footer">
									<u-button v-if="item.status == '0'" type="primary" size="mini"
										@click="editApplication(item)">
										修改申请
									</u-button>
									<u-button v-if="item.status == '0'" type="warning" size="mini"
										@click="uploadMaterials(item)">
										补充材料
									</u-button>
									<u-button v-if="item.status == '99'" type="success" size="mini"
										@click="downloadReport(item)">
										下载诊断报告
									</u-button>
									<u-button v-if="item.status == '99'" type="primary" size="mini"
										@click="firstIdentify(item)">
										首次鉴定
									</u-button>
								</view>
							</view>
						</u-list-item>
					</u-list>
				</view>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import diagnosisApi from '@/api/diagnosis'
	import config from '@/common.js'
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import moment from 'moment'
	export default {
		data() {
			return {
				diagnosisList: [],
				postData: {
					status: '',
					idCardType: '',
					idCardCode: '',
					pageNum: 1,
					pageSize: 100,
				},
				total: 0,
				showStatusPicker: false,
				selectedStatusText: '',
			}
		},
		async created() {
			await this.getDiagnosisStatus()
		},
		onShow() {
			this.postData.idCardCode = this.userInfo?.idNo || ''
			this.fetchDiagnosisList();
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			});
		},
		mounted() {
			this.postData.idCardCode = this.userInfo?.idNo || ''
			this.fetchDiagnosisList()
		},
		computed: {
			...mapGetters(["userInfo", 'diagnosisStatus']),
		},
		methods: {
			...mapActions(['getDiagnosisStatus']),
			formatDate(date) {
				return moment(date).format('YYYY-MM-DD')
			},
			onStatusConfirm(e) {
				this.selectedStatusText = e.value[0].text
				this.postData.status = e.value[0].value
				this.showStatusPicker = false
				this.diagnosisList = []
				this.fetchDiagnosisList()
			},
			async fetchDiagnosisList() {
				const res = await diagnosisApi.getDiagnosisInfo(this.postData)
				this.diagnosisList = res.data.list
				this.postData.pageNum = res.data.pageNum
				this.postData.pageSize = res.data.pageSize
				this.total = res.data.total
			},
			getTags(row) {
				let diagnosisStatus = this.diagnosisStatus.filter(item => item.type)
				return diagnosisStatus.filter((item) => item.value == row.status);
			},
			async downloadReport(item) {
				// 下载报告
				try {
					uni.showLoading({
						title: '下载中...'
					});
					const res = await diagnosisApi.downloadDiagnosisFile({
						diagnosisId: item.id
					});
					if (res.data.success && res.data.data[0].fileUrl) {
						uni.hideLoading();
						const fileUrl = res.data.data[0].fileUrl;
						window.open(fileUrl, '_blank');
					} else {
						uni.hideLoading();
						uni.showToast({
							title: res.data.message || '获取文件失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					uni.showToast({
						title: error.message || '网络错误',
						icon: 'none'
					});
				}
			},
			editApplication(item) {
				// 修改申请
				uni.navigateTo({
					url: `/pages/institution/jgForm?userId=${item.id}`
				})
			},
			uploadMaterials(item) {
				// 补充材料
				uni.navigateTo({
					url: `/pages/institution/addInformation?id=${item.id}&type=zd`
				})
			},
			firstIdentify(item) {
				// 首次鉴定
				uni.navigateTo({
					url: `/pages_user/pages/identify/apply?diagnosisId=${item.id}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.diagnosis-result {
		// padding: 0 30rpx;

		.nav-left {
			display: flex;
			align-items: center;
			color: #fff;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}

		.diagnosis-list {
			margin-top: 20rpx;

			.diagnosis-item {
				background: #fff;
				border-radius: 12rpx;
				padding: 20rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;

					.time {
						color: #666;
						font-size: 28rpx;
					}

					.status {
						font-size: 24rpx;
						padding: 4rpx 16rpx;
						border-radius: 20rpx;

						&.status-success {
							background: #e1f3d8;
							color: #67c23a;
						}

						&.status-warning {
							background: #fdf6ec;
							color: #e6a23c;
						}

						&.status-info {
							background: #ecf5ff;
							color: #409eff;
						}

						&.status-danger {
							background: #fef0f0;
							color: #f56c6c;
						}
					}
				}

				.item-content {
					.info-row {
						display: flex;
						margin-bottom: 10rpx;

						.label {
							color: #666;
							font-size: 28rpx;
							width: 198rpx;
						}

						.value {
							color: #333;
							font-size: 28rpx;
							flex: 1;
						}
					}
				}

				.item-footer {
					margin-top: 20rpx;
					display: flex;
					justify-content: flex-end;
					gap: 20rpx;
				}
			}
		}
	}

	.search-wrap {
		margin-top: 10px;
		height: 70rpx;
		border-radius: 30rpx;
		background: #FFFFFF;
		border: 1px solid #ccc;
	}
</style>