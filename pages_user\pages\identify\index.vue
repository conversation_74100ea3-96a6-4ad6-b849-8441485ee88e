<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="鉴定管理" />
		<view slot="gBody" class="grace-body content">
			<view class="institution">
				<view class="content">
					<view class="content_main">
						<!-- 职业病鉴定申请部分 -->
						<view class="content_maina">
							<view class="content_text">职业病鉴定管理</view>
							<block v-for="(item, index) in diseaseDiagnosisData" :key="index">
								<view class="content_list">
									<view class="right-content">
										<view class="top_img">
											<image class="content_img" :src="item.image" alt="" />
										</view>
										<view class="top">
											<view class="top_title">{{ item.title }}</view>
											<view class="top_list">{{ item.description }}</view>
										</view>
									</view>
									<view class="top_btn">
										<text class="btn" @click="goToPage(item)">{{ item.buttonText }}</text>
									</view>
								</view>
							</block>
						</view>
					</view>
				</view>

			</view>

		</view>
	</gracePage>
</template>

<script>
	export default {
		data() {
			return {
				// 职业病鉴定申请相关的模拟数据
				diseaseDiagnosisData: [{
						image: "../../../static/apartment.png",
						title: "鉴定机构",
						description: "劳动者可选择辖区内的鉴定机构进行职业病鉴定申请",
						buttonText: "申请",
						targetPage: "/pages_user/pages/identify/apply"
					},
					{
						image: "../../../static/memo.png",
						title: "申请记录",
						description: "劳动者可查看历次职业病鉴定申请记录",
						buttonText: "查看",
						targetPage: "/pages_user/pages/identify/jdResult"
					}
				]
			}
		},
		methods: {

			goToPage(item) {
				uni.navigateTo({
					url: `${item.targetPage}`
				})
			},
		},
		onLoad() {}
	}
</script>
<style lang="scss">
	.institution {}

	.nav-left {
		display: flex;
		align-items: center;
		width: auto;
		color: #fff;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.content {
		box-sizing: border-box;
		width: 100%;
		height: 100vh;
		background-color: #f6f6f6;
		display: flex;
		flex-direction: column;
		align-items: center;
		// place-items: center;
		// padding: 10rpx;

		.content_main {
			width: 690rpx;
			height: 100%;

			// margin:  auto;
			.content_maina {
				.content_text {
					margin-top: 15rpx;
					color: #3D3D3D;
					font-weight: bold;
				}

				.content_list {
					width: 100%;
					box-sizing: border-box;
					height: 160rpx;
					background: #fff;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 15rpx;
					padding: 0 24rpx;

					.right-content {
						display: flex;
						align-items: center;

						.content_img {
							width: 112rpx;
							height: 112rpx;
						}

						.top {
							margin-left: 24rpx;

							.top_title {
								font-weight: bolder;
								font-size: 32rpx;
								margin-bottom: 12rpx;
								color: #000000;
							}

							.top_list {
								color: #666666;
								font-size: 24rpx;
							}
						}
					}

					.top_btn {
						display: flex;
						margin-left: 12px;

						.btn {
							width: 50px;
							height: 28px;
							line-height: 28px;
							text-align: center;
							font-size: 14px;
							background: #F0F9EB;
							border-radius: 20px;
							flex-shrink: 0;
							border: 2rpx solid #B3E09C;
							color: #67C23A;
						}
					}
				}

				&:nth-child(2) {
					margin-top: 15px;
				}
			}
		}
	}
</style>