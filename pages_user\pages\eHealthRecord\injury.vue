<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="工伤保险" />
    <view slot="gBody" class="grace-body container">
      <!-- 顶部标签页 -->
      <view class="tabs">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: currentTab === index }"
          @tap="switchTab(index)"
        >
          <text>{{ tab }}</text>
        </view>
      </view>
      <!-- 内容区域 -->
      <scroll-view class="content-area" scroll-y>
        <!-- 诊断记录 -->
        <view v-if="currentTab === 0" class="record-list">
          <view v-if="injuryRecords && injuryRecords.length > 0">
            <view
              v-for="(item, index) in injuryRecords"
              :key="index"
              class="record-card"
            >
              <view class="time-line">
                <view class="date-box">
                  <text class="date">{{ item.date }}</text>
                </view>
                <view class="line"></view>
              </view>
              <view class="card-content">
                <view class="type-tag">{{ item.status }}</view>
                <view class="info-item">
                  <text class="label">事故时间：</text>
                  <text class="value">{{ item.accidentTime }}</text>
                </view>
                <view class="info-item">
                  <text class="label">事故地点：</text>
                  <text class="value">{{ item.location }}</text>
                </view>
                <view class="info-item">
                  <text class="label">事故原因：</text>
                  <text class="value">{{ item.cause }}</text>
                </view>
                <view class="info-item">
                  <text class="label">事故类型：</text>
                  <text class="value">{{ item.type }}</text>
                </view>
                <view class="info-item">
                  <text class="label">认定结果：</text>
                  <text class="value highlight">{{ item.result }}</text>
                </view>
                <view class="info-item">
                  <text class="label">认定部门：</text>
                  <text class="value">{{ item.department }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无工伤认定记录</text>
          </view>
        </view>
        <!-- 鉴定记录 -->
        <view v-if="currentTab === 1" class="record-list">
          <view v-if="disabilityRecords && disabilityRecords.length > 0">
            <view
              v-for="(item, index) in disabilityRecords"
              :key="index"
              class="record-card"
              :class="{ 'first-identification': item.level <= 4 }"
            >
              <view class="time-line">
                <view class="date-box">
                  <text class="date">{{ item.date }}</text>
                </view>
                <view class="line"></view>
              </view>
              <view class="card-content">
                <view class="type-tag">{{ item.level }}级伤残</view>
                <view class="info-item">
                  <text class="label">鉴定时间：</text>
                  <text class="value">{{ item.assessmentTime }}</text>
                </view>
                <view class="info-item">
                  <text class="label">鉴定类型：</text>
                  <text class="value">{{ item.type }}</text>
                </view>
                <view class="info-item">
                  <text class="label">鉴定结论：</text>
                  <text class="value highlight">{{ item.conclusion }}</text>
                </view>
                <view class="info-item">
                  <text class="label">劳动能力：</text>
                  <text class="value">{{ item.workAbility }}</text>
                </view>
                <view class="info-item">
                  <text class="label">鉴定费用：</text>
                  <text class="value">{{ item.cost }}元</text>
                </view>
                <view class="info-item">
                  <text class="label">鉴定机构：</text>
                  <text class="value">{{ item.institution }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-tip">
            <text class="empty-text">暂无劳动鉴定记录</text>
          </view>
        </view>
        <!-- 就诊记录 -->
        <view v-if="currentTab === 2" class="record-list">
          <view class="insurance-benefits">
            <view class="benefit-section">
              <view class="section-title">医疗费用</view>
              <view class="benefit-items">
                <view class="benefit-item">
                  <text class="item-label">医疗费用：</text>
                  <text class="item-value"
                    >{{ benefits.medical.treatment }}元</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">康复费用：</text>
                  <text class="item-value"
                    >{{ benefits.medical.rehabilitation }}元</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">住院补助：</text>
                  <text class="item-value"
                    >{{ benefits.medical.hospitalization }}元</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">交通食宿：</text>
                  <text class="item-value"
                    >{{ benefits.medical.transportation }}元</text
                  >
                </view>
              </view>
            </view>

            <view class="benefit-section">
              <view class="section-title">停工留薪</view>
              <view class="benefit-items">
                <view class="benefit-item">
                  <text class="item-label">停工期间：</text>
                  <text class="item-value">{{
                    benefits.sickLeave.period
                  }}</text>
                </view>
                <view class="benefit-item">
                  <text class="item-label">工资待遇：</text>
                  <text class="item-value"
                    >{{ benefits.sickLeave.salary }}元/月</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">福利待遇：</text>
                  <text class="item-value">{{
                    benefits.sickLeave.welfare
                  }}</text>
                </view>
              </view>
            </view>

            <view class="benefit-section">
              <view class="section-title">伤残待遇</view>
              <view class="benefit-items">
                <view class="benefit-item">
                  <text class="item-label">伤残补助：</text>
                  <text class="item-value"
                    >{{ benefits.disability.compensation }}元</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">伤残津贴：</text>
                  <text class="item-value"
                    >{{ benefits.disability.allowance }}元/月</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">护理费用：</text>
                  <text class="item-value"
                    >{{ benefits.disability.nursing }}元/月</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">辅助器具：</text>
                  <text class="item-value"
                    >{{ benefits.disability.equipment }}元</text
                  >
                </view>
              </view>
            </view>

            <view class="benefit-section">
              <view class="section-title">工亡待遇</view>
              <view class="benefit-items">
                <view class="benefit-item">
                  <text class="item-label">丧葬补助：</text>
                  <text class="item-value">{{ benefits.death.funeral }}元</text>
                </view>
                <view class="benefit-item">
                  <text class="item-label">抚恤金：</text>
                  <text class="item-value"
                    >{{ benefits.death.pension }}元/月</text
                  >
                </view>
                <view class="benefit-item">
                  <text class="item-label">一次性补助：</text>
                  <text class="item-value"
                    >{{ benefits.death.compensation }}元</text
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </gracePage>
</template>
<script>
export default {
  data() {
    return {
      tabs: ["工伤认定", "劳动鉴定", "保险待遇"],
      currentTab: 0,
      isRefreshing: false,
      injuryRecords: [
        // {
        //   date: "2023-12-01",
        //   status: "已认定",
        //   accidentTime: "2023-11-30 14:30",
        //   location: "公司生产车间",
        //   cause: "机器操作不当导致手部受伤",
        //   type: "机械伤害",
        //   result: "确认为工伤",
        //   department: "市人力资源和社会保障局",
        // },
        // {
        //   date: "2023-11-15",
        //   status: "已认定",
        //   accidentTime: "2023-11-10 09:15",
        //   location: "建筑工地",
        //   cause: "高处坠落",
        //   type: "坠落伤害",
        //   result: "确认为工伤",
        //   department: "区人力资源和社会保障局",
        // },
      ],
      disabilityRecords: [
        // {
        //   date: "2023-12-10",
        //   level: 7,
        //   assessmentTime: "2023-12-08",
        //   type: "首次鉴定",
        //   conclusion: "七级伤残",
        //   workAbility: "部分丧失劳动能力",
        //   cost: 2800,
        //   institution: "市劳动能力鉴定委员会",
        // },
        // {
        //   date: "2023-11-20",
        //   level: 4,
        //   assessmentTime: "2023-11-18",
        //   type: "首次鉴定",
        //   conclusion: "四级伤残",
        //   workAbility: "大部分丧失劳动能力",
        //   cost: 3500,
        //   institution: "省劳动能力鉴定委员会",
        // },
      ],
      benefits: {
        medical: {
          treatment: 0,
          rehabilitation: 0,
          hospitalization: 0,
          transportation: 0,
        },
        sickLeave: {
          period: "-",
          salary: 0,
          welfare: "-",
        },
        disability: {
          compensation: 0,
          allowance: 0,
          nursing: 0,
          equipment: 0,
        },
        death: {
          funeral: 0,
          pension: 0,
          compensation: 0,
        },
      },
    };
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
    },
    toggleExpand(index) {
      this.medicalRecords[index].expanded =
        !this.medicalRecords[index].expanded;
    },
  },
};
</script>
<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.tabs {
  display: flex;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;

  width: 107%;
  position: relative;
  left: -24rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #606266;
  position: relative;
}

.tab-item.active {
  color: #409eff;
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #409eff;
  border-radius: 2rpx;
}

.content-area {
  flex: 1;
  overflow: auto;
}

.record-list {
  padding: 20rpx;
}

.record-card {
  display: flex;
  margin-bottom: 30rpx;
}

.time-line {
  width: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.date-box {
  background-color: #409eff;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
}

.date {
  color: #ffffff;
  font-size: 12px;
}

.line {
  width: 2rpx;
  height: 100%;
  background-color: #dcdfe6;
  margin-top: 20rpx;
}

.card-content {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  margin-bottom: 20rpx;
}

.label {
  color: #909399;
  font-size: 14px;
}

.value {
  color: #303133;
  font-size: 14px;
}

.highlight {
  color: #409eff;
  font-weight: 500;
}

.type-tag {
  display: inline-block;
  padding: 6rpx 20rpx;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4rpx;
  font-size: 12px;
  margin-bottom: 20rpx;
}

.first-identification .type-tag {
  background-color: #f0f9eb;
  color: #67c23a;
}

.header {
  margin-bottom: 20rpx;
}

.hospital {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
  margin-right: 20rpx;
}

.department {
  font-size: 14px;
  color: #909399;
}

.insurance-benefits {
  padding: 20rpx;
}

.benefit-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #409eff;
}

.benefit-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.item-label {
  color: #909399;
  font-size: 14px;
}

.item-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.item-value.highlight {
  color: #409eff;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  color: #909399;
  font-size: 14px;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.empty-text {
  color: #909399;
  font-size: 14px;
  text-align: center;
}
</style>