"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let identifyApi = {
	// 获取鉴定机构列表
	getInstitutionList: data => {
		return req({
			url: `app/identify/getInstitutionList`,
			method: 'get',
			data
		});
	},
	// 获取鉴定机构详情
	getInstitutionDetail: data => {
		return req({
			url: `app/identify/getInstitutionDetail`,
			method: 'get',
			data
		})
	},
	// 获取字典类型数据
	getDictType: data => {
		return req({
			url: `app/diagnosis/getDictData`,
			method: 'get',
			data
		})
	},
	// 新增职业病鉴定申请
	addIdentification: data => {
		return req({
			url: `app/identify/addIdentification`,
			method: 'post',
			data
		})
	},
	// 获取职业病鉴定结果列表
	getIdentificationInfo: data => {
		return req({
			url: `app/identify/getIdentificationList`,
			method: 'get',
			data
		});
	},
	// 获取职业病鉴定详情
	getIdentificationDetail: data => {
		return req({
			url: `app/identify/getIdentificationDetail`,
			method: 'get',
			data
		})
	},
	// 编辑职业病诊断申请
	updateIdentification: data => {
		return req({
			url: `app/identify/updateIdentification`,
			method: 'put',
			data
		})
	},
	// 待上传资料详情
	getProvideFile: data => {
		return req({
			url: `app/identify/process/provideFile`,
			method: 'get',
			data
		})
	},
	// 提交职业病诊断申请
	submitIdentify: data => {
		return req({
			url: `app/identify/submit`,
			method: 'put',
			data
		})
	},
  // 下载鉴定报告
  downloadIdentify: data => {
		return req({
			url: `app/identify/downloadCertificate`,
			method: 'get',
			data
		})
	},
  // 获取字典类型数据
	getDictType: data => {
		return req({
			url: `app/identify/getDictData`,
			method: 'get',
			data
		})
	},
  // 下载鉴定申请书
  downloadIdeApplication: data => {
    return req({
      url: `app/identify/downloadApplication`,
      method: 'get',
      data,
    })
  },
  // 获取鉴定申请书
  getIdeApplication: data => {
    return req({
      url: `app/identify/getApplication`,
      method: 'get',
      data
    })
  },
  // 保存职业史
  saveIdentifyJobHistory: data => {
    return req({
      url: `app/identify/identifyJobHistory`,
      method: 'post',
      data
    })
  },
  // 删除职业史
  deleteIdentifyJobHistory: data => {
    return req({
      url: `app/identify/deleteIdentifyJobHistory`,
      method: 'delete',
      data
    })
  },
  // 删除接触史
  deleteIdentifyJobHistoryHazard: data => {
    return req({
      url: `app/identify/deleteIdentifyJobHistoryHazard`,
      method: 'delete',
      data
    })
  },
  // 获取可再鉴定鉴定列表
  getCanAgainDiagnosis: data => {
    return req({
      url: `app/identify/getCanAgainDiagnosis`,
      method: 'get',
      data
    })
  },
  // 可鉴定诊断列表
  getCanIdentificationList: data => {
    return req({
      url: `app/identify/getCanIdentificationList`,
      method: 'get',
      data
    })
  },
  // 获取可鉴定诊断详情信息
  getCanIdentificationDetail: data => {
    return req({
      url: `app/identify/getCanIdentificationDetail`,
      method: 'get',
      data
    })
  },
  // 获取附件3不予受理通知书
  getIdeRefuseNotice: data => {
    return req({
      url: `app/identify/getRefuseNotice`,
      method: 'get',
      data,
    })
  },
  // 获取附件6受理通知书
  getIdeAcceptNotice: data => {
    return req({
      url: `app/identify/getAcceptNotice`,
      method: 'get',
      data,
    })
  },
}

export default identifyApi