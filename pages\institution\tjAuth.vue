<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="授权查询" />

    <view slot="gBody" class="grace-body  container">
      <view class="search-box">
        <input type="text" v-model="searchForm.cname" placeholder="请输入用人单位名称" />
        <view class="btn-box" style="display: flex; justify-content: space-between;">
          <button @click="handleSearch" style="width: 40%;">搜索</button>
          <button @click="reset" style="width: 40%; background: #fff; color: #007AFF;">重置</button>
        </view>
      </view>

      <template v-if="list.length > 0">


        <view class="health-check-item" v-for="(item, index) in list" :key="index">
          <view class="item-header">
            <view class="org-name">{{ item.cname }}</view>
          </view>

          <view class="item-content">

            <view class="check-info">
              <view class="info-item">
                <text class="label">申请时间：</text>
                <text class="value">{{ getFormatedDate(item.createdAt) }}</text>
              </view>
              <view class="info-item">
                <text class="label">授权状态：</text>
                <text class="value type-tag" :class="getConclusionClass(item.status)">{{ getStatus(item.status)
                }}</text>
              </view>
            </view>
          </view>

          <view v-if="item.status == 0 || item.status == 1" class="operaction"
            style="display:flex;justify-content:end;gap:.5em;  height: 40rpx;  line-height: normal;">
            <view v-if="item.status == 0" class="edit-btn" @click="handle(item._id, 1)">同意</view>
            <view v-if="item.status == 0" class="edit-btn" @click="handle(item._id, 2)">拒绝</view>
            <view v-if="item.status == 1" class="edit-btn" @click="handle(item._id, 3)">取消</view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-data">
          <view class="empty-icon">
            <text class="grace-grids-icon grace-icons icon-info" style="font-size:80rpx;"></text>
          </view>
          <view class="empty-text">暂无授权申请记录</view>
        </view>
      </template>
    </view>
  </gracePage>
</template>
<script>
import healthApi from '@/api/health.js'
import moment from "moment";

export default {
  data() {
    return {
      list: [],
      searchForm: {
        cname: ''
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {

    handleSearch() {
      this.getData();
    },
    reset() {
      this.searchForm = {
        cname: '',
      }
      this.getData();
    },

    async getData() {
      const res = await healthApi.getHCReportAuthList({
        ...this.searchForm
      });
      this.list = res.data
    },

    getFormatedDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

    getStatus(status) {
      if (status == 0) {
        return '待处理'
      } else if (status == 1) {
        return '已授权'
      } else if (status == 2) {
        return '已拒绝'
      } else if (status == 3) {
        return '已取消'
      } else {
        return '-'
      }
    },

    getConclusionClass(conclusion) {
      const classMap = {
        '0': 'conclusion-warning',
        '1': 'conclusion-normal',
        '2': 'conclusion-danger',
        '3': 'conclusion-warning',
        '': 'conclusion-alert'
      };
      return classMap[conclusion] || '';
    },

    handle(_id, status) {
      // 弹框提示是否确认操作
      uni.showModal({
        title: '提示',
        content: status == 1 ? '是否同意授权？' : status == 2 ? '是否拒绝授权？' : '是否取消授权？',
        showCancel: true,
        cancelText: '取消',
        confirmText: '确定',
        success: async (res) => {
          if (!res.confirm) {
            return
          }
          const result = await healthApi.updateHCReportAuthStatus({ _id, status });
          if (result.status == 200) {
            uni.showToast({
              title: '操作成功',
              icon: 'success'
            })
            this.getData()
          }
          else {
            uni.showToast({
              title: result.message,
              icon: 'none'
            })
          }
        }
      });
    },
  },
}
</script>
<style lang="scss" scoped>
.header {
  height: 60rpx;

}

.grace-td {
  height: 120rpx;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 超过两行以...显示 */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 限制显示文本的行数为3行 */

}

.circleBtn {
  height: 48rpx;
  width: 48rpx;
  font-size: 16rpx;
  border-radius: 99px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0
}

.health-check-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);

  &:first-child {
    margin-top: 20rpx;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #f0f0f0;

    .date {
      font-size: 14px;
      color: #666666;
    }

    .org-name {
      display: flex;
      align-items: center;
      /* margin-bottom: 20rpx; */
      font-size: 16px;
      color: #333333;

      text {
        margin-left: 10rpx;
      }
    }
  }

  .item-content {
    padding: 20rpx 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .check-info {
    margin-top: 20rpx;

    .info-item {
      margin-bottom: 16rpx;
      font-size: 14px;
      color: #666666;

      .label {
        color: #999999;
      }
    }
  }

  .item-footer {
    margin-top: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

}

.conclusion-normal {
  color: #52c41a;
}

.conclusion-warning {
  color: #fa8c16;
}

.conclusion-danger {
  color: #f5222d;
}

.conclusion-alert {
  color: #722ed1;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    margin-bottom: 20rpx;
    color: #c0c4cc;
  }

  .empty-text {
    font-size: 28rpx;
    color: #909399;
  }
}

.search-box {
  padding: 15px;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20rpx;

  input {
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
  }

  button {
    height: 40px;
    background: #007AFF;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.operaction {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 40rpx;
  margin-bottom: 20rpx;

  view {
    text-align: center;
    line-height: 33px;
    width: 81px;
    height: 33px;
    border-radius: 3px;
  }

  .cancel-btn {
    color: #3E73FE;
    border: 1px solid #3E73FE;
    text-align: center;
    line-height: 33px;
  }

  .edit-btn {
    background: #3E73FE;
    border: 1px solid #3E73FE;
    color: #fff;
    margin-left: 12px;
  }
}
</style>