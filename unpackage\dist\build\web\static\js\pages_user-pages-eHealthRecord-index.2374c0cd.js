(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-index"],{"12b1":function(t,e,a){"use strict";a.r(e);var i=a("1d91"),n=a("9164");for(var c in n)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(c);a("ead9");var r=a("828b"),o=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"0a55a85c",null,!1,i["a"],void 0);e["default"]=o.exports},"16dd":function(t,e,a){t.exports=a.p+"static/img/个人基本信息.395ebcba.svg"},"1d91":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return c})),a.d(e,"a",(function(){return i}));var i={gracePage:a("93fe").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"个人电子健康档案"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"card"},[a("img",{staticClass:"card-img",attrs:{src:e.img,mode:"scaleToFill"}}),a("v-uni-view",{staticClass:"card-content"},[a("v-uni-view",{staticClass:"card-content-text"},[a("v-uni-view",{staticClass:"card-content-text-title"},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"card-content-text-desc"},[t._v(t._s(e.desc))])],1),a("v-uni-view",{staticStyle:{display:"flex","align-items":"center"}},[a("v-uni-button",{staticClass:"card-content-button",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toUrl(e.url)}}},[t._v("查看")])],1)],1)],1)})),1)],1)},c=[]},2580:function(t,e,a){t.exports=a.p+"static/img/医疗卫生服务记录.247c742b.svg"},"3ee7":function(t,e,a){t.exports=a.p+"static/img/职业健康体检记录.84dc9501.svg"},"590f":function(t,e,a){t.exports=a.p+"static/img/诊断医疗记录.b65f5c6e.svg"},"5cf1":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";.grace-body[data-v-0a55a85c]{height:100%;background-color:#f6f6f6!important;display:flex;flex-direction:column;align-items:center;gap:%?20?%;padding-top:%?30?%}.grace-body .card[data-v-0a55a85c]{width:%?690?%;height:%?160?%;border-radius:%?8?%;background-color:#fff;box-shadow:0 2px 4px 0 rgba(0,0,0,.0372);display:flex;padding:%?24?%;box-sizing:border-box}.grace-body .card[data-v-0a55a85c]:last-child{margin-bottom:%?24?%}.grace-body .card .card-img[data-v-0a55a85c]{width:%?112?%;height:%?112?%}.grace-body .card .card-content[data-v-0a55a85c]{width:100%;margin-left:%?24?%;display:flex;justify-content:space-between;gap:%?56?%}.grace-body .card .card-content .card-content-text .card-content-text-title[data-v-0a55a85c]{opacity:.9;font-family:Source Han Sans;font-size:%?32?%;font-weight:700;line-height:%?30?%;letter-spacing:0;font-variation-settings:"opsz" auto;color:#000}.grace-body .card .card-content .card-content-text .card-content-text-desc[data-v-0a55a85c]{opacity:.9;margin-top:%?12?%;font-family:Source Han Sans;font-size:%?24?%;font-weight:400;line-height:normal;letter-spacing:0;font-variation-settings:"opsz" auto;color:#666}.grace-body .card .card-content .card-content-button[data-v-0a55a85c]{width:%?120?%;height:%?64?%;\n  /* 圆 */border-radius:256000px;opacity:1;background:#f0f9eb;box-sizing:border-box;border:1px solid #b3e09c;color:#67c23a;font-size:%?28?%;font-weight:700;line-height:%?44?%;letter-spacing:0;font-variation-settings:"wght" 500;display:flex;align-items:center;justify-content:center;padding:0}',""]),t.exports=e},7116:function(t,e,a){var i=a("5cf1");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("1f63b5ee",i,!0,{sourceMap:!1,shadowMode:!1})},"8c78":function(t,e,a){t.exports=a.p+"static/img/重点人群健康管理记录.23bdbf5c.svg"},9164:function(t,e,a){"use strict";a.r(e);var i=a("b2a6"),n=a.n(i);for(var c in i)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(c);e["default"]=n.a},b2a6:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("16dd")),c=i(a("3ee7")),r=i(a("590f")),o=i(a("8c78")),d=i(a("f86f")),s=i(a("2580")),l={data:function(){return{list:[{title:"个人基本信息",desc:"劳动者可以查看个人基本信息及更改",img:n.default,url:"basicInfo"},{title:"职业健康体检记录",desc:"劳动者可查看职业健康体检记录及结果如有异常可申报",img:c.default,url:"exam"},{title:"诊断医疗记录",desc:"劳动者可查看职业健康诊断记录及结果如有异常可申报",img:r.default,url:"diagnose"},{title:"重点人群健康管理记录",desc:"劳动者可以查看重点人群健康管理记录",img:o.default,url:"healthManage"},{title:"工伤保险记录",desc:"劳动者可查看工伤保险记录",img:d.default,url:"injury"},{title:"医疗卫生服务记录",desc:"劳动者可查看医疗卫生服务记录",img:s.default,url:"servic"},{title:"档案申诉情况",desc:"劳动者可查看档案申诉情况",img:r.default,url:"complaint"},{title:"档案授权管理",desc:"劳动者能够看到所有申请授权的记录并进行处理",img:o.default,url:"auth"},{title:"健康档案报告",desc:"通过统计、趋势分析对个人健康档案进行综合呈现",img:c.default,url:"report"},{title:"职业健康检查预警",desc:"劳动者能够看到所有职业健康检查提醒预警",img:c.default,url:"warning"}]}},methods:{toUrl:function(t){uni.navigateTo({url:t})}},components:{}};e.default=l},ead9:function(t,e,a){"use strict";var i=a("7116"),n=a.n(i);n.a},f86f:function(t,e,a){t.exports=a.p+"static/img/工伤保险记录.00053689.svg"}}]);