(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-report"],{"1cb9":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("e40e")),i={getEHealthRecordAuthList:function(e){return(0,r.default)({url:"manage/eHealthRecord/getEHealthRecordAuthList",method:"get",data:e})},handleEHealthRecordAuth:function(e){return(0,r.default)({url:"manage/eHealthRecord/handleEHealthRecordAuth",method:"post",data:e})},getEHealthRecordBaseInfo:function(e){return(0,r.default)({url:"manage/eHealthRecord/getEHealthRecordBaseInfo",method:"get",data:e})},updateEHealthRecordBaseInfo:function(e){return(0,r.default)({url:"manage/eHealthRecord/updateEHealthRecordBaseInfo",method:"post",data:e})},getSupervisionList:function(e){return(0,r.default)({url:"manage/eHealthRecord/getSupervisionList",method:"post",data:e})},postComplaint:function(e){return(0,r.default)({url:"manage/eHealthRecord/postComplaint",method:"post",data:e})},getLaborIsEdit:function(e){return(0,r.default)({url:"manage/eHealthRecord/getLaborIsEdit",method:"get",data:e})},addEmploymentHistory:function(e){return(0,r.default)({url:"manage/eHealthRecord/addEmploymentHistory",method:"post",data:e})},editEmploymentHistory:function(e){return(0,r.default)({url:"manage/eHealthRecord/editEmploymentHistory",method:"post",data:e})},deleteEmploymentHistory:function(e){return(0,r.default)({url:"manage/eHealthRecord/deleteEmploymentHistory",method:"post",data:e})},getEmploymentHistory:function(e){return(0,r.default)({url:"manage/eHealthRecord/getEmploymentHistory",method:"get",data:e})},getDiaList:function(e){return(0,r.default)({url:"manage/eHealthRecord/getDiaList",method:"post",data:e})},getIdentificationList:function(e){return(0,r.default)({url:"manage/eHealthRecord/getIdentificationList",method:"post",data:e})},findHarmFactors:function(e){return(0,r.default)({url:"manage/eHealthRecord/findHarmFactors",method:"get",data:e})}};t.default=i},"20c5":function(e,t,a){"use strict";var n=a("46eb"),r=a.n(n);r.a},"2fed":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("e40e")),i={getCheckHealthList:function(e){return(0,r.default)({url:"manage/btHealthCheck/checkOrgList",method:"post",data:e})},getOneDetail:function(e){return(0,r.default)({url:"manage/btHealthCheck/getOneDetail",method:"get",data:{id:e.id}})},getappointmentRecord:function(e){return(0,r.default)({url:"manage/btHealthCheck/appointmentRecord",method:"get",data:e})},getappointment:function(e){return(0,r.default)({url:"manage/btHealthCheck/appointment",method:"post",data:e})},cancelAppointment:function(e){return(0,r.default)({url:"manage/btHealthCheck/cancelAppointment",method:"post",data:e})},updateAppointment:function(e){return(0,r.default)({url:"manage/btHealthCheck/updateAppointment",method:"post",data:e})},checkReport:function(e){return(0,r.default)({url:"manage/btHealthCheck/checkReport",method:"get",data:e})},reportList:function(e){return(0,r.default)({url:"manage/btHealthCheck/reportList",method:"get",data:e})},cancelReservation:function(e){return(0,r.default)({url:"manage/btHealthCheck/cancelReservation",method:"get",data:e})},getHCReportAuthList:function(e){return(0,r.default)({url:"manage/btHealthCheck/getHCReportAuthList",method:"get",data:e})},updateHCReportAuthStatus:function(e){return(0,r.default)({url:"manage/btHealthCheck/updateHCReportAuthStatus",method:"post",data:e})}};t.default=i},"46eb":function(e,t,a){var n=a("551d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("967d").default;r("58230c74",n,!0,{sourceMap:!1,shadowMode:!1})},4739:function(e,t,a){"use strict";a.r(t);var n=a("cc34"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"551d":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'.chartContainer[data-v-34bbf51f]{width:100%;height:100%}.infoCard[data-v-34bbf51f]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.infoCard .cardTitle[data-v-34bbf51f]{margin-bottom:16px}.infoCard .cardTitle .titleText[data-v-34bbf51f]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555;display:flex;justify-content:space-between}.infoCard .cardTitle .titlePoint[data-v-34bbf51f]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.infoCard .cardItem[data-v-34bbf51f]{display:flex;margin-bottom:12px;justify-content:space-between;font-size:%?24?%;font-weight:400;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardItem[data-v-34bbf51f]:last-child{margin-bottom:0}.container[data-v-34bbf51f]{display:flex;flex-direction:column;align-items:center;height:64vh;width:100%}uni-picker .uni-input[data-v-34bbf51f]{color:#a8abb2}uni-picker .uni-input[data-v-34bbf51f]::after{content:">";display:inline-block;-webkit-transform:rotate(90deg) scaleY(1.5) translateY(-.25em);transform:rotate(90deg) scaleY(1.5) translateY(-.25em);margin-right:1em}',""]),e.exports=t},"9a53":function(e,t,a){"use strict";a.r(t);var n=a("cc0e"),r=a("4739");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("20c5");var o=a("828b"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"34bbf51f",null,!1,n["a"],void 0);t["default"]=s.exports},cc0e:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={gracePage:a("93fe").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"职业健康检查统计分析"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[e._v("基本信息")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[e._v("姓名："+e._s(e.userInfo.name||"暂无"))])],1),a("v-uni-view",[a("v-uni-text",[e._v("年龄："+e._s(e.userInfo.age||"暂无"))])],1),a("v-uni-view",[a("v-uni-text",[e._v("性别："+e._s("1"===e.userInfo.gender?"男":"2"===e.userInfo.gender?"女":"暂无"))])],1)],1)],1),a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[e._v("筛选条件")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticStyle:{"margin-right":"1em"}},[e._v("检查类型")]),a("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:e.examTypeIndex,range:e.examTypeOption},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.examTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.examTypeLabel))])],1),a("v-uni-view",{staticStyle:{"margin-right":"1em"}},[e._v("检查指标")]),a("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:e.pickerIndex,range:e.pickerArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindPickerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.pickerLabel||"暂无数据"))])],1)],1)],1),a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-view",[e._v("变化趋势图")]),a("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between"}})],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticStyle:{width:"100%",height:"100%"}},[a("v-uni-view",{ref:"chartContainer",staticClass:"chartContainer",attrs:{id:"chartContainer"}})],1)],1)],1)],1)],1)],1)},i=[]},cc34:function(e,t,a){"use strict";(function(e){a("6a54");var n=a("3639").default,r=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("c9b5"),a("bf0f"),a("ab80"),a("c1a3"),a("18f7"),a("de6c"),a("4100"),a("2797"),a("aa9c"),a("e838"),a("fd3c"),a("08eb"),a("f3f7"),a("d4b5"),a("aa77"),a("c223");var i=r(a("2634")),o=r(a("2fdc")),s=r(a("9b1b")),l=r(a("2fed")),c=r(a("1cb9")),u=n(a("19d2")),d=r(a("07b0")),f={dataset:{source:[]},tooltip:{trigger:"axis"},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#999"}},boundaryGap:!1}],yAxis:[{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DCDCDC"}},axisLine:{show:!1,lineStyle:{color:"#A4A4A4"}},axisTick:{show:!1},nameTextStyle:{color:"#A4A4A4"},splitArea:{show:!1}}],series:[{name:"指标",type:"line",encode:{x:"year",y:"result"},lineStyle:{normal:{width:8,color:{type:"linear",colorStops:[{offset:0,color:"#A9F387"},{offset:1,color:"#48D8BF"}],globalCoord:!1},shadowColor:"rgba(72,216,191, 0.3)",shadowBlur:10,shadowOffsetY:20}},itemStyle:{normal:{color:"#fff",borderColor:"#A9F387",borderWidth:10}},label:{normal:{show:!0,position:"top",color:"#A9F387"}},smooth:!0}]},h=(0,s.default)((0,s.default)({},f),{},{series:[{name:"暂无数据",type:"line",data:[],label:{show:!1}}],xAxis:[{type:"category",data:[],axisLine:{lineStyle:{color:"#999"}},boundaryGap:!1}],yAxis:[{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DCDCDC"}},axisLine:{show:!1,lineStyle:{color:"#A4A4A4"}},axisTick:{show:!1},nameTextStyle:{color:"#A4A4A4"},splitArea:{show:!1}}]}),p={data:function(){return{userInfo:{gender:"0",name:"",age:"",department:"",workType:"",workYears:""},ec:{option:{}},pickerArray:[],pickerLabel:"",pickerIndex:0,chartData:[],chartInstance:null,checkItems:[],historyData:[],isEmpty:!1,examTypeOption:[{value:"",label:"全部"},{value:"1",label:"岗前"},{value:"2",label:"在岗"},{value:"3",label:"离岗"}],examTypeLabel:"全部",examTypeIndex:0}},mounted:function(){var t=this;return(0,o.default)((0,i.default)().mark((function a(){var n;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.getBasicInfo();case 3:return a.next=5,l.default.reportList({examType:t.examTypeOption[t.examTypeIndex].value});case 5:if(n=a.sent,n.data&&0!==n.data.length){a.next=10;break}return t.isEmpty=!0,uni.showToast({title:"暂无体检记录",icon:"none",duration:2e3}),a.abrupt("return");case 10:t.processCheckItems(n.data),t.initChartH5(),t.getData(),a.next=20;break;case 15:a.prev=15,a.t0=a["catch"](0),e.error("数据获取失败:",a.t0),t.isEmpty=!0,uni.showToast({title:"数据获取失败",icon:"none",duration:2e3});case 20:case"end":return a.stop()}}),a,null,[[0,15]])})))()},components:{},methods:{getBasicInfo:function(){var t=this;return(0,o.default)((0,i.default)().mark((function a(){var n;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,c.default.getEHealthRecordBaseInfo();case 3:n=a.sent,n.data&&(t.userInfo=(0,s.default)((0,s.default)({},t.userInfo),{},{name:n.data.name||"",gender:n.data.gender||"0",age:n.data.age||"",department:n.data.department||"",workType:n.data.workType||"",workYears:n.data.workYears||""})),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),e.error("获取基本信息失败:",a.t0);case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))()},calculateAge:function(e){var t=new Date(e),a=new Date,n=a.getFullYear()-t.getFullYear(),r=a.getMonth()-t.getMonth();return(r<0||0===r&&a.getDate()<t.getDate())&&n--,n.toString()},processCheckItems:function(e){var t,a=[],n=new Map;e.sort((function(e,t){return new Date(e.registerTime)-new Date(t.registerTime)})),e.forEach((function(e){var t=(0,d.default)(e.registerTime).format("YYYY-MM-DD");e.checkDepartments.forEach((function(e){e.checkProjects.forEach((function(e){e.checkItems.forEach((function(e){var r=e.itemId._id;n.has(r)||n.set(r,{label:e.itemId.projectName,value:e.itemId._id,unit:e.itemId.msrunt,history:[]}),n.get(r).history.push({year:t,result:parseFloat(e.result)}),a.push({label:e.itemId.projectName,value:e.itemId._id,unit:e.itemId.msrunt,result:e.result})}))}))}))})),this.checkItems=Array.from(new Set(a.map((function(e){return JSON.stringify(e)})))).map((function(e){return JSON.parse(e)})),this.pickerArray=this.checkItems.map((function(e){return{value:e.value,label:e.label}})),this.pickerLabel=(null===(t=this.pickerArray[0])||void 0===t?void 0:t.label)||"",this.historyData=Array.from(n.values())},updateData:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.clearData(),t.next=3,l.default.reportList({examType:e.examTypeOption[e.examTypeIndex].value});case 3:if(a=t.sent,a.data&&0!==a.data.length){t.next=8;break}return e.isEmpty=!0,uni.showToast({title:"暂无体检记录",icon:"none",duration:2e3}),t.abrupt("return");case 8:e.processCheckItems(a.data);case 9:case"end":return t.stop()}}),t)})))()},getData:function(){if(!this.checkItems.length)return this.isEmpty=!0,f.dataset.source=[],f.series[0].name="暂无数据",void this.chartInstance.setOption(f);var e=this.checkItems[this.pickerIndex],t=this.historyData.find((function(t){return t.value===e.value}));t&&t.history.length>0?(this.isEmpty=!1,t.history.sort((function(e,t){return e.year-t.year})),f.dataset.source=t.history,f.series[0].name="".concat(e.label,"(").concat(e.unit,")"),this.chartInstance.setOption(f)):(this.isEmpty=!0,this.chartInstance.setOption(h))},bindPickerChange:function(e){this.pickerIndex=e.detail.value,this.pickerLabel=this.pickerArray[this.pickerIndex].label,f.series[0].name=this.pickerLabel,this.getData()},examTypeChange:function(e){var t=this;return(0,o.default)((0,i.default)().mark((function a(){return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.examTypeIndex=e.detail.value,t.examTypeLabel=t.examTypeOption[t.examTypeIndex].label,a.next=4,t.updateData();case 4:t.getData();case 5:case"end":return a.stop()}}),a)})))()},clearData:function(){this.checkItems=[],this.pickerArray=[],this.pickerLabel="",this.historyData=[],this.isEmpty=!0},initChart:function(e,t,a,n){var r=u.init(e,null,{width:t,height:a,devicePixelRatio:n});return e.setChart(r),r.setOption(this.isEmpty?h:f),r},initChartH5:function(){var e=document.getElementById("chartContainer");this.chartInstance=u.init(e),this.chartInstance.setOption(this.isEmpty?h:f)}}};t.default=p}).call(this,a("ba7c")["default"])}}]);