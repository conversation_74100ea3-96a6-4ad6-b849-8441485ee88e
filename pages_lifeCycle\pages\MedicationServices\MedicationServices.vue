<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF">
    <my-header slot="gHeader" title="用药指导" />
    <view class="grace-body container" slot="gBody">
      <view class="search">
        <view class="searchInfo" @click="openSearchPopup">
          <uni-icons type="search" size="20" color="dodgerblue"></uni-icons>
          <text>查询</text>
        </view>
      </view>
      <view class="mechanism">
        <view class="mechanismList" v-for="item in recordsList" :key="item.id">
          <view class="left">
            <view class="text">
              <view class="label"> 报告时间: </view>
              <view class="content"> {{ item.reportTime }} </view>
            </view>
            <view class="text">
              <view class="label"> 药品种类: </view>
              <view class="content"> {{ item.medicineType }} </view>
            </view>
            <view class="text">
              <view class="label"> 药品名称: </view>
              <view class="content"> {{ item.medicineName }} </view>
            </view>
          </view>
          <view class="right">
            <view
              class="tag tag_green"
              @click="gotoMedicationServicesInfo(item.id)"
            >
              查看
            </view>
          </view>
        </view>
      </view>

      <uni-popup
        ref="popup"
        background-color="#fff"
        @change="change"
        style="z-index: 90"
        mask-background-color="#0000000"
      >
        <view class="popup-content">
          <view class="forms">
            <view class="forms_item">
              <view class="label"> 报告时间 </view>
              <uni-datetime-picker
                v-model="formDate.range"
                type="daterange"
                rangeSeparator="至"
              />
            </view>
            <view class="forms_item">
              <view class="label"> 药品种类 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.mechanism"
                placeholder="请输入药品科类"
              ></uni-easyinput>
            </view>
            <view class="forms_item">
              <view class="label"> 药品名称 </view>
              <uni-easyinput
                class="uni-mt-5"
                trim="all"
                v-model="formDate.physician"
                placeholder="请输入药品名称"
              ></uni-easyinput>
            </view>

            <view class="forms_btn">
              <button
                type="default"
                plain="true"
                class="reset_btn"
                @click="reset"
              >
                重置
              </button>
              <button type="primary" @click="search" class="search_btn">
                查询
              </button>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </gracePage>
</template>
	
	<script>
import stationApi from '@/api/rehabStation.js'
export default {
  data() {
    return {
      formDate: {
        range: "",
        mechanism: "",
        physician: "",
      },
      pageParams: {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime"
      },
      recordsList: [],
    };
  },
  onLoad() {
    this.getMedicationList()
  },
  methods: {
    async getMedicationList() {
      try {
        const params = {
          ...this.pageParams,
          drugType: this.formDate.mechanism || undefined,
          drugName: this.formDate.physician || undefined,
          fromCreateTime: this.formDate.range?.[0] || undefined,
          toCreateTime: this.formDate.range?.[1] || undefined
        }
        
        const data = await stationApi.medicationGuidanceList(params)
        if (data.status === 200) {
          this.recordsList = data.data.list
        } 
      } catch (error) {
        console.error('获取用药指导列表失败:', error)
      }
    },
    gotoMedicationServicesInfo(id) {
      console.log("查看用药指导记录：", id);
      uni.navigateTo({
        url: `/pages_lifeCycle/pages/MedicationServices/MedicationServicesInfo?id=${id}`,
      });
    },
    change(e) {
      console.log(e);
    },
    reset() {
      this.formDate = {
        range: "",
        mechanism: "",
        physician: "",
      };
      this.pageParams = {
        pageNum: 1,
        pageSize: 9999,
        isAsc: "desc",
        orderBy: "createTime"
      };
      this.getMedicationList();
    },
    search() {
      this.pageParams.pageNum = 1
      this.getMedicationList();
      this.$refs.popup.close();
    },
    openSearchPopup() {
      this.$refs.popup.open("right");
    },
  },
};
</script>
	
	<style scoped lang="less">
.tag {
  padding: 10rpx 30rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
}
.tag_green {
  background-color: #21bd9f30;

  color: #21bd9f;
  border: 1px solid #21bd9f;
}
.tag_black {
  background-color: #5b5b5b30;

  color: #5b5b5b;
  border: 1px solid #5b5b5b;
}
.grace-body {
  min-height: calc(100vh - 120rpx);
  padding-top: 30rpx;
  background-color: #f6f6f6;
}

.grace-body .search {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: column;
  color: #169bd5;
  margin-bottom: 30rpx;
}

.grace-body .search .searchInfo {
  display: flex;
  align-items: center;
}

.mechanismList {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 30rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mechanismList .left {
  flex: 1;
  margin-right: 20rpx;
}

.mechanismList .right {
  color: dodgerblue;
  font-size: 28rpx;
}

.mechanismList .left .text {
  display: flex;
  margin-bottom: 10rpx;
  align-items: center;
}

.mechanismList .left .text .label {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.mechanismList .left .text .content {
  font-size: 28rpx;
}

.popup-content {
  position: relative;
  width: 70vw;
  height: 88vh;
  padding: 40rpx;
  padding-top: 120rpx;
}

.forms_item {
  margin-bottom: 20rpx;
}

.forms_item .label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.forms_item input {
  border: 1px solid #f6f6f6;
  border-radius: 5rpx;
}

.forms_btn {
  position: absolute;
  bottom: 5%;
  width: 100%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.forms_btn button {
  margin: 0;
  font-size: 28rpx;
  padding: 0 80rpx;
}
.reset_btn {
  background-color: #5b5b5b;
}
.search_btn {
  background-color: #169bd5;
}
</style>