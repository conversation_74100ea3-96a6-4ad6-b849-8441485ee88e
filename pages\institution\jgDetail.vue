<template>
	<view class="institution">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					机构详情
				</view>
			</block>
		</uni-nav-bar>
		<view class="section-body">
			<view class="jg-detail">
				<view class="row">
					<view class="label">机构名称：</view>
					<view class="desc">{{institutionDetail.institutionName}}</view>
				</view>
				<view class="row">
					<view class="label">联系人：</view>
					<view class="desc">{{institutionDetail.contactPerson}}</view>
				</view>
				<view class="row phone">
					<view class="label">联系电话：</view>
					<view class="desc">{{institutionDetail.contactPhone}}</view>
					<u-icon name="phone-fill" color="#409EFF" size="20"></u-icon>
				</view>
				<view class="row address">
					<view class="label">地址：</view>
					<view class="desc">{{institutionDetail.address}}4号</view>
					<u-icon name="map-fill" color="#409EFF" size="20"></u-icon>
				</view>
				<view class="row">
					<view class="label">备案号：</view>
					<view class="desc">{{institutionDetail.recordCode}}</view>
				</view>
				<view class="row">
					<view class="label">职业病诊断类别：</view>
					<view class="desc">
						{{institutionDetail.diseaseCategories&&institutionDetail.diseaseCategories.join('')}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import diagnosisApi from '../../api/diagnosis.js'
	export default {
		data() {
			return {
				id: '',
				institutionDetail: {}
			}
		},
		created(options) {
			this.getInsDetail()
		},
		onLoad(options) {
			this.id = options
		},
		methods: {
			// 获取诊断机构详情
			async getInsDetail() {
				console.log(this.id);
				const res = await diagnosisApi.getInsDetail(this.id)
				this.institutionDetail = res.data
			},
			back() {
				uni.navigateBack()
			},
		},

	}
</script>


<style lang="scss" scoped>
	.institution {
		width: 100%;
		min-height: 100vh;
		background-color: #f6f6f6;
		padding: 0 30rpx;
		box-sizing: border-box;
	}

	.nav-left {
		display: flex;
		align-items: center;
		width: auto;
		color: #fff;
		white-space: nowrap;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.section-body {
		padding-top: 30rpx;

		.jg-detail {
			.row {
				width: 100%;
				// border-bottom: 1px solid rgba(62, 115, 254, 0.1228);
				border-bottom: 1px solid #9e9e9e54;
				margin-bottom: 30rpx;
				padding-bottom: 20rpx;
				position: relative;
				display: flex;
				flex-direction: column;

				.label {
					font-family: Source Han Sans;
					font-size: 16px;
					color: #333333;
					padding-bottom: 14rpx;
				}

				.desc {
					font-family: Source Han Sans;
					font-size: 16px;
					font-weight: 350;
					color: #666666;
				}

				.u-icon {
					position: absolute;
					right: 0;
					bottom: 14rpx;
				}
			}
		}
	}
</style>