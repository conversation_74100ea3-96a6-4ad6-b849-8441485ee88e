<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <my-header slot="gHeader" title="职业健康检查统计分析" />
    <view slot="gBody" class="grace-body">

      <view class="infoCard grace-box-shadow">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText"><text>基本信息</text></view>
        </view>
        <view class="cardItem">
          <view><text>姓名：{{ userInfo.name || '暂无' }}</text></view>
          <view><text>年龄：{{ userInfo.age || '暂无' }}</text></view>
          <view><text>性别：{{ userInfo.gender === '1' ? '男' : (userInfo.gender === '2' ? '女' : '暂无') }}</text></view>
          <!-- <view><text>婚姻状况：{{ userInfo.marriage }}</text></view> -->
        </view>
        <!-- <view class="cardItem">
          <view><text>手机：{{ userInfo.phoneNum }}</text></view>
        </view>
        <view class="cardItem">
          <view><text>证件号：{{ userInfo.idNo }}</text></view>
        </view> -->
      </view>

      <view class="infoCard grace-box-shadow">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText"><text>筛选条件</text></view>
        </view>
        <view class="cardItem">
          <view style="margin-right: 1em;">
            检查类型
          </view>
          <picker mode=selector @change="examTypeChange" range-key="label" :value="examTypeIndex"
            :range="examTypeOption">
            <view class="uni-input">{{ examTypeLabel }}</view>
          </picker>

          <view style="margin-right: 1em;">
            检查指标
          </view>
          <picker mode=selector @change="bindPickerChange" range-key="label" :value="pickerIndex" :range="pickerArray">
            <view class="uni-input">{{ pickerLabel || '暂无数据' }}</view>
          </picker>
        </view>
      </view>

      <view class="infoCard grace-box-shadow">
        <view class="cardTitle">
          <view class="titlePoint"></view>
          <view class="titleText">
            <view>变化趋势图</view>
            <view style="display: flex;justify-content: space-between;">
              <!-- <view style="margin-right: 1em;">
                检查指标
              </view> -->
              <!-- <picker mode=selector @change="bindPickerChange" range-key="label" :value="pickerIndex"
                :range="pickerArray">
                <view class="uni-input">{{ pickerLabel || '暂无数据' }}</view>
              </picker> -->
            </view>
          </view>

        </view>

        <view class="cardItem">
          <view class="container">
            <!-- echarts板块 -->
            <view style="width: 100%;height: 100%;">
              <!-- #ifdef MP-WEIXIN -->
              <uni-ec-canvas class="uni-ec-canvas" id="line-chart" canvas-id="multi-charts-line" :ec="ec"
                ref="canvas"></uni-ec-canvas>
              <!-- #endif -->
              <!-- #ifdef H5 -->
              <view ref="chartContainer" id="chartContainer" class="chartContainer"></view>
              <!-- #endif -->
            </view>
          </view>
        </view>

      </view>

    </view>
  </gracePage>
</template>

<script>
import healthApi from '@/api/health.js'
import recordApi from '@/api/record.js'

// #ifdef MP-WEIXIN
import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas';
import * as echarts from '@/components/uni-ec-canvas/echarts.js';
// #endif
// #ifdef H5
import * as echarts from 'echarts'  // 引入ECharts库
import moment from 'moment'
// #endif
const option = {
  dataset: {
    source: []
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: [{
    type: 'category',
    axisLine: {
      lineStyle: {
        color: '#999'
      }
    },
    boundaryGap: false,
  }],
  yAxis: [{
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#DCDCDC'
      }
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#A4A4A4'
      },
    },
    axisTick: {
      show: false
    },
    nameTextStyle: {
      color: '#A4A4A4'
    },
    splitArea: {
      show: false
    }
  }],
  series: [{
    name: '指标',
    type: 'line',
    encode: { x: 'year', y: 'result' },
    lineStyle: {
      normal: {
        width: 8,
        color: {
          type: 'linear',
          colorStops: [{
            offset: 0,
            color: '#A9F387' // 0% 处的颜色
          }, {
            offset: 1,
            color: '#48D8BF' // 100% 处的颜色
          }],
          globalCoord: false // 缺省为 false
        },
        shadowColor: 'rgba(72,216,191, 0.3)',
        shadowBlur: 10,
        shadowOffsetY: 20
      }
    },
    itemStyle: {
      normal: {
        color: '#fff',
        borderColor: '#A9F387',
        // color: '#A9F387'
        // borderColor: '#fff',
        borderWidth: 10,
      },
      // emphasis: {
      //   color: '#A9F387',
      // }
    },
    label: {
      normal: {
        show: true,
        position: 'top',
        color: '#A9F387'
      }
    },
    smooth: true
  }]
};

// 空数据状态的配置
const emptyOption = {
  ...option,
  series: [{
    name: '暂无数据',
    type: 'line',
    data: [],
    label: {
      show: false
    }
  }],
  xAxis: [{
    type: 'category',
    data: [],
    axisLine: {
      lineStyle: {
        color: '#999'
      }
    },
    boundaryGap: false,
  }],
  yAxis: [{
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#DCDCDC'
      }
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#A4A4A4'
      },
    },
    axisTick: {
      show: false
    },
    nameTextStyle: {
      color: '#A4A4A4'
    },
    splitArea: {
      show: false
    }
  }]
};

export default {
  data() {
    return {
      userInfo: {
        gender: "0", // 0: 男 1: 女
        name: "",
        age: "",
        department: "",
        workType: "",
        workYears: "",
      },
      ec: {
        option: {}
      },
      pickerArray: [
        // 根据检查项目动态生成
      ],
      pickerLabel: '',
      pickerIndex: 0,
      chartData: [],
      chartInstance: null,
      checkItems: [], // 存储所有检查项
      historyData: [], // 存储历史数据
      isEmpty: false, // 添加空数据状态标记

      examTypeOption: [
        { value: '', label: '全部' },
        { value: '1', label: '岗前' },
        { value: '2', label: '在岗' },
        { value: '3', label: '离岗' },
      ],
      examTypeLabel: '全部',
      examTypeIndex: 0,
    };
  },

  async mounted() {
    try {
      // 获取基本信息
      await this.getBasicInfo();

      const res = await healthApi.reportList({ examType: this.examTypeOption[this.examTypeIndex].value });

      if (!res.data || res.data.length === 0) {
        this.isEmpty = true;
        uni.showToast({
          title: '暂无体检记录',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 处理数据,提取所有检查项和历史数据
      this.processCheckItems(res.data);

      // 更新用户信息
      // const latestReport = res.data[0];
      // this.userInfo = {
      //   name: latestReport.name,
      //   gender: latestReport.gender,
      //   age: this.calculateAge(latestReport.birthDate)
      // };

      // #ifdef MP-WEIXIN
      this.$refs.canvas.init(this.initChart);
      // #endif

      // #ifdef H5
      this.initChartH5();
      // #endif

      this.getData();
    } catch (error) {
      console.error('数据获取失败:', error);
      this.isEmpty = true;
      uni.showToast({
        title: '数据获取失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  components: {
    // #ifdef MP-WEIXIN
    uniEcCanvas,
    // #endif
  },

  methods: {

    // 获取基本信息
    async getBasicInfo() {
      try {
        const res = await recordApi.getEHealthRecordBaseInfo();
        if (res.data) {
          // 更新用户信息
          this.userInfo = {
            ...this.userInfo,
            name: res.data.name || '',
            gender: res.data.gender || '0',
            age: res.data.age || '',
            department: res.data.department || '',
            workType: res.data.workType || '',
            workYears: res.data.workYears || '',
          };
        }
      } catch (error) {
        console.error('获取基本信息失败:', error);
      }
    },


    // 计算年龄
    calculateAge(birthDate) {
      const birth = new Date(birthDate);
      const today = new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      return age.toString();
    },

    // 处理检查项数据
    processCheckItems(reports) {
      const allItems = [];
      const historyMap = new Map(); // 用于存储每个检查项的历史数据

      // 按时间排序
      reports.sort((a, b) => new Date(a.registerTime) - new Date(b.registerTime));

      reports.forEach(report => {
        const reportDate = moment(report.registerTime).format('YYYY-MM-DD');

        report.checkDepartments.forEach(dept => {
          dept.checkProjects.forEach(project => {
            project.checkItems.forEach(item => {
              const itemKey = item.itemId._id;
              if (!historyMap.has(itemKey)) {
                historyMap.set(itemKey, {
                  label: item.itemId.projectName,
                  value: item.itemId._id,
                  unit: item.itemId.msrunt,
                  history: []
                });
              }

              // 添加历史数据
              historyMap.get(itemKey).history.push({
                year: reportDate,
                result: parseFloat(item.result)
              });

              allItems.push({
                label: item.itemId.projectName,
                value: item.itemId._id,
                unit: item.itemId.msrunt,
                result: item.result
              });
            });
          });
        });
      });

      // 去重并保存历史数据
      this.checkItems = Array.from(new Set(allItems.map(item => JSON.stringify(item))))
        .map(item => JSON.parse(item));

      // 更新picker选项
      this.pickerArray = this.checkItems.map(item => ({
        value: item.value,
        label: item.label
      }));

      this.pickerLabel = this.pickerArray[0]?.label || '';

      // 保存历史数据
      this.historyData = Array.from(historyMap.values());
    },

    async updateData() {
      this.clearData()
      const res = await healthApi.reportList({ examType: this.examTypeOption[this.examTypeIndex].value });

      if (!res.data || res.data.length === 0) {
        this.isEmpty = true;
        uni.showToast({
          title: '暂无体检记录',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      // 处理数据,提取所有检查项和历史数据
      this.processCheckItems(res.data);
    },

    // 获取数据
    getData() {
      if (!this.checkItems.length) {
        this.isEmpty = true;
        option.dataset.source = [];
        option.series[0].name = `暂无数据`;
        // #ifdef MP-WEIXIN
        this.$refs.canvas.init(this.initChart);
        // #endif
        // #ifdef H5
        this.chartInstance.setOption(option);
        // #endif
        return;
      }

      const selectedItem = this.checkItems[this.pickerIndex];
      const historyItem = this.historyData.find(item => item.value === selectedItem.value);

      if (historyItem && historyItem.history.length > 0) {
        this.isEmpty = false;
        // 按年份排序历史数据
        historyItem.history.sort((a, b) => a.year - b.year);

        option.dataset.source = historyItem.history;
        option.series[0].name = `${selectedItem.label}(${selectedItem.unit})`;

        // #ifdef MP-WEIXIN
        this.$refs.canvas.init(this.initChart);
        // #endif

        // #ifdef H5
        this.chartInstance.setOption(option);
        // #endif
      } else {
        this.isEmpty = true;
        // #ifdef MP-WEIXIN
        this.$refs.canvas.init(this.initChart);
        // #endif

        // #ifdef H5
        this.chartInstance.setOption(emptyOption);
        // #endif
      }
    },
    // 指标切换
    bindPickerChange: function (e) {
      this.pickerIndex = e.detail.value
      this.pickerLabel = this.pickerArray[this.pickerIndex].label;
      option.series[0].name = this.pickerLabel;
      this.getData();
    },

    // 检查类型切换
    async examTypeChange(e) {
      this.examTypeIndex = e.detail.value
      this.examTypeLabel = this.examTypeOption[this.examTypeIndex].label
      await this.updateData()
      this.getData();
    },

    // 清空残留数据
    clearData() {
      this.checkItems = []
      this.pickerArray = []
      this.pickerLabel = ''
      this.historyData = []
      this.isEmpty = true;
    },

    // 小程序初始化图表 
    initChart(canvas, width, height, canvasDpr) {
      let chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: canvasDpr
      });
      canvas.setChart(chart);
      chart.setOption(this.isEmpty ? emptyOption : option);
      return chart;
    },
    // H5
    initChartH5() {
      const dom = document.getElementById('chartContainer');
      this.chartInstance = echarts.init(dom);
      this.chartInstance.setOption(this.isEmpty ? emptyOption : option);
    }

  }
};
</script>

<style scoped lang="scss">
.chartContainer {
  width: 100%;
  height: 100%;
}

.infoCard {
  padding: 15px;
  border-radius: 10px;
  margin-top: 12.5px;
  position: relative;

  .cardTitle {
    margin-bottom: 16px;

    .titleText {
      font-weight: 600;
      font-size: 28rpx;
      line-height: 28rpx;
      letter-spacing: 0px;
      color: #555555;

      display: flex;
      justify-content: space-between;
    }

    .titlePoint {
      position: absolute;
      left: 0px;
      top: 13px;
      width: 6px;
      height: 20px;
      border-radius: 3px 3px 0px 3px;
      opacity: 1;

      background: #3E73FE;
    }
  }

  .cardItem {
    display: flex;
    margin-bottom: 12px;
    justify-content: space-between;

    font-size: 24rpx;
    font-weight: normal;
    line-height: 28rpx;
    letter-spacing: 0px;
    color: #555555;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 64vh;
  width: 100%;
}


picker {
  .uni-input {
    color: #A8ABB2;
  }

  .uni-input::after {
    content: '>';
    display: inline-block;
    transform: rotate(90deg) scaleY(1.5) translate(0, -0.25em);
    margin-right: 1em;
  }
}
</style>
