### 劳动者APP

# 劳动者APP 版本 更新日志
 ## 1.0.249更新日志  
- fix:抓取劳动者生命周期职业病病人分类
 ## 1.0.247更新日志  
- fix:疑似职业病申请功能优化，不允许重复发起诊断申请
 ## 1.0.246更新日志  
- fix:
  - 基本信息打开后空白
 ## 1.0.245更新日志  
- fix:
  - 新增鉴定申请改为从鉴定记录和诊断记录抓取数据；鉴定记录新增下载受理、不予受理通知书功能
 ## 1.0.243更新日志  
- feat:
  - 过敏史、既往史、家族史编辑
  - 职业史增加危害因素
  - 诊断医疗记录的诊断列表添加诊断证明书下载

 ## 1.0.239更新日志  
- fix:
  - 修复下载鉴定申请书乱码的问题;鉴定过程人数不能为0的校验；鉴定结果添加鉴定机构字段

 ## 1.0.238更新日志  
- fix:
  - 体检预警 

 ## 1.0.237更新日志  
- Feat: 
  - 体检报告授权查询
- fix:
  - 体检预警 

 ## 1.0.236更新日志  
- Fix: 
  - 诊断和鉴定过程新增保存和删除职业史功能;诊断、鉴定结果添加不受理原因字段
 ## 1.0.235更新日志  
- Feat: 
  - 预约记录 撤回预约
  - 体检机构查询 根据劳动者绑定企业辖区筛选、展示内容调整
  - 职业健康统计分析 添加检查类型筛选
  - 电子档案假数据清空
  
 ## 1.0.234更新日志  
- Fix: 
  - 修复职业病鉴定相关补充材料报送，新增下载职业病鉴定报告功能
 ## 1.0.232更新日志  
- Fix: 
  - 修复业务咨询图片回显的问题
 ## 1.0.231更新日志  
- Fix: 
  - 修复职业病诊断相关补充材料报送，新增下载职业病诊断报告功能

 ## 1.0.224更新日志  
- Feat: 
  - 更新康复记录接口；新增康复记录下载接口 

## 1.0.222 更新日志
- feat
  - 电子健康档案：个人信息 职业史 怎删改查
  - 电子健康档案：个人信息 职业病发生情况
  - 电子健康档案：诊断医疗记录 数据获取调整
  - 电子健康档案：工作场所历次危害因素检测 
  - fix: 体检报告

## 1.0.222 更新日志
- feat
  - 电子健康档案：个人信息 职业史 怎删改查
  - 电子健康档案：个人信息 职业病发生情况
  - 电子健康档案：诊断医疗记录 数据获取调整
  - 电子健康档案：工作场所历次危害因素检测 
  - fix: 体检报告

## 1.0.222 更新日志
- feat
  - 工伤认定

## 1.0.221 更新日志
- fix
  - 修复诊断、鉴定申请时统一社会信用代码校验的问题；修复诊断附件上传功能

## 1.0.220 更新日志
- feat
  - 修复劳动者pc端时间选择器适配

## 1.0.219 更新日志
- feat
  - 劳动者pc端适配
## 1.0.217 更新日志
- feat
  - 对接全生命周期接口；新增接口
## 1.0.216 更新日志
- feat
  - 劳动者全生命周期静态页面

## 1.0.215 更新日志
- feat
  - 疑似职业病上报对接接口;诊断记录添加首次鉴定功能；鉴定记录添加师级鉴定功能

## 1.0.213、214 更新日志
- feat
  - 远程会诊

## 1.0.211 更新日志
- feat
  - 劳动者申诉
  - 劳动者电子档案诊断鉴定数据展示
  
## 1.0.196 更新日志
- fix
  - 体检预约完善
  
## 1.0.198、200 更新日志
- fix 
  - 电子健康档案 个人基本信息编辑
 
## 1.0.196 更新日志
- fix
  - 体检报告性别
  
## 1.0.195 更新日志

- fix
  - 电子档案基本信息 生育史、月经史性别判断
  - 检查预警模块入口调整

## 1.0.194 更新日志

- fix:
  - 首页鉴定预约改为鉴定申请

## 1.0.191 更新日志

- fix:
  - 诊断、鉴定列表新增查询条件

## 1.0.190 更新日志

- feat:
  - 体检提醒
  - 检查统计模块放到首页
  - 检查报告（体检结果）
  - 体检统计 入口添加 趋势图优化
  - 体检预警调整

## 1.0.187 更新日志

- fix:
  - 管理员培训和价格为0的培训不走付费逻辑

## 1.0.185 更新日志

- feat:
  - 电子档案基本信息、授权管理
- fix:
  - 首页入口模块图标重复

## 1.0.185 更新日志

- fix:
  - 首页入口模块图标重复

## 1.0.184 更新日志

- feat:
  - 对接职业病鉴定列表接口

## 1.0.175 更新日志

- feat:
  - 对接职业病诊断申请、查看申请列表接口

## 1.0.169 更新日志

- fix:
  - 在新疆分支隐藏人脸识别功能

## 1.0.167 更新日志

- fix:
  - 首页功能入口模块接口联调

## 1.0.166 更新日志

- fix:
  - 首页功能入口模块静态

## 1.0.165 更新日志

- fix:
  - 诊断、鉴定机构地图暂时注释掉

## 1.0.161、163、164 更新日志

- feat:
  - 劳动者端新增职业病鉴定、职业病诊断页面
  - 修复诊断、鉴定机构地图无法查看的问题

## 1.0.156、162 更新日志

- feat:
  - 职业人群电子健康档案

## 1.0.154_1 更新日志

- feat:
  - 劳动者端职业健康体检预约

## 1.0.154 更新日志

- feat:
  - 劳动者端职业健康体检预约

## 1.0.146 更新日志

- feat:
  - 个人指标趋势查看

## 1.0.146 更新日志

- fix：
  - 预警内容页面异常

## 1.0.142 更新日志

- fix：
  - 调整设备检修的功能

## 1.0.137 更新日志

- feat：
  - 调整体检清单功能

## 1.0.135 更新日志

- fix：
  - 【山西焦煤】【体检】修改相应检查项目查询保存部分逻辑
  - 小程序环境样式问题
  - 添加加项页面的联动滚动效果

## 1.0.134 更新日志

- fix：
  - 修复小程序环境下点击页面报错

## 1.0.133 更新日志

- fix：
  - H5环境体检套餐页面立即预约按钮功能异常

## 1.0.132 更新日志

- fix：
  - 调整打包参数

## 1.0.129 更新日志

- 功能新增：
  - 修改部分引入方式，兼容h5编译，更改获取环境配置服务地址方式,添加环境配置，add docker build

## 1.0.130 更新日志

- fix：
  - 焦煤体检预约总价格算法

## 1.0.127 更新日志

- 功能更新：
  - 体检预约

## 1.0.125 更新日志

- 功能更新：
  - 体检预约

## 1.0.125 更新日志

- 功能更新：
  - 劳动者小程序查看体检报告详情

## 1.0.124 更新日志

- 功能更新：
  - 适配oss

## 1.0.123 更新日志

- 功能更新：
  - 修改职业史时间选择器

## 1.0.119 更新日志

- 功能更新：
  - 新增违章信息推送
  - 增加user分包配置
  - 配置不同环境域名

## 1.0.115 更新日志

- 功能更新：
  - 增加小程序分享功能

## 1.0.114 更新日志

- 功能更新：
  - 增加体检医院确认，上传引导单
  - 增加培训分包配置

## 1.0.109 更新日志

- 功能更新：
  - 增加问卷调查
  - 劳动者职业史
  - 增加体检预约
- 功能修复:
  - 修复培训签字时页面滑动bug

**所需组件：**

* 动画组件名：``cmd-transition`` 代码块： cmdTransition
* 头像组件名：``cmd-avatar`` 代码块： cmdAvatar
* 底部导航栏组件名：``cmd-bottom-nav`` 代码块： cmdBottomNav
* 列表单元组件名：``cmd-cell-item`` 代码块： cmdCellItem
* icon图标组件名：``cmd-icon`` 代码块： cmdIcon
* 输入框组件名：``cmd-input`` 代码块： cmdInput
* 导航栏组件名：``cmd-nav-bar`` 代码块： cmdNavBar
* 导航栏内容页组件名：``cmd-page-body`` 代码块： cmdPageBody

使用自定义导航栏时

```json
{
  // 全局bar样式中去除，顶部导航条、滚动条、原生回弹阴影
  "globalStyle": {
  	// 不显示工具栏toolbar
  	"navigationStyle": "custom",
  	"app-plus": {
  		// 不显示滚动条
  		"scrollIndicator": "none",
  		// 页面回弹效果
  		"bounce": "none"
  	}
  }
}
```

全局样式使用
如果 ``cmd``组件在使用中发生大小异常，可以在组件内加上view标签通用样式

```css
/* 全局样式 */
@import url("./common/uni/uni.css");

/* 通用 */
view {
	font-size: 28upx;
	line-height: 1.8;
}
```
