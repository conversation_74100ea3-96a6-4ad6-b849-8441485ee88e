<template>
	<view class="institution">
		<uni-nav-bar leftWidth="180rpx" :fixed="true" background-color="#007AFF" @clickLeft="back" border={false}>
			<block slot="left">
				<view class="nav-left">
					<image src="../../static/leftArrow.svg" mode=""></image>
					预约记录
					<!-- 提交过的预约记录 -->
				</view>
			</block>
		</uni-nav-bar>

		<view class="record-body">
			<view class="record-section">
				<view class="card-section">
					<view class="card" v-for="item in recordList" :key="item._id">
						<view class="title">
							<text></text>
							{{ item.physicalExamOrgName }}
						</view>
						<view class="name">
							<text class="label">预约时间</text>
							<text class="des">{{ item.reservationDate }}</text>
						</view>
						<view class="name">
							<text class="label">体检类型</text>
							<text class="des" v-if="item.examType === 0">离岗</text>
							<text class="des" v-if="item.examType === 1">岗前</text>
							<text class="des" v-if="item.examType === 2">在岗</text>
						</view>
						<view class="name">
							<text class="label">预约状态</text>
							<text class="des" v-if="item.reservationStatu === 0">未预约</text>
							<text class="des" v-if="item.reservationStatu === 1">已预约(待审核)</text>
							<text class="des" v-if="item.reservationStatu === 2">审核通过</text>
							<text class="des" v-if="item.reservationStatu === 3">已拒绝</text>
						</view>
						<view class="name">
							<text class="label">登记状态</text>
							<text class="des" v-if="!item.registerStatus && item.registerStatus != 0">未登记</text>
							<text class="des" v-if="item.registerStatus === 0">未登记</text>
							<text class="des" v-if="item.registerStatus === 1">已登记</text>
							<text class="des" v-if="item.registerStatus === 2">已总结</text>
						</view>
						<view class="name" v-if="item.refuseReason">
							<text class="label">拒绝原因</text>
							<text class="des">{{ item.refuseReason }}</text>
						</view>
						<view class="operaction">
							<!-- 未预约状态 -->
							<!-- <view class="edit-btn" v-if="item.reservationStatu === 0" @click="handleEdit(item)">预约
							</view> -->
							<!-- 已拒绝状态 -->
							<view class="edit-btn" v-if="canEdit(item)" @click="handleEdit(item)">重新预约
							</view>
							<!-- 已预约待审核 且 尚未结束 -->
							<view class="edit-btn" v-if="canCancel(item)" @click="cancel(item)">撤回</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-popup :show="showEdit" mode="bottom" :closeable="true" @close="showEdit = false">
			<view style="width: 100%;height: 50vh;padding: 0 10px;box-sizing: border-box;">
				<u--form labelPosition="left" labelWidth='100' ref="uForm" :model="form" style="padding-top: 50px;">
					<u-form-item label="体检日期:" prop="reservationDate" borderBottom @click="showCalendar = true">
						<u--input disabledColor="#ffffff" placeholder="请选择体检日期" v-model="form.reservationDate"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</u--form>
				<u-button type="primary" text="确定" @click="handleUpdateSave"
					:disabled="!form.reservationDate || form.reservationDate === '尚未预约'"></u-button>
			</view>
		</u-popup>
		<u-calendar :show="showCalendar" mode="single" :minDate="minDate" :maxDate="maxDate" @confirm="confirm"
			@close="showCalendar = false"></u-calendar>
	</view>
</template>

<script>
import { mapGetters } from 'vuex'
import healthApi from '@/api/health.js'
import userApi from '@/api/user.js' //导入接口

import moment from "moment";
export default {
	data() {
		return {
			recordList: [],
			showEdit: false,
			showCalendar: false,
			form: {
				id: "",
				reservationDate: '',
				examType: -1
			},
			minDate: '',
			maxDate: ''
		}
	},
	onLoad() {
		this.getList()
	},
	computed: {
		...mapGetters(['userInfo', 'hasLogin']),
	},
	methods: {
		back() {
			uni.navigateBack()
		},
		async getList() {
			const res = await userApi.getHcAppointmentList({
			})
			const temp = res.data.filter(item => {  // 过滤掉未预约的记录
				return item.reservationStatu !== 0
			}).reverse()
			this.recordList = temp.map(item => {
				item.reservationDate = item.reservationDate ? moment(item.reservationDate).format("YYYY-MM-DD") : '尚未预约'
				return item
			})
		},
		handleEdit(item) {
			this.showEdit = true
			this.form.id = item._id
			this.form.examType = item.examType
			this.form.reservationDate = item.reservationDate
			this.minDate = moment(item.examStartDate).format("YYYY-MM-DD")
			this.maxDate = moment(item.examEndDate).format("YYYY-MM-DD")
		},
		cancel(item) {
			uni.showModal({
				title: '提示',
				content: '您确定要撤回预约吗？',
				success: async (res) => {
					if (res.confirm) {
						const result = await healthApi.cancelReservation({
							id: item._id
						})
						if (result.status === 200) {
							uni.showToast({
								title: "撤回成功",
								duration: 3000
							})
							this.getList()
						} else {
							uni.showToast({
								title: result.msg,
								icon: "none",
								duration: 3000
							})
						}
					}
				}
			})
		},

		canCancel(item) {
			// 预约状态为已预约 且 尚未结束 且 登记状态为未登记（不存在或者等于0）
			return item.reservationStatu === 1 && moment(item.examEndDate).isAfter(moment())
		},
		canEdit(item) {
			// 预约状态为已拒绝 且 尚未结束 且 登记状态为未登记（不存在或者等于0）
			return item.reservationStatu === 3 && moment(item.examEndDate).isAfter(moment())
		}
	}
}
</script>

<style lang="scss" scoped>
.institution {
	width: 100%;
	background-color: #f6f6f6;
	padding: 0 15px;
	box-sizing: border-box;
}

.nav-left {
	display: flex;
	align-items: center;
	width: auto;
	color: #fff;

	image {
		width: 40rpx;
		height: 40rpx;
	}
}

.record-body {
	.record-section {
		margin-top: 15px;

		.card-section {
			.card {
				width: 100%;
				border-radius: 5px;
				background: #FFFFFF;
				margin-top: 12px;
				padding: 14px;
				box-sizing: border-box;
				margin-bottom: 14px;

				.title {
					font-family: PingFangSC;
					font-size: 14px;
					color: #555555;
					display: flex;
					align-items: center;
					margin-left: -14px;
					margin-bottom: 15px;

					text {
						display: inline-block;
						width: 6px;
						height: 20px;
						border-radius: 3px 3px 0px 3px;
						background: #FE3E3E;
						margin-right: 12px;
					}
				}

				.name {
					font-size: 14px;
					margin-bottom: 6px;

					.label {
						margin-right: 24px;
						color: #000;
					}

					.des {
						color: #555555;
					}
				}

				.operaction {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					margin-top: 24px;

					view {
						text-align: center;
						line-height: 33px;
						width: 81px;
						height: 33px;
						border-radius: 3px;
					}

					.cancel-btn {
						color: #3E73FE;
						border: 1px solid #3E73FE;
						text-align: center;
						line-height: 33px;
					}

					.edit-btn {
						background: #3E73FE;
						border: 1px solid #3E73FE;
						color: #fff;
						margin-left: 12px;
					}
				}
			}
		}
	}
}
</style>